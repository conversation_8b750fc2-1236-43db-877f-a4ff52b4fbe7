
#pragma once

#include "reflection/meta/user_property.hpp"
#include "reflection/objects/make_user_object.hpp"

namespace gbf {
namespace reflection {
namespace detail {

template <typename A>
class UserPropertyImpl : public UserProperty {
 public:
  UserPropertyImpl(MetaClass* owner, IdRef name, A&& accessor);

 protected:
  bool IsReadable() const final;
  bool IsWritable() const final;

  Value GetValue(const UserObject& object) const final;
  void SetValue(const UserObject& object, const Value& value) const final;

  virtual void* ContainerPtrToValuePtrInternal(const UserObject& object, int ArrayIndex = 0) const override;

 private:
  A accessor_;  // Object used to access the actual C++ property
};

}  // namespace detail
}  // namespace reflection
}  // namespace gbf

#include "reflection/builder/property/userpropertyimpl.inl"
