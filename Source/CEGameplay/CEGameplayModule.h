#pragma once
#include "core/modules/imodule.h"
#include "CEGameplayAPI.h"
namespace cross
{
    static const char kModuleCEGamePlay[] = "CEGameplay";
    class CEGameplayModule : public gbf::IModule
    {
    public:
        CEGameplayModule();
        // method from IModule
        gbf::ModuleCallReturnStatus Init() override
        {
            return gbf::ModuleCallReturnStatus::Succeed;
        }
        gbf::ModuleCallReturnStatus Start() override 
        {
            return gbf::ModuleCallReturnStatus::Succeed;
        }
        gbf::ModuleCallReturnStatus Update() override
        {
            return gbf::ModuleCallReturnStatus::Succeed;
        }
        gbf::ModuleCallReturnStatus Stop() override
        {
            return gbf::ModuleCallReturnStatus::Succeed;
        }
        gbf::ModuleCallReturnStatus Release() override
        {
            return gbf::ModuleCallReturnStatus::Succeed;
        }
        void Free() override
        {
            delete this;
        }
    };
}   // namespace cross
