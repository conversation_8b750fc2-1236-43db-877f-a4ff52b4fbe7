#pragma once
#include "ParticleAffectorInfo.h"
#include "CrossBase/Math/Color.h"
#include "../MaterialParameterCollection.h"
namespace cross
{

#define DESERIALIZE_MODULE(node, context, m, name)                                                                                                                                                                                             \
    if (node.<PERSON><PERSON><PERSON><PERSON>(name))                                                                                                                                                                                                                  \
    {                                                                                                                                                                                                                                          \
        m.Deserialize(node[name], context);                                                                                                                                                                                                    \
    }

enum class CEMeta(Editor) SimulationType
{
    CPU,
    GPU,
    NGPU
};

enum class CEMeta(Editor) LoopBehavior : UInt32
{
    Once,
    Infinite,
    Multiple
};

enum class CEMeta(Editor) LocationShapeType
{
    Box,
    Sphere,
    Cone,
    Circle,
    MeshSurface
};

enum class CEMeta(Editor) EmitFromType
{
    Volume,
    Shell,
    Edge,
    HemiSphereVolume,
    HemiSphereShell,
    ConeVolume,
    ConeVolumeShell
};

enum class CEMeta(Editor) EmitRadiusMode
{
    Random,
    Loop,
    PingPong
};

enum class CEMeta(Editor) EmitDistributionType
{
    Random,
    Uniform,
    Direct
};

enum class CEMeta(Editor) ParticleRendererType
{
    Unset = -1,
    Sprite,
    Mesh
};

enum class CEMeta(Editor) RenderAlignment
{
    Unaligned,
    Velocity,
    Custom
};
enum class CEMeta(Editor) RenderFacing
{
    Plane,
    Position,
    Custom, 
    Blend
};
enum class CEMeta(Editor) MeshSurfaceEmitType
{
    Triangle,
    Vertex,
    Edge
};

struct EmitterStateInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether Auto Random Seed"))
    bool AutoRandomSeed{true};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "ParticleEmitter's Random Seed"))
    UInt32 RandomSeed{0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Simulate In LocalSpace"))
    bool LocalSpace{true};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Loop Behavior"))
    LoopBehavior LoopBehavior{LoopBehavior::Infinite};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Loop Duration"))
    float LoopDuration{5.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Loop Count"))
    UInt32 LoopCount{0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Emitter Spawn After Delay Time"))
    MinMaxCurve StartDelay{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = ""))
    bool ApplyLighting{false};

    CE_Serialize_DeserializeEditor;
};

struct SpawnBurstInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Time Point When Spawn Burst"))
    float BurstTime{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle Burst Count"))
    MinMaxCurve  BurstCount{0.0f};

    UInt32 AccumulateCycle{0u};

    CE_Serialize_DeserializeEditor;
};

struct ParticleSpawnInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Max Particle's Count Allowed"))
    SInt32 ParticleCountLimit{10000};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Spawn Count Per Second"))
    MinMaxCurve SpawnRateCount{20.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Emitter's Burst Particle Array"))
    std::vector<SpawnBurstInfo> Bursts;

    CE_Serialize_DeserializeEditor;
};

struct ParticleInitInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Stard Speed"))
    MinMaxCurve StartSpeed;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "If Origin Velocity is not enabled, a default vector is used"))
    bool EnableOriginVelocity = false;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Origin Velocity"))
    DynamicVector3 OriginVelocity;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Lifetime"))
    MinMaxCurve Lifetime;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Total Size Scaler"))
    MinMaxCurve Scaler{1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "If True, use XYZ separate value of SpriteSize or MeshSize add to Scaler."))
    bool SeparateAxis{true};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Size X"))
    MinMaxCurve SpriteSizeX{1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Size Y"))
    MinMaxCurve SpriteSizeY{1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Sprite Rotation Radians"))
    MinMaxCurve SpriteRotation{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Size X"))
    MinMaxCurve MeshSizeX;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Size Y"))
    MinMaxCurve MeshSizeY;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Size Z"))
    MinMaxCurve MeshSizeZ;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Rotation X Radians"))
    MinMaxCurve MeshRotationX{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Rotation Y Radians"))
    MinMaxCurve MeshRotationY{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Origin Rotation Z Radians"))
    MinMaxCurve MeshRotationZ{0.0f};

    CEMeta(Serialize, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Particle's UV Scale"))
    Float4 UVScale{0.0f, 0.0f, 1.0f, 1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxGradient", ToolTips = "Particle's Color"))
    MinMaxGradient Color;

    CE_Serialize_DeserializeEditor;
};

struct LocationMoveState
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Position curve from begin to end"))
    DynamicVector3 PositionCurve;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Box curve from begin to end"))
    DynamicVector3 BoxCurve;

    CE_Serialize_DeserializeEditor;
};

struct LocationShapeInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Position Offset"))
    Float3 PositionOffset;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Scale Position Offset"))
    Float3 PositionOffsetScalar{1.0f, 1.0f, 1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Emitter's Shape"))
    LocationShapeType ShapeType{LocationShapeType::Box};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Emit From Type"))
    EmitFromType EmitFrom{EmitFromType::Volume};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Emit From Type"))
    MeshSurfaceEmitType EmitType{MeshSurfaceEmitType::Triangle};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Box Shape"))
    Float3 Box{10.0f, 10.0f, 10.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Sphere&Circle Radius"))
    float Radius{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Cone's Angle"))
    float Angle{30.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The angular portion of a full circle that forms the emitter's shape"))
    float Arc{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Cone Length"))
    float Length{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Cone's inner angle"))
    float InnerAngle{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Cone's axis"))
    Float3 Axis{0.0f, 1.0f, 0.0f};

    //take affect only in GPU circle currently
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = ""))
    EmitDistributionType DistributionType{EmitDistributionType::Random};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "UDistribution"))
    float UDistribution{0.0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "DiskCoverage"))
    float DiskCoverage{1.0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "UniformSpiralAmount"))
    float UniformSpiralAmount{1.0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "UniformSpiralFalloff"))
    float UniformSpiralFalloff{1.0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Mesh As Emit From", FileTypeDescriptor = "MeshAsset#nda", ObjectClassID1 = ClassIDType.CLASS_MeshAssetDataResource))
    std::string ModelPath = "EngineResource/Model/Cube.nda";

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Which LOD Selected"))
    UInt32 LodIndex{0u};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Move State"))
    LocationMoveState MoveState;

    CE_Serialize_DeserializeEditor;
};

struct GPUComputeSimInfo
{
    CEMeta(Serialize, Reflect)
    UInt32 TotalFloatComponents = 0;

    CEMeta(Serialize, Reflect)
    UInt32 TotalInt32Components = 0;

    CEMeta(Serialize, Reflect)
    UInt32 TotalHalfComponents = 0;

    CEMeta(Serialize, Reflect)
    SInt32 ThreadGroupSizeX = 64;

    CEMeta(Serialize, Reflect)
    SInt32 ThreadGroupSizeY = 1;

    CEMeta(Serialize, Reflect)
    SInt32 ThreadGroupSizeZ = 1;

    CEMeta(Serialize, Reflect)
    bool HasInterpolationParameters = false;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Simulation compute shader", FileTypeDescriptor = "Particle Simulation#nda", ObjectClassID1 = ClassIDType.CLASS_ComputeShader))
    std::string SimulationPath{"PipelineResource/FFSRP/Shader/Features/VFX/ParticleGPUSimulation.compute.nda"};

    CEMeta(Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsCode", ToolTips = "Uniforms"))
    std::string SimulationParameters = "";

    CEMeta(Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsCode", ToolTips = "void OverrideDashboard()"))
    std::string OverrideDashboard = "";

    CEMeta(Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsCode", ToolTips = "void OverrideInitStage()"))
    std::string OverrideInitStage = "";

    CEMeta(Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsCode", ToolTips = "void OverrideUpdateStage()"))
    std::string OverrideUpdateStage = "";

    CE_Serialize_DeserializeEditor;
};

struct DynamicMaterialProperty
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Property's Name"))
    std::string Name{""};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "MinMaxCurve", ToolTips = "Value Of Current Property (4 At Most)"))
    std::vector<MinMaxCurve> Properties;

    CE_Serialize_DeserializeEditor;
};

struct ParticleCustomProperty
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Property's Name"))
    std::string Name{ "" };

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "MinMaxCurve", ToolTips = "Value Of Current Property (4 At Most)"))
    std::vector<MinMaxCurve> Properties;

    CE_Serialize_DeserializeEditor;
};

struct ParticleMaterialSlot
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Material Slots", FileTypeDescriptor = "Material Assets#nda",
            ObjectClassID1 = ClassIDType.CLASS_Material, ObjectClassID2 = ClassIDType.CLASS_Fx))
    std::string MaterialPath = "PipelineResource/FFSRP/Material/ParticleSpriteDefault.nda";


    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Lod Index", bReadOnly = true, bHide = true))
    SInt32 LodIndex {0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "MeshPart Index", bReadOnly = true, bHide = true))
    SInt32 MeshPartIndex {0};

    CE_Serialize_DeserializeEditor;
};
    
struct  ParticleRendererInfo
{
    CEMeta(Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true))
    bool Modified{true};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enable Cull By Distance From Camera When Checkked"))
    bool EnableCameraDistanceCull{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Min Camera Distance"))
    float MinCameraDistance {0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Max Camera Distance"))
    float MaxCameraDistance {0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Dynamic Custom Properties For Emitter"))
    std::vector<DynamicMaterialProperty> EmitterCustomProperty;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Dynamic Custom Properties For Particle"))
    std::vector<ParticleCustomProperty> ParticleCustomProperty;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "MaterialParameterCollection", FileTypeDescriptor = "MaterialParameterCollection#mpc", ObjectClassID1 = ClassIDType.CLASS_MaterialParameterCollection))
    std::string EmitterMPCPath = "EngineResource/Material/DefaultEmpty.mpc";

    // CEMeta(Serialize, Editor, Reflect)
    // CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "MaterialParameterCollection", FileTypeDescriptor = "MaterialParameterCollection#mpc", ObjectClassID1 = ClassIDType.CLASS_MaterialParameterCollection))
    // std::string ParticleMPCPath = "PipelineResource/FFSRP/Material/ParticleDefault.mpc";

    CE_Serialize_DeserializeEditor;
};

struct  ParticleSpriteRendererInfo : ParticleRendererInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true))
    bool Enabled{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Defines how the particle alignment is affected by other parameters"))
    RenderAlignment Alignment{RenderAlignment::Unaligned};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Custum alignment direction"))
    Float3 AlignDirection;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Defines how the sprite particle orients itself relative to the camera"))
    RenderFacing Facing{RenderFacing::Position};
    
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Custum direction that particle face"))
    Float3 FacingDirection{1.0, 0.0, 0.0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The distance at which the FacingCameraDistanceBlend is fully FacingCamera"))
    float MinBlendDistance{0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The distance at which the FacingCameraDistanceBlend is fully FacingCameraPosition"))
    float MaxBlendDistance{0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enable large coordinate in large scenes"))
    bool EnableLargeCoordinate{true};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Default pivot for sprite particle"))
    Float2 DefaultPivot;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Particle's Material", FileTypeDescriptor = "Material#nda", ObjectClassID1 = ClassIDType.CLASS_Material, ObjectClassID2 = ClassIDType.CLASS_Fx))
    std::string MaterialPath = "PipelineResource/FFSRP/Material/ParticleSpriteDefault.nda";

    CE_Serialize_DeserializeEditor;
};

struct ParticleMeshRendererInfo : ParticleRendererInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true))
    bool Enabled{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The way that particle billboards face"))
    RenderAlignment Alignment{RenderAlignment::Velocity};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Direction that particle face for custom"))
    Float3 AlignDirection;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Particle's Mesh", FileTypeDescriptor = "MeshAsset#nda", ObjectClassID1 = ClassIDType.CLASS_MeshAssetDataResource))
    std::string ModelPath = "EngineResource/Model/Cube.nda";

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Particle's Material"))
    std::vector<ParticleMaterialSlot> MaterialSlots;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Particle's Material", FileTypeDescriptor = "Material#nda", ObjectClassID1 = ClassIDType.CLASS_Material, ObjectClassID2 = ClassIDType.CLASS_Fx))
    std::string MaterialPath = "PipelineResource/FFSRP/Material/ParticleMeshDefault.nda";

    CE_Serialize_DeserializeEditor;
};

struct ParticleEmitterInfo
{
    ParticleEmitterInfo() = default;

    ParticleEmitterInfo(const EmitterStateInfo& emitterState, const ParticleSpawnInfo& particleSpawn, const LocationShapeInfo& locationShape, const ParticleInitInfo& particleInit, const SizeScaleInfo& sizeScale,
                        const ColorScaleInfo& colorScale, const SpriteRotationRateInfo& spriteRotationRate, const VelocityInfo& velocity, const VectorNoiseInfo& vectorNoise, const GravityInfo& gravityForce, const ForceInfo& force,
                        const VortexForceInfo& vortexForce, const PointAttractionForceInfo& pointAttractionForce, const SolveForceVelocityInfo& solveForceVelocity, const SubUVInfo& subUV, const ParticleStateInfo& particleState,
                        const EventGeneratorInfo& eventGenerator, const ParticleSpriteRendererInfo& spriteRenderer, const ParticleMeshRendererInfo& meshRenderer, ParticleRendererType rendererType)
        : EmitterState(emitterState)
          , ParticleSpawn(particleSpawn)
          , LocationShape(locationShape)
          , ParticleInit(particleInit)
          , SizeScale(sizeScale)
          , ColorScale(colorScale)
          , SpriteRotationRate(spriteRotationRate)
          , Velocity(velocity)
          , VectorNoise(vectorNoise)
          , GravityForce(gravityForce)
          , Force(force)
          , VortexForce(vortexForce)
          , PointAttractionForce(pointAttractionForce)
          , SolveForceVelocity(solveForceVelocity)
          , SubUV(subUV)
          , ParticleState(particleState)
          , EventGenerator(eventGenerator)
          , SpriteRenderer(spriteRenderer)
          , MeshRenderer(meshRenderer)
          , RendererType(rendererType) {}

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Emitter Name"))
    std::string EmitterName{""};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Emitter State", Category = "EmitterUpdate"))
    EmitterStateInfo EmitterState;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Particle Spawn", Category = "EmitterUpdate"))
    ParticleSpawnInfo ParticleSpawn;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Location Shape", Category = "ParticleSpawn"))
    LocationShapeInfo LocationShape;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Particle Init", Category = "ParticleSpawn"))
    ParticleInitInfo ParticleInit;

    /******* Affector Modules *******/
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Particle Size's Scale", Category = "ParticleUpdate"))
    SizeScaleInfo SizeScale;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Particle Color's Scale", Category = "ParticleUpdate"))
    ColorScaleInfo ColorScale;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Sprite Particle Rotate's Rate", Category = "ParticleUpdate"))
    SpriteRotationRateInfo SpriteRotationRate;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Velocity Affector Over Lifetime", Category = "ParticleUpdate"))
    VelocityInfo Velocity;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Noise Affector Over Lifetime", Category = "ParticleUpdate"))
    VectorNoiseInfo VectorNoise;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Gravity Force", Category = "ParticleUpdate"))
    GravityInfo GravityForce;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Linear Force Over Lifetime", Category = "ParticleUpdate"))
    ForceInfo Force;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Vortex Force Over Lifetime", Category = "ParticleUpdate"))
    VortexForceInfo VortexForce;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Point Attraction Force Over Lifetime", Category = "ParticleUpdate"))
    PointAttractionForceInfo PointAttractionForce;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Solve Force and Velocity", Category = "ParticleUpdate"))
    SolveForceVelocityInfo SolveForceVelocity;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "TextureSheet Animation", Category = "ParticleUpdate"))
    SubUVInfo SubUV;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Particle State", Category = "ParticleUpdate"))
    ParticleStateInfo ParticleState;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Event Generator", Category = "ParticleUpdate"))
    EventGeneratorInfo EventGenerator;

    /******* Renderer Modules *******/
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(DisplayName = "SpriteRenderer", PropertyType = "Struct", ToolTips = "Particle Sprite Renderer", Category = "ParticleRenderer"))
    ParticleSpriteRendererInfo SpriteRenderer;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(DisplayName = "MeshRenderer", PropertyType = "Struct", ToolTips = "Particle Mesh Renderer", Category = "ParticleRenderer"))
    ParticleMeshRendererInfo MeshRenderer;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether Auto Random Seed"))
    SimulationType Simulation{SimulationType::CPU};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "GPU compute"))
    GPUComputeSimInfo GPUComputeSim;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "ParticleEmitterNode World Position X", bHide = true))
    SInt32 WorldX{0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "ParticleEmitterNode World Position Y", bHide = true))
    SInt32 WorldY{0};

    // Must be as last one for ParticleEmitterNode(Editor)
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Particle Renderer Type", bHide = true))
    ParticleRendererType RendererType{ParticleRendererType::Sprite};

    CEFunction(Editor)
    std::string GenSimulationShader() const;

    CE_Serialize_DeserializeEditor;
};

}   // namespace cross