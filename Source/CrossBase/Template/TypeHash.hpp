#pragma once

#include "Template/TypeDemangle.hpp"
#include "String/HashString.h"

namespace cross
{

template<typename T>
const HashString& GetTypeHash()
{
	static HashString sTypeHash;
	if (sTypeHash.IsEmpty())
	{
		sTypeHash.SetString(std::string(NameDetailPretty<T>()));
	}
	return sTypeHash;
}

template<typename ScalarType, std::enable_if_t<std::is_scalar_v<ScalarType> && !std::is_same_v<ScalarType, char*> && !std::is_same_v<ScalarType, const char*>>* = nullptr>
[[nodiscard]] inline UInt32 GetTypeHash(ScalarType Value)
{
    if constexpr (std::is_integral_v<ScalarType>)
    {
        if constexpr (sizeof(ScalarType) <= 4)
        {
            return Value;
        }
        else if constexpr (sizeof(ScalarType) == 8)
        {
            return (UInt32)Value + ((UInt32)(Value >> 32) * 23);
        }
        else if constexpr (sizeof(ScalarType) == 16)
        {
            const UInt64 Low = (UInt64)Value;
            const UInt64 High = (UInt64)(Value >> 64);
            return GetTypeHash(Low) ^ GetTypeHash(High);
        }
        else
        {
            static_assert(sizeof(ScalarType) == 0, "Unsupported integral type");
            return 0;
        }
    }
    else if constexpr (std::is_floating_point_v<ScalarType>)
    {
        if constexpr (std::is_same_v<ScalarType, float>)
        {
            return *(UInt32*)&Value;
        }
        else if constexpr (std::is_same_v<ScalarType, double>)
        {
            return GetTypeHash(*(UInt64*)&Value);
        }
        else
        {
            static_assert(sizeof(ScalarType) == 0, "Unsupported floating point type");
            return 0;
        }
    }
    else if constexpr (std::is_enum_v<ScalarType>)
    {
        return GetTypeHash((__underlying_type(ScalarType))Value);
    }
    else if constexpr (std::is_pointer_v<ScalarType>)
    {
        // Once the TCHAR* deprecations below are removed, we want to prevent accidental string hashing, so this static_assert should be commented back in
        // static_assert(!TIsCharType<std::remove_pointer_t<ScalarType>>::Value, "Pointers to string types should use a PointerHash() or FCrc::Stricmp_DEPRECATED() call depending on requirements");

        return PointerHash(Value);
    }
    else
    {
        static_assert(sizeof(ScalarType) == 0, "Unsupported scalar type");
        return 0;
    }
}
}