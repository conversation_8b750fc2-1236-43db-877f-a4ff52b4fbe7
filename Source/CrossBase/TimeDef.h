#pragma once
#include <chrono>
#include <ctime>
#include <mutex>
#include "CrossBase/Template/TypeTraits.hpp"
#if CROSSENGINE_WIN
#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <timeapi.h>
#include <processthreadsapi.h>

#pragma comment(lib, "Winmm.lib")

#ifndef PROCESS_POWER_THROTTLING_IGNORE_TIMER_RESOLUTION
#define PROCESS_POWER_THROTTLING_IGNORE_TIMER_RESOLUTION 0x4   // Defined in Windows 11 processthreadsapi.h but not in earlier versions
#endif // PROCESS_POWER_THROTTLING_IGNORE_TIMER_RESOLUTION

#endif // CROSSENGINE_WIN

namespace cross
{
    namespace time_lit = std::chrono_literals;

    using ClockType = std::chrono::steady_clock;
    using TimePoint = ClockType::time_point;
    using Seconds = std::chrono::duration<float>;
    using SecondCount = std::chrono::seconds;
    using MillionSeconds = std::chrono::duration<float, std::milli>;
    using MillionSecondCount = std::chrono::milliseconds;
    using NanoSecondCount = std::chrono::nanoseconds;

    template <typename T>
    using TIsDuration = TIsInstanceOf<T, std::chrono::duration>;
    template <typename T>
    constexpr bool TIsDurationV = TIsDuration<T>::value;

    template <typename T>
    using TIsTimePoint = TIsInstanceOf<T, std::chrono::time_point>;
    template <typename T>
    constexpr bool TIsTimePointV = TIsTimePoint<T>::value;

    namespace time
    {
        template <typename To, typename From>
        [[nodiscard]] constexpr auto Cast(From const& from) -> std::enable_if_t<
            std::conjunction_v<TIsDuration<From>, TIsDuration<To>>, To>
        {
            return std::chrono::duration_cast<To>(from);
        }

        template <typename To, typename From>
        [[nodiscard]] constexpr auto Cast(From const& from) -> std::enable_if_t<
            std::conjunction_v<TIsTimePoint<From>, TIsDuration<To>>, std::chrono::time_point<typename From::clock, To>>
        {
            return std::chrono::time_point_cast<To>(from);
        }

        inline auto CurrentMs()
        {
            return std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
        }

        inline const std::tm GetDateTime(std::chrono::system_clock::time_point now = std::chrono::system_clock::now())
        {
            std::time_t start_time = std::chrono::system_clock::to_time_t(now);
            struct tm time;
#if CROSSENGINE_WIN
            localtime_s(&time, &start_time);
#elif CROSSENGINE_ANDROID
            std::time_t now123 = std::time(NULL);
            localtime_r(&now123, &time);
#else
            localtime_r(&start_time, &time);
#endif
            return time;
        }

        inline const std::tm GetDateTime(std::chrono::steady_clock::time_point t)
        {
            return GetDateTime(std::chrono::system_clock::now()
                + std::chrono::duration_cast<std::chrono::system_clock::duration>(t - std::chrono::steady_clock::now()));
        }

        inline const std::string GetHourMinutesSecondMilli(std::chrono::steady_clock::time_point t)
        {
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(t.time_since_epoch()) % 1000;
            char timedisplay[100];
            auto time = GetDateTime(t);
            std::strftime(timedisplay, sizeof(timedisplay), "%H_%M_%S", &time);
            return std::string(timedisplay) + "_" + std::to_string(ms.count());
        }

        inline const std::string TimeStamp()
        {
            using namespace std::chrono;
            auto now = system_clock::now();
            auto ms = duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
            char timedisplay[100];
            auto t = GetDateTime(now);
            std::strftime(timedisplay, sizeof(timedisplay), "%Y_%m_%d_%H_%M_%S", &t);
            return std::string(timedisplay) + "_"+std::to_string(ms.count());
        }
#if CROSSENGINE_WIN
        inline const void WinSleep(std::int64_t time) 
        {
#ifndef _MANAGED
            HANDLE hTimer = CreateWaitableTimer(NULL, TRUE, NULL);
            if (hTimer == NULL) {
                //LOG_ERROR("Error creating waitable timer");
                return;
            }
            LARGE_INTEGER ft;
            ft.QuadPart = -time * 10000LL; // ms
            if (!SetWaitableTimerEx(hTimer, &ft, 0, NULL, NULL, NULL, WT_EXECUTEONLYONCE)) {
                //LOG_ERROR("Error setting waitable timer");
                CloseHandle(hTimer);
                return;
            }
            DWORD dwWaitResult = WaitForSingleObject(hTimer, INFINITE);
            if (dwWaitResult != WAIT_OBJECT_0) {
                //LOG_ERROR("Unexpected wait result");
                CloseHandle(hTimer);
                return;
            }
            CloseHandle(hTimer);
#endif
        }
#endif

        // Raise timer resolution
        // 
        // By default, Windows timer resolution is 15.625 ms, which means sleep time shorter than that is unguaranteed
        // Windows provides `timeBeginPeriod` and `timeEndPeriod` API to request higher timer resolution, but it performs differently according to specific Windows version
        // Before Windows 10, version 2004 (20H1, build 10.0.19041), whatever process calls this API will affect global system task schedular
        // After Windows 10, version 2004 (20H1, build 10.0.19041), call to this API only affects current process behaviour
        // After Windows 11, when program windows is fully occluded or minimized, this request will be ignored by OS and will possibly not take effect
        // Therefore this function wrap the api and do the necessary settings
        // And be cautious using higher timer resolution, which can increase device power consumption
        //
        // For more details, see https://learn.microsoft.com/en-us/windows/win32/api/timeapi/nf-timeapi-timebeginperiod
        // Also, there are useful comments that may be helpful when choosing suitable timer method on Windows
        // See https://github.com/chromium/chromium/blob/main/base/time/time_win.cc
        inline bool TimerResolution(const std::chrono::milliseconds& resolution)
        {
            using namespace std::literals::chrono_literals;

#if CROSSENGINE_WIN
            static bool initialized = false;
            static std::chrono::milliseconds minResolution = 0ms;
            static std::chrono::milliseconds maxResolution = 0ms;
            static std::chrono::milliseconds previousResolution = 0ms;
            static std::mutex timerMutex;

            std::scoped_lock lock(timerMutex);

            if (!initialized)
            {
                // Set current process power throttling
                // Process power throttling may affect timer resolution starting with Windows 11
                // For more details, see https://learn.microsoft.com/en-us/windows/win32/api/processthreadsapi/nf-processthreadsapi-setprocessinformation
                PROCESS_POWER_THROTTLING_STATE powerThrottlingState;
                RtlZeroMemory(&powerThrottlingState, sizeof(powerThrottlingState));
                powerThrottlingState.Version = PROCESS_POWER_THROTTLING_CURRENT_VERSION;
                powerThrottlingState.ControlMask = PROCESS_POWER_THROTTLING_IGNORE_TIMER_RESOLUTION;
                powerThrottlingState.StateMask = 0;

                // Get system min / max time resolution
                TIMECAPS caps;
                RtlZeroMemory(&caps, sizeof(caps));
                ::timeGetDevCaps(&caps, sizeof(caps));
                minResolution = std::chrono::milliseconds(caps.wPeriodMin);   // 1ms
                maxResolution = std::chrono::milliseconds(caps.wPeriodMax);

                initialized = true;
            }

            if (resolution < minResolution || resolution > maxResolution)
            {
                return false;
            }

            // Frequently change timer resolution is not recommended
            // See https://learn.microsoft.com/en-us/windows/win32/api/synchapi/nf-synchapi-sleep
            if (resolution != previousResolution)
            {
                if (previousResolution != 0ms)
                {
                    // LOG_DEBUG("timeEndPeriod {}", previousResolution.count());
                    ::timeEndPeriod(static_cast<UInt32>(previousResolution.count()));
                }

                // LOG_DEBUG("timeBeginPeriod {}", resolution.count());
                ::timeBeginPeriod(static_cast<UInt32>(resolution.count()));
                previousResolution = resolution;
            }
#endif

            return true;
        }
    }

    template <typename Duration>
    inline auto AheadOf(Duration const& duration) -> std::enable_if_t<TIsDurationV<Duration>, TimePoint>
    {
        return ClockType::now() + duration;
    }
}
