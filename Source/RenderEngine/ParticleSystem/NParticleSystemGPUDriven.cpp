#include "NParticleSystemGPUDriven.h"
#include "CrossFX/ParticleSystem/ParticleAttributeNames.h"
#include "ParticleSimulationSystemR.h"
#include "RendererSystemR.h"
#include "Common/FrameCounter.h"
#include "ECS/Develop/Framework/Types.h"
#include "RenderNode/ParticleSystemRenderNode.h"

namespace cross {

UInt32 NParticleSystemGPUDriven::sTickCounter = 0;

void FillEmitterParameters(REDPass* pass, const fx::NParticleSystemGPUTick& tick, const fx::NParticleEmitterGPUComputeData& computeData)
{
    auto& emitterParameters = computeData.mEmitterParameters;
    pass->SetProperty(fx::NParticleProperty::EmitterParameters::Emitter_Age, emitterParameters.mEmitterAge);
    pass->SetProperty(fx::NParticleProperty::EmitterParameters::Emitter_RandomSeed, emitterParameters.mEmitterRandomSeed);
    pass->SetProperty(fx::NParticleProperty::EmitterParameters::Engine_Emitter_TotalSpawnedParticles, emitterParameters.mEmitterTotalSpawnedParticles);

    if (computeData.mContext->mHasInterpolationParameters)
    {
        auto& emitterParametersPrev = computeData.mPrevEmitterParameters;

        pass->SetProperty(fx::NParticleProperty::EmitterParameters::PREV_Emitter_Age, emitterParametersPrev.mEmitterAge);
        pass->SetProperty(fx::NParticleProperty::EmitterParameters::PREV_Emitter_RandomSeed, emitterParametersPrev.mEmitterRandomSeed);
        pass->SetProperty(fx::NParticleProperty::EmitterParameters::PREV_Engine_Emitter_TotalSpawnedParticles, emitterParametersPrev.mEmitterTotalSpawnedParticles);
    }
}

void FillGlobalParameters(REDPass* pass, const fx::NParticleSystemGPUTick& tick, const fx::NParticleEmitterGPUComputeData& computeData)
{
    auto& parameter = tick.mGlobalParameters;

    pass->SetProperty(fx::NParticleProperty::GlobalParameters::Engine_InverseDeltaTime, parameter.mEngineInvDeltaTime);
    pass->SetProperty(fx::NParticleProperty::GlobalParameters::Engine_DeltaTime, parameter.mEngineDeltaTime);
    pass->SetProperty(fx::NParticleProperty::GlobalParameters::Engine_WorldDeltaTime, parameter.mWorldDeltaTime);

    if (computeData.mContext->mHasInterpolationParameters)
    {
        auto& parameterPrev = tick.mPrevGlobalParameters;
        pass->SetProperty(fx::NParticleProperty::GlobalParameters::PREV_Engine_InverseDeltaTime, parameterPrev.mEngineInvDeltaTime);
        pass->SetProperty(fx::NParticleProperty::GlobalParameters::PREV_Engine_DeltaTime, parameterPrev.mEngineDeltaTime);
        pass->SetProperty(fx::NParticleProperty::GlobalParameters::PREV_Engine_WorldDeltaTime, parameterPrev.mWorldDeltaTime);
    }
}

void FillOwnerParameters(REDPass* pass, const fx::NParticleSystemGPUTick& tick, const fx::NParticleEmitterGPUComputeData& computeData)
{
    auto& parameter = tick.mOwnerParameters;

    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_LWCTile, parameter.mEngineLWCTile);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_Scale, parameter.mEngineScale);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_Rotation, parameter.mEngineRotation.XYZW());
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_Position, parameter.mEnginePosition);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemLocalToWorld, parameter.mEngineLocalToWorld);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemLocalToWorldTransposed, parameter.mEngineLocalToWorldTransposed);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemWorldToLocal, parameter.mEngineWorldToLocal);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemWorldToLocalTransposed, parameter.mEngineWorldToLocalTransposed);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemLocalToWorldNoScale, parameter.mEngineLocalToWorldNoScale);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemWorldToLocalNoScale, parameter.mEngineWorldToLocalTransposed);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemXAxis, parameter.mEngineXAxis);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemYAxis, parameter.mEngineYAxis);
    pass->SetProperty(fx::NParticleProperty::OwnerParameters::Engine_Owner_SystemZAxis, parameter.mEngineZAxis);

    if (computeData.mContext->mHasInterpolationParameters)
    {
        auto& parameterPrev = tick.mPrevOwnerParameters;

        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_LWCTile, parameterPrev.mEngineLWCTile);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_Scale, parameterPrev.mEngineScale);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_Rotation, parameterPrev.mEngineRotation.XYZW());
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_Position, parameterPrev.mEnginePosition);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemLocalToWorld, parameterPrev.mEngineLocalToWorld);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemLocalToWorldTransposed, parameterPrev.mEngineLocalToWorldTransposed);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemWorldToLocal, parameterPrev.mEngineWorldToLocal);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemWorldToLocalTransposed, parameterPrev.mEngineWorldToLocalTransposed);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemLocalToWorldNoScale, parameterPrev.mEngineLocalToWorldNoScale);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemWorldToLocalNoScale, parameterPrev.mEngineWorldToLocalTransposed);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemXAxis, parameterPrev.mEngineXAxis);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemYAxis, parameterPrev.mEngineYAxis);
        pass->SetProperty(fx::NParticleProperty::OwnerParameters::PREV_Engine_Owner_SystemZAxis, parameterPrev.mEngineZAxis);
    }
}

void NParticleSystemGPUDriven::ProcessPendingTicks(RenderWorld* renderWorld)
{
    ProcessPendingProxies();
    UpdateInstanceCountManager();
    // Create Textures
    PrepareAllTicks(renderWorld);
    ExecuteTicks();

    // GPUInstanceCounterManager.UpdateDrawIndirectBuffers
    // Niagara supports two types of simulation ticks: PreOpaque and PostOpaque
}

void NParticleSystemGPUDriven::PrepareAllTicks(RenderWorld* renderWorld)
{
    for (auto proxy : mComputeProxies)
    {
        PrepareTicksForProxy(proxy, renderWorld);
    }
}

void NParticleSystemGPUDriven::PrepareTicksForProxy(fx::NParticleSystemGPUComputeProxy* proxy, RenderWorld* renderWorld)
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto red = rendererSystem->GetRenderingExecutionDescriptor();
    auto frameNum = rendererSystem->GetCurrentFrameNum();

    ecs::EntityID entityID = proxy->GetEntityID();
    auto particleCompWriter = renderWorld->GetComponent<ParticleSystemComponentR>(entityID).Write();
    ParticleSystemRenderNode& renderNode = particleCompWriter->GetRenderNode();
    auto& renderUnits = renderNode.GetRenderUnits();

    for (auto& tick : proxy->mPendingTicks)
    {
        for (auto& emitterCompute : tick.GetEmitterGPUComputeDatas())
        {
            auto context = emitterCompute.mContext.get();
            auto& dispatchInfo = mDispatchInfos.emplace_back(context, tick, emitterCompute);

            for (int bufferIndex = 0; bufferIndex < context->mGPUDataBufferR.size(); bufferIndex++)
            {
                auto& buffer = context->mGPUDataBufferR[bufferIndex];
                if (buffer == nullptr)
                {
                    buffer = new NParticleGPUDataBuffer(context->mTotalFloatComponents, context->mTotalInt32Components);
                    buffer->Allocate(red, context->mCurrentMaxAllocateInstancesR + 1, std::to_string(bufferIndex).c_str());
                }
            }

            dispatchInfo.mSrcCountOffset = context->mCountOffsetR;
            dispatchInfo.mDstCountOffset = mParticleInstanceCountManager.AcquireSlot();
            Assert(dispatchInfo.mDstCountOffset != fx::INVALID_INDEX);
            // dispatch a read back mDstCountOffset

            context->mCountOffsetR = dispatchInfo.mDstCountOffset;

            dispatchInfo.mSrcBuffer = context->GetPrevDataBuffer();
            dispatchInfo.mDstBuffer = context->GetNextDataBuffer();
            context->AdvanceDataBuffer();

            auto prevNumInstances = context->mCurrentNumInstancesR;
            context->mCurrentNumInstancesR += emitterCompute.mSpawnInfo.mSpawnRateInstances;
            context->mCurrentNumInstancesR = std::min(context->mCurrentMaxAllocateInstancesR, context->mCurrentNumInstancesR);

            dispatchInfo.mSrcNumInstances = prevNumInstances;
            dispatchInfo.mDstNumInstances = context->mCurrentNumInstancesR;
            context->mSpawnCountCache[frameNum & 1] = dispatchInfo.mDstNumInstances - dispatchInfo.mSrcNumInstances;
            Assert(dispatchInfo.mDstNumInstances >= dispatchInfo.mSrcNumInstances);

            LOG_INFO("{} : {} -> {}        offset {}", frameNum, dispatchInfo.mSrcNumInstances, dispatchInfo.mDstNumInstances, context->mCountOffsetR);
            // Suppose it's a final tick
            if (dispatchInfo.mSrcCountOffset != fx::INVALID_INDEX)
            {
                mInstanceCountToRelease.push_back(dispatchInfo.mSrcCountOffset);
                auto& readback = context->mEmitterInstanceCountReadbacks[frameNum % 3];
                Assert(readback.mGPUCountOffset == fx::INVALID_INDEX);

                readback.mCPUCount = dispatchInfo.mSrcNumInstances;
                readback.mFrameIndex = frameNum - 1;
                readback.mGPUCountOffset = dispatchInfo.mSrcCountOffset;
            }

            auto& renderUnit = renderUnits[context->mEmitterIndex];
            auto gpuRenderUnit = dynamic_cast<NGPUParticleRenderUnit*>(renderUnit.get());
            Assert(gpuRenderUnit);
            gpuRenderUnit->mInstanceCountOffset = dispatchInfo.mDstCountOffset;

            dispatchInfo.mGPUSceneBufferOffset = gpuRenderUnit->mAllocationInfo.mObjectAlloc.mIndexStart;
            dispatchInfo.mGPUSceneBufferView = gpuRenderUnit->mAllocationInfo.mObjectAlloc.mTypedBufferView;
            Assert(dispatchInfo.mGPUSceneBufferOffset >= 0);
        }
    }
}

void NParticleSystemGPUDriven::ExecuteTicks()
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto red = rendererSystem->GetRenderingExecutionDescriptor();

    for (const auto& dispatchInfo : mDispatchInfos)
    {
        auto context = dispatchInfo.mContext;
        auto& emitterGPUComputeData = dispatchInfo.mEmitterGPUComputeData;

        sTickCounter++;

        auto simPass = red->AllocatePass("NParticleGPUSimulation", true);
        simPass->SetProperty(fx::NParticleProperty::EmitterTickCounter, sTickCounter);
        simPass->SetProperty(fx::NParticleProperty::InputFloat, dispatchInfo.mSrcBuffer->mBufferFloatView.get());
        simPass->SetProperty(fx::NParticleProperty::InputInt, dispatchInfo.mSrcBuffer->mBufferIntView.get());
        simPass->SetProperty(fx::NParticleProperty::RWOutputFloat, dispatchInfo.mDstBuffer->mBufferFloatView.get());
        simPass->SetProperty(fx::NParticleProperty::RWOutputInt, dispatchInfo.mDstBuffer->mBufferIntView.get());
        simPass->SetProperty(fx::NParticleProperty::RWInstanceCounts, mParticleInstanceCountManager.GetCountBufferView());
        simPass->SetProperty(fx::NParticleProperty::ReadInstanceCountOffset, dispatchInfo.mSrcCountOffset);
        simPass->SetProperty(fx::NParticleProperty::WriteInstanceCountOffset, dispatchInfo.mDstCountOffset);
        simPass->SetProperty(fx::NParticleProperty::ComponentBufferSizeRead, static_cast<UInt32>(dispatchInfo.mSrcBuffer->mFloatStride / sizeof(float)));
        simPass->SetProperty(fx::NParticleProperty::ComponentBufferSizeWrite, static_cast<UInt32>(dispatchInfo.mDstBuffer->mFloatStride / sizeof(float)));
        simPass->SetProperty(fx::NParticleProperty::NumSpawnedInstances, dispatchInfo.mDstNumInstances - dispatchInfo.mSrcNumInstances);

        {
            simPass->SetProperty(fx::NParticleProperty::EmitterSpawnInfoOffsets, emitterGPUComputeData.mSpawnInfo.mSpawnInfoStartOffsets, sizeof(emitterGPUComputeData.mSpawnInfo.mSpawnInfoStartOffsets));
            simPass->SetProperty(fx::NParticleProperty::EmitterSpawnInfoParams, emitterGPUComputeData.mSpawnInfo.mSpawnInfoParams, sizeof(emitterGPUComputeData.mSpawnInfo.mSpawnInfoParams));
        }

        FillEmitterParameters(simPass, dispatchInfo.mTick, emitterGPUComputeData);
        FillGlobalParameters(simPass, dispatchInfo.mTick, emitterGPUComputeData);
        FillOwnerParameters(simPass, dispatchInfo.mTick, emitterGPUComputeData);

        simPass->SetProperty(fx::GpuParticleProp::GPU_SCENE_BUFFER_OFFSET, dispatchInfo.mGPUSceneBufferOffset);
        simPass->SetProperty(fx::GpuParticleProp::GLOBAL_SPACE_GPU_SCENE_BUFFER, dispatchInfo.mGPUSceneBufferView->mREDView.get());

        UInt3 globalThreadCount = {context->mCurrentNumInstancesR, 1, 1};
        UInt3 groupCount = math::DivideAndRoundUp(globalThreadCount, static_cast<UInt3>(context->mGPUScript.mThreadGroupSize));

        simPass->SetProperty(fx::NParticleProperty::DispatchThreadIdToLinear, UInt3(1, globalThreadCount.x, globalThreadCount.x * globalThreadCount.y));
        simPass->SetProperty(fx::NParticleProperty::DispatchThreadIdBounds, UInt3(globalThreadCount.x, globalThreadCount.y, globalThreadCount.z));
        simPass->Dispatch(context->mGPUScript.mShader, NameID("SimulateMainComputeCS"), groupCount.x, groupCount.y, groupCount.z);
    }

    FinishDispatches();
}

void NParticleSystemGPUDriven::FinishDispatches()
{
    for (auto proxy : mComputeProxies)
    {
        proxy->ReleaseTicks();
    }

    mDispatchInfos.clear();
    
    mParticleInstanceCountManager.EnqueueReadback();

    mParticleInstanceCountManager.ReleaseSlots(mInstanceCountToRelease);
    mInstanceCountToRelease.clear();
}

void NParticleSystemGPUDriven::UpdateInstanceCountManager()
{
    // Consume any pending readbacks that are ready
    //mParticleInstanceCountManager.Debug();

    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto frameNum = rendererSystem->GetCurrentFrameNum();

    auto frameIndexReadback = mParticleInstanceCountManager.GetFrameIndexReadback();

    for (auto proxy : mComputeProxies)
    {
        for (auto context : proxy->GetComputeContexts())
        {
            for (auto& readback : context->mEmitterInstanceCountReadbacks)
            {
                if (readback.mGPUCountOffset == fx::INVALID_INDEX || readback.mFrameIndex != frameIndexReadback)
                    continue;

                auto readbackCount = mParticleInstanceCountManager.GetReadbackCount(readback.mGPUCountOffset);

                auto updatedCount = readbackCount + context->mSpawnCountCache[0] + context->mSpawnCountCache[1];
                Assert(frameNum - readback.mFrameIndex == 3 && context->mCurrentNumInstancesR >= updatedCount);
                Assert(readback.mCPUCount >= readbackCount);
                context->mCurrentNumInstancesR = updatedCount;
                LOG_INFO("{} dead {} {}  count {} - {}", frameNum, readback.mFrameIndex, readback.mGPUCountOffset, readbackCount, updatedCount);
                readback.mGPUCountOffset = static_cast<UInt32>(fx::INVALID_INDEX);

                // context->mSpawnCountCache[frameNum & 1] = 0;
            }
        }
    }
}

void NParticleSystemGPUDriven::ProcessPendingProxies()
{
    for (auto proxy : mPendingRemovedProxies)
    {
        for (auto context : proxy->GetComputeContexts())
        {
            for (int bufferIndex = 0; bufferIndex < context->mGPUDataBufferR.size(); bufferIndex++)
            {
                auto& buffer = context->mGPUDataBufferR[bufferIndex];
                if (buffer)
                {
                    buffer->Release();
                    buffer = nullptr;
                }
            }
        }
    }

    std::sort(mPendingRemovedProxies.begin(), mPendingRemovedProxies.end());
    std::sort(mComputeProxies.begin(), mComputeProxies.end());

    {
        size_t i = 0, j = 0, k = 0;
        while (i < mComputeProxies.size() && j < mPendingRemovedProxies.size())
        {
            if (mComputeProxies[i] < mPendingRemovedProxies[j])
            {
                mComputeProxies[k++] = mComputeProxies[i++];
            }
            else if (mComputeProxies[i] == mPendingRemovedProxies[j])
            {
                ++i;
                ++j;
            }
            else
            { 
                ++j;
            }
        }
        
        while (i < mComputeProxies.size())
        {
            mComputeProxies[k++] = mComputeProxies[i++];
        }

        mComputeProxies.resize(k);
    }

    for (auto proxy : mPendingRemovedProxies)
    {
        delete proxy;
    }

    mPendingRemovedProxies.clear();
}

}