#include "Shadow.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "RenderEngine/RenderPipeline/Effects/FoliageGpuDriven.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "Resource/AssetStreaming.h"
#include <set>
#include <algorithm>

namespace cross {
UInt32 DivideAndRoundUp(UInt32 dividend, UInt32 divisor)
{
    return (dividend + divisor - 1) / divisor;
}

void VirtualShadowMapSettings::Initialize()
{
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(BlitShadowMapArrayComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ReprojectionShadowMapComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HoleFillingComputeShader);

    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ClearBufferUIntComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(InitIndirectArgs1D);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VirtualShadowMapPageManagementComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VirtualShadowMapProjectionComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VirtualShadowMapInstanceCullingComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VirtualShadowMapInstanceCulling2ComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VirtualShadowMapCacheManagementComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VirtualShadowMapFoliageInstanceCullingComputeShader);

    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LocalShadowMapInstanceCullingComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LocalShadowMapFoliageInstanceCullingComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LocalShadowCacheDebugComputeShader);

    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(OutputDebugTextureComputeShader);
}

void LocalLightShadowCacheSettings::Initialize()
{
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ClearBufferUIntComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LocalShadowMapInstanceCullingComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LocalShadowMapInstanceCulling2ComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LocalShadowCacheDebugComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(InitIndirectArgs1D);
}

ShadowPass::ShadowPass()
    : mRenderWorld(nullptr)
    , mRED(nullptr)
    , mDirectionalShadowMaps(nullptr)
{}

bool ShadowPass::ExecuteImp(const GameContext& gameContext, REDTextureView* depthView, std::array<REDTextureView*, 4>& gBufferViews, ShadowProperties* shadowProperties, UInt32 frameCount, REDTextureView*& shadowMaskView)
{
    mGameContext = gameContext;
    mRenderWorld = gameContext.mRenderWorld;
    mRenderPipeline = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    mRED = mRenderPipeline->GetRenderingExecutionDescriptor();

    auto clear_all_shadow_related_data = [=, this]() {
        mVirtualShadowMapArray.InitContext(mRenderWorld, mRenderPipeline, &mSetting);
        mVirtualShadowMapArray.FillDefaultShadowProperties(shadowProperties);

        shadowProperties->cachedSpotShadowMapRangesView = AllocateSpotLightRangesBuffer();
        shadowProperties->cachedPointShadowMapRangesView = AllocatePointLightRangesBuffer();
    };


    ClearShadowDatas();

    if (!mRenderPipeline->mSetting->Shadow)
    {
        clear_all_shadow_related_data();
        return false;
    }

    auto ffsRdrPipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    // ShadowMap
    {
        auto dirShadowMapCount = AllocateDirectionalShadowDatas(shadowProperties);
        GenerateCastShadowLightList();

        if (mCastShadowLightEntities.empty())
        {
            clear_all_shadow_related_data();
            return false;
        }

         mRED->BeginRegion("Shadow");

        AllocateDirectionalShadowMaps(shadowProperties, dirShadowMapCount);

        RenderShadowMaps(shadowProperties, depthView);
        
        // CacheLocalLightShadow
        if (mLocalLightShadowSettings.enable)
        {
            mLocalLightShadowMapProxy.InitContext(mRenderWorld, &mLocalLightShadowSettings);
            mLocalLightShadowMapProxy.RenderLocalLightsShadowMaps(mRenderPipeline, mLightCullingResult, mShadowDatas, mShadowMatrices, mSpotLightShadowMapRanges, mPointLightShadowMapRanges);
            shadowProperties->cachedSpotShadowMapsView = mLocalLightShadowMapProxy.GetLocalLightShadowMapView();
            shadowProperties->cachedPointShadowMapsView = mLocalLightShadowMapProxy.GetLocalLightShadowMapView();
            if (mLocalLightShadowSettings.VisualizedLocalShadowCache)
            {
                mLocalLightShadowMapProxy.RenderDebugLocalShadowCacheTexture(mDebugLocalShadowCacheTetxureView);
                ffsRdrPipe->GetViewModeVisualization().SetRenderDebugTex(GetLocalShadowCacheDebugTextureView());
            }
        }

        shadowProperties->shadowDatasBufferView = AllocateShadowDatasBuffer();
        shadowProperties->directionalShadowDatasBufferView = AllocateDirectionalShadowDatasBuffer();
        shadowProperties->shadowMatricesBufferView = AllocateShadowMatricesBuffer();
        shadowProperties->cachedSpotShadowMapRangesView = AllocateSpotLightRangesBuffer();
        shadowProperties->cachedPointShadowMapRangesView = AllocatePointLightRangesBuffer();
    }

    NGIBufferView* lightDataBufferView = AllocateLightBuffer();
    shadowProperties->lightDataBufferView = lightDataBufferView;
    // VirtualShadowMap
    {
        mVirtualShadowMapArray.InitContext(mRenderWorld, mRenderPipeline, &mSetting);

        if (mSetting.EnableVirtualShadowMap())
        {
            mVirtualShadowMapArray.RenderVirtualShadowMaps(depthView, gBufferViews[1], frameCount);

            mVirtualShadowMapArray.RenderDebugTexture(depthView, gBufferViews, lightDataBufferView, mDebugTetxureView);
            if (mSetting.DebugMode)
            {
                ffsRdrPipe->GetViewModeVisualization().SetRenderDebugTex(GetDebugTextureView());
            }
        }

        mVirtualShadowMapArray.SetShadowProperties(shadowProperties);
    }

    // ShadowMap ShadowMask
    {
        RenderShadowMask(depthView, gBufferViews, shadowProperties, lightDataBufferView, frameCount, shadowMaskView);
    }

    // VirtualShadowMap ShadowMask
    if (mSetting.EnableVirtualShadowMap())
    {
        mVirtualShadowMapArray.RenderShadowMask(depthView, gBufferViews, lightDataBufferView, static_cast<UInt32>(mCastShadowLightEntities.size()), shadowMaskView, shadowProperties);
    }

    mRED->EndRegion();

    return true;
}

void ShadowPass::RenderShadowMaps(ShadowProperties* shadowProperties, REDTextureView* depthView)
{
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto& lightEntities = mLightCullingResult->GetLightList();

    for (int lightIndex = 0; lightIndex < lightEntities.size(); lightIndex++)
    {
        auto lightEntity = lightEntities[lightIndex];
        auto lightComp = mRenderWorld->GetComponent<LightComponentR>(lightEntity);
        auto lightType = lightSys->GetLightType(lightComp.Read());
        auto shadowDataIndex = lightSys->GetLightShadowDataIndex(lightComp.Read());

        if (shadowDataIndex < 0)
        {
            continue;
        }

        if (lightType == LightType::Directional)
        {
            if (!mSetting.EnableVirtualShadowMap())
            {
                QUICK_SCOPED_CPU_TIMING("RenderDirectionLightShadow");
                RenderShadowMap_DirectionalLight(shadowProperties, lightEntity, shadowDataIndex, depthView);
            }
        }
    }
}

UInt16 ShadowPass::AllocateDirectionalShadowDatas(ShadowProperties* shadowProperties)
{
    QUICK_SCOPED_CPU_TIMING("AllocateDirectionalShadowDatas");
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();

    shadowProperties->dirLightEntities.clear();

    UInt16 shadowCascadeCount = static_cast<UInt16>(shadowSys->GetCascadeCount());
    UInt16 shadowDataIndex = 0, directionalShadowDataIndex = 0;
    UInt16 dirShadowMapIndex = 0;
    UInt16 shadowMatrixIndex = 0;
    mNumDirShadow = 0;

    const auto& lights = mLightCullingResult->GetLightIndexList();
    for (auto lightIdx : lights)
    {
        // auto lightComp = mRenderWorld->GetComponent<LightComponentR>(lightEntity);
        auto lightType = lightSys->GetLightType(lightIdx);
        auto lightEntity = lightSys->GetLightEntityID(lightIdx);

        lightSys->SetLightShadowDataIndex(lightIdx, -1);
        if (!shadowSys->IsShadowEnable(mRenderPipeline->GetCamera(), lightEntity))
        {
            continue;
        }

        if (lightType == LightType::Directional)
        {
            lightSys->SetLightShadowDataIndex(lightIdx, shadowDataIndex);

            ShadowData shadowData(lightSys, lightIdx, dirShadowMapIndex, shadowMatrixIndex, 0.0f);
            shadowData.directionalShadowDataIndex = directionalShadowDataIndex;
            mShadowDatas.push_back(shadowData);
            mCastShadowLightEntities.push_back(lightEntity);

            if (!mSetting.EnableVirtualShadowMap())
            {
                dirShadowMapIndex += shadowCascadeCount;
                shadowMatrixIndex += shadowCascadeCount;
                directionalShadowDataIndex++;
            }

            mNumDirShadow++;
            shadowDataIndex++;
        }
    }

    mShadowMatrices.resize(shadowMatrixIndex);

    return dirShadowMapIndex;
}

void ShadowPass::AllocateDirectionalShadowMaps(ShadowProperties* shadowProperties, UInt16 dirShadowMapCount)
{
    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();

    // DirectionalLight
    {
        UInt16 directionalShadowMapArraySize = dirShadowMapCount;

        // DirShadowMaps
        {
            const auto resolusion = mRenderPipeline->mSetting->ShadowMapResolution;

            if (!mDirectionalShadowMaps || !mRED->Validate(mDirectionalShadowMaps) || mDirectionalShadowMaps->mDesc.ArraySize != directionalShadowMapArraySize || mDirectionalShadowMaps->mDesc.Width != resolusion)
            {
                if (directionalShadowMapArraySize > 0)
                {
                    mDirectionalShadowMaps = mRED->AllocateTexture("DirectionalLightShadowMaps",
                                                                   NGITextureDesc{GraphicsFormat::D32_SFloat,
                                                                                  NGITextureType::Texture2DArray,
                                                                                  1,
                                                                                  1,
                                                                                  resolusion,
                                                                                  resolusion,
                                                                                  1,
                                                                                  directionalShadowMapArraySize,
                                                                                  NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst | NGITextureUsage::CopySrc,
                                                                                  false,
                                                                                  true});
                }
                else
                {
                    mDirectionalShadowMaps = nullptr;
                }
            }
        }

        // DirShadowMapsReprojection
        {
            UInt16 directionalShadowMapReprojectionArraySize = shadowSys->IsEnableReprojectionShadow() ? directionalShadowMapArraySize : 0;
            const auto resolusion = mRenderPipeline->mSetting->ReprojectionShadowMapResolution;

            if (!mDirectionalShadowMapsReprojection || !mRED->Validate(mDirectionalShadowMapsReprojection) || mDirectionalShadowMapsReprojection->mDesc.ArraySize != directionalShadowMapReprojectionArraySize ||
                mDirectionalShadowMapsReprojection->mDesc.Width != resolusion)
            {
                if (directionalShadowMapReprojectionArraySize > 0)
                {
                    mDirectionalShadowMapsReprojection =
                        mRED->AllocateTexture("DirectionalLightShadowMapsReprojection",
                                              NGITextureDesc{GraphicsFormat::R32_SFloat,
                                                             NGITextureType::Texture2DArray,
                                                             1,
                                                             1,
                                                             resolusion,
                                                             resolusion,
                                                             1,
                                                             directionalShadowMapReprojectionArraySize,
                                                             NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst | NGITextureUsage::CopySrc,
                                                             true,
                                                             true});
                }
                else
                {
                    mDirectionalShadowMapsReprojection = nullptr;
                }
            }
        }

        if (mDirectionalShadowMaps)
        {
            mDirectionalShadowMaps->ExtendLifetime();

            shadowProperties->dirShadowMapsView = mRED->AllocateTextureView(
                mDirectionalShadowMaps, NGITextureViewDesc{NGITextureUsage::ShaderResource, GraphicsFormat::D32_SFloat, NGITextureType::Texture2DArray, NGITextureSubRange{NGITextureAspect::Depth, 0, 1, 0, directionalShadowMapArraySize}});
        }

        if (mDirectionalShadowMapsReprojection)
        {
            mDirectionalShadowMapsReprojection->ExtendLifetime();

            mDirectionalShadowMapsReprojectionView = mRED->AllocateTextureView(
                mDirectionalShadowMapsReprojection,
                NGITextureViewDesc{NGITextureUsage::ShaderResource, GraphicsFormat::R32_SFloat, NGITextureType::Texture2DArray, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, mDirectionalShadowMapsReprojection->mDesc.ArraySize}});
        }
        else
        {
            mDirectionalShadowMapsReprojectionView = nullptr;
        }
    }
}

void ShadowPass::GenerateCastShadowLightList()
{
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();

    const auto& lights = mLightCullingResult->GetLightIndexList();
    for (auto lightIdx : lights)
    {
        // auto lightComp = mRenderWorld->GetComponent<LightComponentR>(lightEntity);
        auto lightType = lightSys->GetLightType(lightIdx);
        auto lightEntity = lightSys->GetLightEntityID(lightIdx);

        if (!shadowSys->IsShadowEnable(mRenderPipeline->GetCamera(), lightEntity))
        {
            continue;
        }

        if (lightType == LightType::Directional)
        {
            mCastShadowLightEntities.push_back(lightEntity);
        }
        else if (mLocalLightShadowSettings.enable && lightType == LightType::Spot)
        {
            mCastShadowLightEntities.push_back(lightEntity);
        }
        else if (mLocalLightShadowSettings.enable && lightType == LightType::Point)
        {
            mCastShadowLightEntities.push_back(lightEntity);
        }
    }
}

void ShadowPass::RenderShadowMap_DirectionalLight(ShadowProperties* shadowProperties, ecs::EntityID lightEntity, UInt32 shadowDataIndex, REDTextureView* depthView)
{
    auto* shadowCameraSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto shadowCameraComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(lightEntity);
    auto lightComp = mRenderWorld->GetComponent<LightComponentR>(lightEntity);

    const auto& shadowData = mShadowDatas[shadowDataIndex];
    auto dirShadowMapIndex = shadowData.shadowMapIndex;
    auto shadowMatrixIndex = (SizeType)shadowData.matrixIndex;
    int depthPyramidIndex = lightSys->GetLightDepthPyramidIndex(lightComp.Read());

    auto& shadowCameras = *shadowCameraSys->GetDirectionalShadowCameras(shadowCameraComp.Read(), mRenderPipeline->GetCamera());
    shadowProperties->dirLightEntities.push_back(lightEntity);

    DirectionalShadowData directionalShadowData;

    for (UInt32 cascadeIndex = 0; cascadeIndex < shadowCameraSys->GetCascadeCount(); cascadeIndex++)
    {
        const auto& shadowCamera = shadowCameras[cascadeIndex];
        bool isShadowCascadeNeedUpdate = shadowCameraSys->IsShadowCascadeNeedUpdate(mRenderPipeline->GetCamera(), cascadeIndex);
        shadowProperties->dirShadoMapsDrawLastFrame[lightEntity][cascadeIndex] = isShadowCascadeNeedUpdate;

        if (isShadowCascadeNeedUpdate)
        {
            REDTextureView* shadowMapView = mRED->AllocateTextureView(
                shadowProperties->dirShadowMapsView->mTexture,
                NGITextureViewDesc{NGITextureUsage::DepthStencil, GraphicsFormat::D32_SFloat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Depth, 0, 1, static_cast<UInt16>(dirShadowMapIndex + cascadeIndex), 1}});

            REDTextureRef depthPyramidLastFrame = nullptr;
            if (depthPyramidIndex >= 0 && cascadeIndex < MAX_CASCADE_SHADOW_COUNT && depthPyramidIndex + cascadeIndex < shadowProperties->dirShadowDepthPyramidLastFrame.size())
            {
                depthPyramidLastFrame = shadowProperties->dirShadowDepthPyramidLastFrame[depthPyramidIndex + cascadeIndex];
            }
            REDTextureView* depthPyramid = nullptr;
            if (depthPyramidLastFrame /* && mRED->Validate(depthPyramidLastFrame)*/)
            {
                depthPyramid = mRED->AllocateTextureView(depthPyramidLastFrame,
                                                         {
                                                             NGITextureUsage::ShaderResource,
                                                             depthPyramidLastFrame->mDesc.Format,
                                                             NGITextureType::Texture2D,
                                                             {
                                                                 NGITextureAspect::Color,
                                                                 0,
                                                                 depthPyramidLastFrame->mDesc.MipCount,
                                                                 0,
                                                                 1,
                                                             },
                                                         });
            }

            RenderOneShadowCamera(&shadowCamera, "_DirectionLight_" + std::to_string(cascadeIndex), shadowMapView, false, depthPyramid);
        }

        if (shadowCameraSys->IsEnableReprojectionShadow() && shadowCamera.GetCachedViewCamera() != nullptr)
        {
            REDTextureView* srcShadowMapSRV = mRED->AllocateTextureView(
                shadowProperties->dirShadowMapsView->mTexture,
                NGITextureViewDesc{NGITextureUsage::ShaderResource, GraphicsFormat::D32_SFloat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Depth, 0, 1, static_cast<UInt16>(dirShadowMapIndex + cascadeIndex), 1}});

            REDTextureView* dstShadowMapView = mRED->AllocateTextureView(mDirectionalShadowMapsReprojection,
                                                                         NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                                            GraphicsFormat::R32_UInt,
                                                                                            NGITextureType::Texture2D,
                                                                                            NGITextureSubRange{NGITextureAspect::Color, 0, 1, static_cast<UInt16>(dirShadowMapIndex + cascadeIndex), 1}});

            ReprojectionShadowMap2(shadowCamera, depthView, srcShadowMapSRV, dstShadowMapView);
        }

        mShadowMatrices[shadowMatrixIndex + cascadeIndex] = shadowCamera.GetViewProjMatrix();

        directionalShadowData.splitFar[cascadeIndex] = shadowCamera.GetSplitFar();
        directionalShadowData.fadePlaneOffset[cascadeIndex] = shadowCamera.GetFadePlaneOffset();
        directionalShadowData.shadowReceiverBias[cascadeIndex] = shadowCamera.GetShadowReceiverBias();
        directionalShadowData.transitionScale[cascadeIndex] = 1.0f / shadowCamera.ComputeTransitionSize();
        directionalShadowData.transmissionScale[cascadeIndex] = shadowCamera.GetMaxSubjectZ() - shadowCamera.GetMinSubjectZ();
    }

    mDirectionalShadowDatas.push_back(directionalShadowData);
}

void ShadowPass::RenderShadowMask(REDTextureView* depthView, std::array<REDTextureView*, 4>& gBufferViews, ShadowProperties* shadowProperties, NGIBufferView* lightDataBufferView, UInt32 frameCount, REDTextureView*& shadowMaskView)
{
    auto* shadowCameraSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();
    auto gameViewWidth = depthView->mTexture->mDesc.Width;
    auto gameViewHeight = depthView->mTexture->mDesc.Height;
    auto targetWidth = static_cast<UInt16>(gameViewWidth);
    auto targetHeight = static_cast<UInt16>(gameViewHeight);

    REDTextureView* depthSRV = mRED->AllocateTextureView(depthView->mTexture,
                                                         NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                            depthView->mTexture->mDesc.Format,
                                                                            NGITextureType::Texture2D,
                                                                            {
                                                                                NGITextureAspect::Depth,
                                                                                0,
                                                                                1,
                                                                                0,
                                                                                1,
                                                                            }});

    UInt16 shadowMaskMapArraySize = mNumDirShadow;
    if (shadowMaskMapArraySize > 0)
    {
        // R:wholeSceneShadow, G:wholeSceneShadowSSS R:punctualLightShadow, G:punctualLightShadowSSS
        REDTextureRef shadowMaskMaps = mRED->AllocateTexture("shadowMaskMap2DArray",
                                                           NGITextureDesc{GraphicsFormat::R8G8B8A8_UNorm,
                                                                          NGITextureType::Texture2DArray,
                                                                          1,
                                                                          1,
                                                                          targetWidth,
                                                                          targetHeight,
                                                                          1,
                                                                          shadowMaskMapArraySize,
                                                                          NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                                                                          false,
                                                                          true});

        shadowMaskView = mRED->AllocateTextureView(shadowMaskMaps,
                                                   NGITextureViewDesc{
                                                       NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                       GraphicsFormat::R8G8B8A8_UNorm,
                                                       NGITextureType::Texture2DArray,
                                                       NGITextureSubRange{
                                                           NGITextureAspect::Color,
                                                           0,
                                                           1,
                                                           0,
                                                           shadowMaskMapArraySize,
                                                       },
                                                   });

        auto* pass = mRED->AllocatePass("CollectShadow");

        pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);
        pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
        pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
        pass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3]);
        pass->SetProperty(NAME_ID("_ShadowMatrices"), shadowProperties->shadowMatricesBufferView);
        pass->SetProperty(NAME_ID("_ShadowDatas"), shadowProperties->shadowDatasBufferView);
        pass->SetProperty(NAME_ID("_DirectionalShadowDatas"), shadowProperties->directionalShadowDatasBufferView);
        pass->SetProperty(NAME_ID("_SpotLightShadowRanges"), shadowProperties->cachedSpotShadowMapRangesView);
        Float4 shadowSize{static_cast<float>(mRenderPipeline->mSetting->ShadowMapResolution),
                          static_cast<float>(mRenderPipeline->mSetting->ShadowMapResolution),
                          1.f / mRenderPipeline->mSetting->ShadowMapResolution,
                          1.f / mRenderPipeline->mSetting->ShadowMapResolution};
        Float4 reprojectionShadowSize{static_cast<float>(mRenderPipeline->mSetting->ReprojectionShadowMapResolution),
                                      static_cast<float>(mRenderPipeline->mSetting->ReprojectionShadowMapResolution),
                                      1.f / mRenderPipeline->mSetting->ReprojectionShadowMapResolution,
                                      1.f / mRenderPipeline->mSetting->ReprojectionShadowMapResolution};
        pass->SetProperty(NAME_ID("_DirShadowMapSize"), shadowSize);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        pass->SetProperty(BuiltInProperty::ce_CameraTilePosition, mRenderPipeline->GetRenderCamera()->GetTilePosition());
        pass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
#else
        pass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), false);
#endif
        pass->SetProperty(NAME_ID("_ShadowCascadeCount"), static_cast<float>(shadowCameraSys->GetCascadeCount()));
        pass->SetProperty(NAME_ID("_TransmissionDensityScale"), mRenderPipeline->mSetting->TransmissionDensityScale);
        if (shadowProperties->dirShadowMapsView)
        {
            pass->SetProperty(NAME_ID("_DirShadowMaps"), shadowProperties->dirShadowMapsView);
        }
        if (mDirectionalShadowMapsReprojectionView)
        {
            pass->SetProperty(NAME_ID("_DirShadowMapsReprojection"), mDirectionalShadowMapsReprojectionView);
        }

        pass->SetProperty(NAME_ID("_ShadowMasks"), shadowMaskView);

        Float4 screenTexelSize = Float4(1.f / static_cast<float>(targetWidth), 1.f / static_cast<float>(targetHeight), static_cast<float>(targetWidth), static_cast<float>(targetHeight));

        pass->SetProperty(NAME_ID("_ShadowMaskTexelSize"), screenTexelSize);
        pass->SetProperty(NAME_ID("_LightCount"), static_cast<float>(shadowMaskMapArraySize));
        pass->SetProperty(NAME_ID("_LightDatas"), lightDataBufferView);

        const auto& cascadeControl = shadowCameraSys->GetCascadeControl();
        pass->SetProperty(NAME_ID("_CascadeControl"), Float4(cascadeControl[0], cascadeControl[1], cascadeControl[2], cascadeControl[3]));
        pass->SetProperty(NAME_ID("_FrameCount"), UInt4(frameCount, 0, 0, 0));
        pass->SetProperty(NAME_ID("USE_REPROJECTION_SHADOW"), false);

        UInt4 cascadeUseReprojection;
        if (shadowCameraSys->IsEnableReprojectionShadow())
        {
            cascadeUseReprojection = UInt4(0, 1, 1, 1);
        }
        else
        {
            cascadeUseReprojection = UInt4(0, 0, 0, 0);
        }

        pass->SetProperty(NAME_ID("_CascadeUseReprojection"), cascadeUseReprojection);
        pass->SetProperty(NAME_ID("_ReprojectionDirShadowMapSize"), reprojectionShadowSize);

        UInt3 groupSize_pass;
        mRenderPipeline->mCollectShadowMaskShader->GetThreadGroupSize("CS_RenderShadowMask", groupSize_pass.x, groupSize_pass.y, groupSize_pass.z);
        pass->Dispatch(mRenderPipeline->mCollectShadowMaskShader, "CS_RenderShadowMask", (targetWidth + groupSize_pass.x - 1) / groupSize_pass.x, (targetHeight + groupSize_pass.y - 1) / groupSize_pass.y, 1);
    }
}

REDTextureView* ShadowPass::BlitDirShadowMap(REDTextureView* srcShadowMapsView)
{
    if (srcShadowMapsView == nullptr)
        return nullptr;

    const UInt32 resolution = 512;

    REDTextureRef dstShadowMaps = mRED->AllocateTexture(
        "DirectionalLightShadowMaps_LowRes",
        NGITextureDesc{
            GraphicsFormat::R32_SFloat, NGITextureType::Texture2DArray, 1, 1, resolution, resolution, 1, srcShadowMapsView->mTexture->mDesc.ArraySize, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, false, true});

    auto* dstShadowMapsView = mRED->AllocateTextureView(dstShadowMaps,
                                                        NGITextureViewDesc{
                                                            NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                            GraphicsFormat::R32_SFloat,
                                                            NGITextureType::Texture2DArray,
                                                            NGITextureSubRange{
                                                                NGITextureAspect::Color,
                                                                0,
                                                                1,
                                                                0,
                                                                srcShadowMapsView->mTexture->mDesc.ArraySize,
                                                            },
                                                        });

    auto* pass = mRED->AllocatePass("BlitDirShadowMaps");
    pass->SetProperty(NAME_ID("_SrcShadowMaps"), srcShadowMapsView);
    pass->SetProperty(NAME_ID("_DstShadowMaps"), dstShadowMapsView);

    UInt3 groupSize_pass;
    mSetting.BlitShadowMapArrayComputeShaderR->GetThreadGroupSize("Main", groupSize_pass.x, groupSize_pass.y, groupSize_pass.z);
    pass->Dispatch(mSetting.BlitShadowMapArrayComputeShaderR, "Main", (resolution + groupSize_pass.x - 1) / groupSize_pass.x, (resolution + groupSize_pass.y - 1) / groupSize_pass.y, 1);

    return dstShadowMapsView;
}

void ShadowPass::RenderOneShadowCamera(const RenderCamera* shadowCamera, const std::string& cameraTag, REDTextureView*& shadowView, bool isPunctualShadow, REDTextureView* depthPyramidLastFrame, SubMapInfo* subMapInfo)
{
    mRED->BeginRegion("ShadowCaster" + cameraTag);

    REDPass* shadowPass;
    NGIClearValue clearValue{};
    clearValue.depthStencil.depth = 1.f;
    clearValue.depthStencil.stencil = 0;
    REDDepthStencilTargetDesc depthStencilTargetDesc{
        shadowView,
        NGILoadOp::Clear,
        NGIStoreOp::Store,
        NGILoadOp::Clear,
        NGIStoreOp::Store,
        clearValue,
    };

    // TODO(yazhenyuan): seems problematic in parrallel case, same key for multiple passes?
    auto* cullingResult = mRED->Cull(REDCullingDesc{mRenderWorld, const_cast<cross::RenderCamera*>(shadowCamera)});
    auto* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{NAME_ID("shadow_all"), 0, UINT16_MAX, RenderEffectTag::CastShadow});

    // Render shadow depth
    {
        mRED->BeginRenderPass("ShadowCaster" + cameraTag, 0, nullptr, &depthStencilTargetDesc);
        shadowPass = mRED->AllocateSubRenderPass("shadow", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);

        // override the global context;
        auto invViewMatrix = shadowCamera->GetViewMatrix().Inverted();
        Float3 cameraPos = Float3(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32);
        shadowPass->SetProperty(BuiltInProperty::ce_CameraPos, cameraPos);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        shadowPass->SetProperty(BuiltInProperty::ce_CameraTilePosition, shadowCamera->GetTilePosition());
#endif
        shadowPass->SetProperty(BuiltInProperty::ce_View, shadowCamera->GetViewMatrix());
        shadowPass->SetProperty(BuiltInProperty::ce_Projection, shadowCamera->GetProjMatrix());

        if (isPunctualShadow)
        {
            shadowPass->SetProperty(NAME_ID("_NormalizeFactor"), shadowCamera->GetFarPlane());
        }
        else
        {
            const DirectionalLightShadowCamera* dirShadowCamera = static_cast<const DirectionalLightShadowCamera*>(shadowCamera);
            shadowPass->SetProperty(NAME_ID("ce_ShadowParams"), Float4(dirShadowCamera->GetShadowBias(), dirShadowCamera->GetShadowSlopeBias(), dirShadowCamera->GetMaxShadowSlopeBias(), 0.0f));
        }

        shadowPass->SetProperty(NAME_ID("PUNCTUAL_LIGHT"), isPunctualShadow);   // PUCTUAL_LIGHT here means "local light", including spot & point lights
        shadowPass->SetProperty(NAME_ID("SOFT_SHADOW"), false);
        shadowPass->SetProperty(BuiltInProperty::CE_INSTANCING, true);

        auto drawUnitsDesc = REDRenderDrawUnitsDesc{drawUnitList,
                                                    NGIRasterizationStateDesc{
                                                        FillMode::Solid,
                                                        CullMode::None,
                                                        FaceOrder::CW,
                                                        false,
                                                        false,
                                                        false,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        RasterizationMode::DefaultRaster,
                                                        0,
                                                    }};

        shadowPass->OnCulling([=](REDPass* pass) { 
            if (drawUnitList->GetDefaultObjectIndexBufferView())
                shadowPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView()); 
        });

        shadowPass->RenderDrawUnits(drawUnitsDesc);

        mRED->EndRenderPass();
    }

    mRED->EndRegion();
}

void ShadowPass::ReprojectionShadowMap(const DirectionalLightShadowCamera& shadowCamera, REDTextureView* depthView, REDTextureView* srcShadowMapSRV, REDTextureView* dstShadowMapUAV)
{
    auto* ffsRenderPipelineSetting = mRenderPipeline->GetSetting();
    bool isEnableHoleFilling = ffsRenderPipelineSetting->EnableHoleFilling;

    const auto& srcShadowMapDesc = srcShadowMapSRV->mTexture->mDesc;
    const auto srcWidth = srcShadowMapDesc.Width;
    const auto srcHeight = srcShadowMapDesc.Height;

    const auto& dstShadowMapDesc = dstShadowMapUAV->mTexture->mDesc;
    const auto dstWidth = dstShadowMapDesc.Width;
    const auto dstHeight = dstShadowMapDesc.Height;

    const auto& depthBufferDesc = depthView->mTexture->mDesc;
    const auto depthWidth = depthBufferDesc.Width;
    const auto depthHeight = depthBufferDesc.Height;

    REDTextureView* depthSRV = mRED->AllocateTextureView(depthView->mTexture,
                                                         NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                            depthView->mTexture->mDesc.Format,
                                                                            NGITextureType::Texture2D,
                                                                            {
                                                                                NGITextureAspect::Depth,
                                                                                0,
                                                                                1,
                                                                                0,
                                                                                1,
                                                                            }});
    REDTextureRef tempShadowMap = nullptr;
    REDTextureView* dstTempShadowMapUAV_UInt32 = nullptr;

    if (isEnableHoleFilling)
    {
        tempShadowMap = mRED->AllocateTexture("Reprojection Temp ShadowMap",
                                              NGITextureDesc{
                                                  GraphicsFormat::R32_SFloat,
                                                  NGITextureType::Texture2D,
                                                  1,
                                                  1,
                                                  dstWidth / 2,
                                                  dstHeight / 2,
                                                  1,
                                                  1,
                                                  NGITextureUsage::CopyDst | NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                  true,
                                              });

        dstTempShadowMapUAV_UInt32 = mRED->AllocateTextureView(tempShadowMap,
                                                               NGITextureViewDesc{
                                                                   NGITextureUsage::UnorderedAccess,
                                                                   GraphicsFormat::R32_UInt,
                                                                   NGITextureType::Texture2D,
                                                                   NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1},
                                                               });
    }

    // Clear ShadowMap
    {
        auto* pass = mRED->AllocatePass("ClearShadowMap", true);
        {
            pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapUAV);

            UInt3 groupSize;
            mSetting.ReprojectionShadowMapComputeShaderR->GetThreadGroupSize(NameID("ClearShadowMap"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mSetting.ReprojectionShadowMapComputeShaderR, NameID("ClearShadowMap"), DivideAndRoundUp(dstWidth, groupSize.x), DivideAndRoundUp(dstHeight, groupSize.y), 1);
        }
    }

    // Reprojection to ShadowMap
    {
        auto* pass = mRED->AllocatePass("ReprojectionShadowMap", true);
        {
            pass->SetProperty(NAME_ID("_PrevInvShadowMatrix"), shadowCamera.GetCachedShadowMatrix().Inverted());
            pass->SetProperty(NAME_ID("_CurrShadowMatrix"), shadowCamera.GetViewProjMatrix());
            pass->SetProperty(NAME_ID("_PrevLightDirection"), shadowCamera.GetCachedLightDirection());
            pass->SetProperty(NAME_ID("_CurrLightDirection"), shadowCamera.GetLightDirection());

            pass->SetProperty(NAME_ID("_SrcShadowMap"), srcShadowMapSRV);
            pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapUAV);

            UInt3 groupSize;
            mSetting.ReprojectionShadowMapComputeShaderR->GetThreadGroupSize(NameID("ReprojectionShadowMap"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mSetting.ReprojectionShadowMapComputeShaderR, NameID("ReprojectionShadowMap"), DivideAndRoundUp(srcWidth, groupSize.x), DivideAndRoundUp(srcHeight, groupSize.y), 1);
        }
    }

    if (isEnableHoleFilling)
    {
        // DownSample
        {
            auto* pass = mRED->AllocatePass("DownSample", true);
            {
                pass->SetProperty(NAME_ID("_SrcShadowMap"), dstShadowMapUAV);
                pass->SetProperty(NAME_ID("_DstShadowMap"), dstTempShadowMapUAV_UInt32);

                UInt3 groupSize;
                mSetting.HoleFillingComputeShaderR->GetThreadGroupSize(NameID("DownSample"), groupSize.x, groupSize.y, groupSize.z);
                pass->Dispatch(mSetting.HoleFillingComputeShaderR, NameID("DownSample"), DivideAndRoundUp(dstWidth / 2, groupSize.x), DivideAndRoundUp(dstHeight / 2, groupSize.y), 1);
            }
        }

        // HoleFilling
        {
            auto* pass = mRED->AllocatePass("HoleFilling", true);
            {
                auto* srcTempShadowMapUAV_UInt32 = mRED->AllocateTextureView(tempShadowMap,
                                                                             NGITextureViewDesc{
                                                                                 NGITextureUsage::ShaderResource,
                                                                                 GraphicsFormat::R32_UInt,
                                                                                 NGITextureType::Texture2D,
                                                                                 NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1},
                                                                             });

                pass->SetProperty(NAME_ID("_SrcShadowMap"), srcTempShadowMapUAV_UInt32);
                pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapUAV);

                UInt3 groupSize;
                mSetting.HoleFillingComputeShaderR->GetThreadGroupSize(NameID("HoleFilling"), groupSize.x, groupSize.y, groupSize.z);
                pass->Dispatch(mSetting.HoleFillingComputeShaderR, NameID("HoleFilling"), DivideAndRoundUp(dstWidth, groupSize.x), DivideAndRoundUp(dstHeight, groupSize.y), 1);
            }
        }
    }

    // Reprojection DepthBuffer to ShadowMap
    {
        auto* pass = mRED->AllocatePass("ReprojectionDepthBuffer", true);
        {
            pass->SetProperty(NAME_ID("_CurrShadowMatrix"), shadowCamera.GetViewProjMatrix());
            pass->SetProperty(NAME_ID("_PrevLightDirection"), shadowCamera.GetCachedLightDirection());
            pass->SetProperty(NAME_ID("_CurrLightDirection"), shadowCamera.GetLightDirection());
            pass->SetProperty(NAME_ID("_ReprojectionDepthBufferBias"), mRenderPipeline->mSetting->ReprojectionDepthBufferBias);

            pass->SetProperty(NAME_ID("_DepthBuffer"), depthSRV);
            pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapUAV);

            UInt3 groupSize;
            mSetting.ReprojectionShadowMapComputeShaderR->GetThreadGroupSize(NameID("ReprojectionDepthBuffer"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mSetting.ReprojectionShadowMapComputeShaderR, NameID("ReprojectionDepthBuffer"), DivideAndRoundUp(depthWidth, groupSize.x), DivideAndRoundUp(depthHeight, groupSize.y), 1);
        }
    }
}

void ShadowPass::ReprojectionShadowMap2(const DirectionalLightShadowCamera& shadowCamera, REDTextureView* depthView, REDTextureView* srcShadowMapSRV, REDTextureView* dstShadowMapView_UInt32)
{
    auto* ffsRenderPipelineSetting = mRenderPipeline->GetSetting();

    const auto& srcShadowMapDesc = srcShadowMapSRV->mTexture->mDesc;
    const auto srcWidth = srcShadowMapDesc.Width;
    const auto srcHeight = srcShadowMapDesc.Height;

    const auto& dstShadowMapDesc = dstShadowMapView_UInt32->mTexture->mDesc;
    const auto dstWidth = dstShadowMapDesc.Width;
    const auto dstHeight = dstShadowMapDesc.Height;

    const auto& depthBufferDesc = depthView->mTexture->mDesc;
    const auto depthWidth = depthBufferDesc.Width;
    const auto depthHeight = depthBufferDesc.Height;

    REDTextureView* dstShadowMapView_Float = mRED->AllocateTextureView(dstShadowMapView_UInt32->mTexture,
                                                                       NGITextureViewDesc{
                                                                           NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                           GraphicsFormat::R32_SFloat,
                                                                           NGITextureType::Texture2D,
                                                                           dstShadowMapView_UInt32->mDesc.SubRange,
                                                                       });

    REDTextureView* depthSRV = mRED->AllocateTextureView(depthView->mTexture,
                                                         NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                            depthView->mTexture->mDesc.Format,
                                                                            NGITextureType::Texture2D,
                                                                            {
                                                                                NGITextureAspect::Depth,
                                                                                0,
                                                                                1,
                                                                                0,
                                                                                1,
                                                                            }});

    REDTextureRef filteredShadowMap = mRED->AllocateTexture("Filtered ShadowMap",
                                                          NGITextureDesc{
                                                              GraphicsFormat::R32_SFloat,
                                                              NGITextureType::Texture2D,
                                                              1,
                                                              1,
                                                              dstWidth,
                                                              dstHeight,
                                                              1,
                                                              1,
                                                              NGITextureUsage::CopyDst | NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                              true,
                                                          });

    REDTextureView* filteredShadowMapView = mRED->AllocateTextureView(filteredShadowMap,
                                                                      NGITextureViewDesc{
                                                                          NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                          GraphicsFormat::R32_SFloat,
                                                                          NGITextureType::Texture2D,
                                                                          NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1},
                                                                      });

    // Clear ShadowMap
    {
        auto* pass = mRED->AllocatePass("ClearShadowMap");
        {
            pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapView_UInt32);

            UInt3 groupSize;
            mSetting.ReprojectionShadowMapComputeShaderR->GetThreadGroupSize(NameID("ClearShadowMap"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mSetting.ReprojectionShadowMapComputeShaderR, NameID("ClearShadowMap"), DivideAndRoundUp(dstWidth, groupSize.x), DivideAndRoundUp(dstHeight, groupSize.y), 1);
        }
    }

    // Reprojection to ShadowMap
    {
        auto* pass = mRED->AllocatePass("ReprojectionShadowMap");
        {
            pass->SetProperty(NAME_ID("_PrevInvShadowMatrix"), shadowCamera.GetCachedShadowMatrix().Inverted());
            pass->SetProperty(NAME_ID("_CurrShadowMatrix"), shadowCamera.GetViewProjMatrix());
            pass->SetProperty(NAME_ID("_PrevLightDirection"), shadowCamera.GetCachedLightDirection());
            pass->SetProperty(NAME_ID("_CurrLightDirection"), shadowCamera.GetLightDirection());

            pass->SetProperty(NAME_ID("_SrcShadowMap"), srcShadowMapSRV);
            pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapView_UInt32);

            UInt3 groupSize;
            mSetting.ReprojectionShadowMapComputeShaderR->GetThreadGroupSize(NameID("ReprojectionShadowMap"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mSetting.ReprojectionShadowMapComputeShaderR, NameID("ReprojectionShadowMap"), DivideAndRoundUp(srcWidth, groupSize.x), DivideAndRoundUp(srcHeight, groupSize.y), 1);
        }
    }

    // Reprojection DepthBuffer to ShadowMap
    {
        auto* pass = mRED->AllocatePass("ReprojectionDepthBuffer");
        {
            pass->SetProperty(NAME_ID("_CurrShadowMatrix"), shadowCamera.GetViewProjMatrix());
            pass->SetProperty(NAME_ID("_PrevLightDirection"), shadowCamera.GetCachedLightDirection());
            pass->SetProperty(NAME_ID("_CurrLightDirection"), shadowCamera.GetLightDirection());
            pass->SetProperty(NAME_ID("_ReprojectionDepthBufferBias"), mRenderPipeline->mSetting->ReprojectionDepthBufferBias);

            pass->SetProperty(NAME_ID("_DepthBuffer"), depthSRV);
            pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapView_UInt32);

            UInt3 groupSize;
            mSetting.ReprojectionShadowMapComputeShaderR->GetThreadGroupSize(NameID("ReprojectionDepthBuffer"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mSetting.ReprojectionShadowMapComputeShaderR, NameID("ReprojectionDepthBuffer"), DivideAndRoundUp(depthWidth, groupSize.x), DivideAndRoundUp(depthHeight, groupSize.y), 1);
        }
    }

    constexpr UInt32 GroupSize1D = 16;
    Float4 screenSize = Float4(static_cast<float>(dstWidth), static_cast<float>(dstHeight), 1.0f / static_cast<float>(dstWidth), 1.0f / static_cast<float>(dstHeight));

    UInt4 uintParams0(ffsRenderPipelineSetting->HoleFillingMaxStepCount, ToUnderlying(ffsRenderPipelineSetting->ReprojectionShadowFilterType), 0u, 0u);

    // MedianFilter
    {
        auto* pass = mRED->AllocatePass("MedianFilter");
        {
            pass->SetProperty(NAME_ID("_SrcShadowMap"), dstShadowMapView_Float);
            pass->SetProperty(NAME_ID("_DstShadowMap"), filteredShadowMapView);

            pass->SetProperty(NAME_ID("_ScreenSize"), screenSize);
            pass->SetProperty(NAME_ID("_UIntParams0"), uintParams0);

            pass->Dispatch(mSetting.HoleFillingComputeShaderR, NameID("MedianFilter"), DivideAndRoundUp(dstWidth, GroupSize1D), DivideAndRoundUp(dstHeight, GroupSize1D), 1);
        }
    }

    // HoleFilling
    {
        auto* pass = mRED->AllocatePass("HoleFilling");
        {
            pass->SetProperty(NAME_ID("_PrevShadowMatrix"), shadowCamera.GetCachedShadowMatrix());
            pass->SetProperty(NAME_ID("_PrevInvShadowMatrix"), shadowCamera.GetCachedShadowMatrix().Inverted());
            pass->SetProperty(NAME_ID("_CurrShadowMatrix"), shadowCamera.GetViewProjMatrix());
            pass->SetProperty(NAME_ID("_CurrInvShadowMatrix"), shadowCamera.GetViewProjMatrix().Inverted());

            pass->SetProperty(NAME_ID("_SrcShadowMap"), filteredShadowMapView);
            pass->SetProperty(NAME_ID("_FilteredShadowMap"), filteredShadowMapView);
            pass->SetProperty(NAME_ID("_DstShadowMap"), dstShadowMapView_Float);

            pass->SetProperty(NAME_ID("_ScreenSize"), screenSize);
            pass->SetProperty(NAME_ID("_UIntParams0"), uintParams0);
            pass->SetProperty(NAME_ID("ENABLE_HOLEFILLING"), ffsRenderPipelineSetting->EnableHoleFilling);

            pass->Dispatch(mSetting.HoleFillingComputeShaderR, NameID("HoleFilling"), DivideAndRoundUp(dstWidth, GroupSize1D), DivideAndRoundUp(dstHeight, GroupSize1D), 1);
        }
    }
}

void ShadowPass::ClearShadowDatas()
{
    mShadowDatas.clear();
    mDirectionalShadowDatas.clear();
    mCastShadowLightEntities.clear();
    mSpotLightShadowMapRanges.clear();
    mPointLightShadowMapRanges.clear();
}

ShadowPass::LightData ShadowPass::GetLightData(ecs::EntityID lightEntity)
{
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();
    auto* skySys = mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();

    auto [lightComp, transformComp, tilePositionComp, shadowComp] = mRenderWorld->GetComponent<LightComponentR, TransformComponentR, TilePositionComponentR, ShadowCameraComponentR>(lightEntity);

    Float4 lightColor = Float4(lightSys->GetLightColor(lightComp.Read()) * lightSys->GetLightIntensity(lightComp.Read()), 0.0f);

    Float4 lightDirPos;
    Float4 lightTilePos;
    Float4 lightSpotDir;
    Float4 lightAttenuation;
    SInt32 virtualShadowMapId = -1;
    float sourceAngle = 0.0f;

    switch (lightSys->GetLightType(lightComp.Read()))
    {
    case LightType::Directional:
    {
        {
            Float3 tranmittance = lightSys->GetLightTransmittance(lightComp.Read());
            Float3 color = lightColor.XYZ() * tranmittance;
            lightColor = Float4(color.x, color.y, color.z, -1.0f);
        }

        lightDirPos = transformSys->GetWorldRotation(transformComp.Read()).Float4Rotate(Float4(0, 0, 1, 0));
        lightDirPos.w = 0.0f;
        lightAttenuation = Float4{0.0f, 0.0f, 0.00000001f, 100000000.0f};
        sourceAngle = lightSys->GetLightSourceAngle(lightComp.Read());

        auto* virtualShadowMapClipmap = shadowSys->GetVirtualShadowMapClipmap(shadowComp.Read(), mRenderPipeline->GetCamera());
        if (virtualShadowMapClipmap != nullptr)
        {
            virtualShadowMapId = virtualShadowMapClipmap->GetShadowMapId();
        }
        break;
    }
    case LightType::Point:
    {
        auto range = lightSys->GetLightRange(lightComp.Read());
        auto range2 = range * range;
        auto& translation = transformSys->GetWorldTranslation(transformComp.Read());
        lightDirPos = Float4(translation, 1.0f);
        lightTilePos = Float4(transformSys->GetTilePosition(tilePositionComp.Read()), 0.0f);
        lightAttenuation = Float4(-1.f, 1.f, 1.0f / range2, range2);
        break;
    }
    case LightType::Spot:
    {
        auto range = lightSys->GetLightRange(lightComp.Read());
        auto range2 = range * range;
        auto spotCosAngle = lightSys->GetLightOuterCosAngle(lightComp.Read());   // wxt-todo
        auto cosHalfAngle = cos(acos(spotCosAngle) * 0.5f);                          // 1.0f / cos(acos(spotCosHalfAngle) * 0.5f);
        auto& translation = transformSys->GetWorldTranslation(transformComp.Read());
        lightDirPos = Float4(translation.x, translation.y, translation.z, 1.0f);
        lightTilePos = Float4(transformSys->GetTilePosition(tilePositionComp.Read()), 0.0f);
        lightSpotDir = Float4(transformSys->GetWorldRotation(transformComp.Read()).Float4Rotate(Float4(0, 0, 1, 0)));
        lightAttenuation = Float4(spotCosAngle, cosHalfAngle, 1.0f / range2, range2);
        break;
    }
    default:
        break;
    }

    return LightData{
        lightDirPos,
        lightTilePos,
        lightAttenuation,
        lightColor,
        lightSpotDir,
        virtualShadowMapId,
        sourceAngle,
        0,
        0,
    };
}

NGIBufferView* ShadowPass::AllocateLightBuffer()
{
    QUICK_SCOPED_CPU_TIMING("ShadowPassAllocateLightsBuffer");
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    std::vector<LightData> lightDatas;
    for (auto lightEntity : mCastShadowLightEntities)
        lightDatas.push_back(GetLightData(lightEntity));

    SizeType lightDataByteSize = sizeof(LightData) * lightDatas.size();

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto lightDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, lightDataByteSize);
    lightDataBufferWrap.MemWrite(0, lightDatas.data(), lightDataByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            lightDataBufferWrap.GetNGIOffset(),
            lightDataByteSize,
            GraphicsFormat::Unknown,
            sizeof(LightData),
        },
        lightDataBufferWrap.GetNGIBuffer());
}

NGIBufferView* ShadowPass::AllocateShadowMatricesBuffer()
{
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    UInt32 shadowMatricesByteSize = static_cast<UInt32>(sizeof(Float4x4) * mShadowMatrices.size());
    UInt32 shadowMatricesBufferSize = std::max(shadowMatricesByteSize, 1u);

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto shadowMatricesBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, shadowMatricesBufferSize);
    shadowMatricesBufferWrap.MemWrite(0, mShadowMatrices.data(), shadowMatricesByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            shadowMatricesBufferWrap.GetNGIOffset(),
            shadowMatricesBufferSize,
            GraphicsFormat::Unknown,
            sizeof(Float4x4),
        },
        shadowMatricesBufferWrap.GetNGIBuffer());
}

NGIBufferView* ShadowPass::AllocateShadowDatasBuffer()
{
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    UInt32 shadowDataByteSize = static_cast<UInt32>(sizeof(ShadowData) * mShadowDatas.size());
    UInt32 shadowDataBufferSize = std::max(shadowDataByteSize, 1u);

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto shadowDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, shadowDataBufferSize);
    shadowDataBufferWrap.MemWrite(0, mShadowDatas.data(), shadowDataByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            shadowDataBufferWrap.GetNGIOffset(),
            shadowDataBufferSize,
            GraphicsFormat::Unknown,
            sizeof(ShadowData),
        },
        shadowDataBufferWrap.GetNGIBuffer());
}

NGIBufferView* ShadowPass::AllocateDirectionalShadowDatasBuffer()
{
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    UInt32 directionalShadowDataByteSize = static_cast<UInt32>(sizeof(DirectionalShadowData) * mDirectionalShadowDatas.size());
    UInt32 directionalShadowDataBufferSize = std::max(directionalShadowDataByteSize, 1u);

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto directionalShadowDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, directionalShadowDataBufferSize);
    directionalShadowDataBufferWrap.MemWrite(0, mDirectionalShadowDatas.data(), directionalShadowDataByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            directionalShadowDataBufferWrap.GetNGIOffset(),
            directionalShadowDataBufferSize,
            GraphicsFormat::Unknown,
            sizeof(DirectionalShadowData),
        },
        directionalShadowDataBufferWrap.GetNGIBuffer());
}

NGIBufferView* ShadowPass::AllocateSpotLightRangesBuffer()
{
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    UInt32 spotRangeDataByteSize = static_cast<UInt32>(sizeof(SubMapInfo) * mSpotLightShadowMapRanges.size());
    UInt32 spotRangeDataBufferSize = std::max(spotRangeDataByteSize, static_cast<UInt32>(sizeof(SubMapInfo)));

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto spotRangeBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, spotRangeDataBufferSize);
    spotRangeBufferWrap.MemWrite(0, mSpotLightShadowMapRanges.data(), spotRangeDataByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            spotRangeBufferWrap.GetNGIOffset(),
            spotRangeDataBufferSize,
            GraphicsFormat::Unknown,
            sizeof(SubMapInfo),
        },
        spotRangeBufferWrap.GetNGIBuffer());
}

NGIBufferView* ShadowPass::AllocatePointLightRangesBuffer()
{
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    UInt32 pointRangeDataByteSize = static_cast<UInt32>(sizeof(SubMapInfo) * mPointLightShadowMapRanges.size());
    UInt32 pointRangeDataBufferSize = std::max(pointRangeDataByteSize, static_cast<UInt32>(sizeof(SubMapInfo)));

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto pointRangeBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, pointRangeDataBufferSize);
    pointRangeBufferWrap.MemWrite(0, mPointLightShadowMapRanges.data(), pointRangeDataByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            pointRangeBufferWrap.GetNGIOffset(),
            pointRangeDataBufferSize,
            GraphicsFormat::Unknown,
            sizeof(SubMapInfo),
        },
        pointRangeBufferWrap.GetNGIBuffer());
}

ShadowData::ShadowData(LightSystemR* lightSys, const LightSystemR::RenderLightComponentReader& lightComp, SInt32 shadowMapIndex, SInt32 shadowMatrixIndex, float normalizeFactor)
{
    this->shadowMapIndex = shadowMapIndex;
    this->matrixIndex = shadowMatrixIndex;
    this->directionalShadowDataIndex = -1;
    this->shadowType = (UInt32)lightSys->GetLightShadowType(lightComp);
    this->normalizeFactor = normalizeFactor;
    this->pcfKernal = lightSys->GetLightFilterSizePCF(lightComp);
    this->pcssSoftness = lightSys->GetLightSoftnessPCSS(lightComp);
    this->pcssSampleCount = lightSys->GetLightSampleCountPCSS(lightComp);
    this->shadowAmount = lightSys->GetLightShadowAmount(lightComp);
}

ShadowData::ShadowData(LightSystemR* lightSys, UInt32 lightIdx, SInt32 shadowMapIndex, SInt32 shadowMatrixIndex, float normalizeFactor)
{
    this->shadowMapIndex = shadowMapIndex;
    this->matrixIndex = shadowMatrixIndex;
    this->directionalShadowDataIndex = -1;
    this->shadowType = (UInt32)lightSys->GetLightShadowType(lightIdx);
    this->normalizeFactor = normalizeFactor;
    this->pcfKernal = lightSys->GetLightFilterSizePCF(lightIdx);
    this->pcssSoftness = lightSys->GetLightSoftnessPCSS(lightIdx);
    this->pcssSampleCount = lightSys->GetLightSampleCountPCSS(lightIdx);
    this->shadowAmount = lightSys->GetLightShadowAmount(lightIdx);
}

}   // namespace cross
