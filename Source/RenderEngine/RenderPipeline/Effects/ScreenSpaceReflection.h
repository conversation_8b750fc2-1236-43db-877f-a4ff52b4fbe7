#pragma once
#include "PassBase.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"

namespace cross {

enum class CEMeta(Reflect, Editor) SSR_DENOISE_METHOD
{
    DISABLE_DENOISER = 0,
    SIMPLE_DENOISER,
    STOCHASTIC_DENOISER,
};

class CEMeta(Editor, Reflect) RENDER_ENGINE_API ScreenSpaceReflectionSetting : public PassSetting
{
public:
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bKeyFrame = true)) bool Glossy{false};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float MaxTraceDistance{100000.f};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float MaxScreenTraceFraction{8.0f};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", ValueMin = "1", ValueMax = "10"))
    int SlopeCompareTolerance{4};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool SeperatedTranslucentPass{false};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool UseFullDepth{true};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) SSR_DENOISE_METHOD EnableDenoiser{SSR_DENOISE_METHOD::SIMPLE_DENOISER};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float StochasticRoughnessThreshold{0.4f};
    CEMeta(Serialize, Editor, Script, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) UInt32 NumRays{1};
    CEMeta(Serialize, Editor, Script, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) UInt32 NumSteps{45};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) UInt32 StartMipLevel{0};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) SInt32 FrameIndex{-1};
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "float", ToolTips = "", bKeyFrame = true)) float ViewRange{0.6f};
    CE_Virtual_Serialize_Deserialize;

    virtual void Initialize() override;
    ScreenSpaceReflectionSetting()
    {
        enable = false;
    }
};

class StochasticSSRTemporalState
{
public:
    ~StochasticSSRTemporalState();

    std::unique_ptr<NGITexture> mRadiance[2]{nullptr};
    std::unique_ptr<NGITexture> mVariance[2]{nullptr};
    std::unique_ptr<NGITexture> mSampleCount[2]{nullptr};

    REDTextureView* mRadianceView[2]{nullptr};
    REDTextureView* mRadianceViewSRV[2]{nullptr};
    REDTextureView* mRadianceViewRT[2]{nullptr};
    REDTextureView* mVarianceView[2]{nullptr};
    REDTextureView* mVarianceViewSRV[2]{nullptr};
    REDTextureView* mSampleCountView[2]{nullptr};
    REDTextureView* mSampleCountViewSRV[2]{nullptr};

    REDTextureRef mNumFramesAccumulatedRT{nullptr};
    REDTextureRef mFastUpdateModeHistoryRT{nullptr};
    REDTextureRef mDefaultR8RT{nullptr};
    REDTextureView* mDefaultR8View{nullptr};

    REDTextureRef mDiffuseIndirectHistoryRT{nullptr};
    REDTextureRef mNormalHistoryRT = nullptr;
    REDTextureRef mDepthHistoryRT = nullptr;

    UInt32 mCurrentIndex{0};
    bool mTexInited{false};

    void IncTextureIndex()
    {
        mCurrentIndex ^= mCurrentIndex;
    }

    void TryCreateNGIResource(RenderingExecutionDescriptor* red, UInt32 width, UInt32 height);
    void DelayDeleteNGIResource();
    void InitTextureView(RenderingExecutionDescriptor* red, ComputeShaderR* clearShader, bool bEnableStochasticDenoiser);
};

class RENDER_ENGINE_API ScreenSpaceReflection : public PassBase<ScreenSpaceReflectionSetting, ScreenSpaceReflection>
{
public:
    MaterialR* mPostMtl = nullptr;
    NGITextureView* mBlackTexture = nullptr;
    NGITextureView* mBlackCubeTexture = nullptr;
    const RenderCamera* mCamera;
    MaterialR* mDeferSSRMtl = nullptr;
    ComputeShaderR* mStochasticReprojectShader = nullptr;
    ComputeShaderR* mStochasticPrefilterShader = nullptr;
    ComputeShaderR* mStochasticResolveTemporalShader = nullptr;
    TextureR* mNoiseTexture = nullptr;
    void InitializeSetting();
    UInt32 GetFrameCountMod8()
    {
        if (mSetting.FrameIndex == -1)
        {
            mFrameCountMod8 = (mFrameCountMod8 + 1) % 8;
            return mFrameCountMod8;
        }
        else
        {
            return mSetting.FrameIndex;
        }
    };
    StochasticSSRTemporalState mTemporalState;

protected:
    bool ExecuteImp(const GameContext& gameContext, std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* sceneViewLastFrame, REDTextureView* sceneDepthStencilView, REDTextureView* depthPyramid, REDTextureView*& ssrView);

    UInt32 mFrameCountMod8 = 0;
    UInt32 mFrameCount{0};

    friend PassBase<ScreenSpaceReflectionSetting, ScreenSpaceReflection>;
};
}   // namespace cross