#include "StellarMeshRenderPipeline.h"
#include "StellarMeshCulling.h"
#include "StellarMeshRasterize.h"
#include "RenderEngine/StellarMesh/StellarMeshSceneSetting.h"
#include "RenderEngine/RenderPipeline/Effects/StellarMesh/StellarMeshDefs.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"

namespace cross {

struct StellarMeshRenderPipeline::Impl
{
    Impl() = default;
    ~Impl() = default;

    StellarMeshCullingContext  mCullingContext{};
    StellarMeshCullingPipeline mCullingPipeline{};
    StellarMeshRasterizePipeline mRasterizePipeline{};
    
    RenderWorld* mRenderWorld;
    FFSWorldRenderPipeline* mWorldRenderPipeline;
    FFSRenderPipeline* mRenderPipeline;
    FFSRenderPipelineSetting const* mRenderPipelineSetting;
    RenderingExecutionDescriptor* mRED;
    StellarMeshRenderPipelineSetting const* mSetting;

    std::tuple<REDBufferRef, REDBufferView*, REDBufferView*> CreateBuffer(std::string_view name, UInt32 elementCount, UInt32 elementBytes, bool isDrawIndirect = false)
    {
        auto indirectUsage = isDrawIndirect ? NGIBufferUsage::IndirectBuffer : NGIBufferUsage{0};
        NGIBufferUsage usage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | indirectUsage;
        REDBufferRef buffer = mRED->AllocateBuffer(name, NGIBufferDesc{elementCount * elementBytes, usage});
        REDBufferView* bufferUAV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});
        REDBufferView* bufferSRV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer | indirectUsage, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});

        return {buffer, bufferUAV, bufferSRV};
    }

    void Initialize(const GameContext& gameContext)
    {
        mRenderWorld = gameContext.mRenderWorld;
        mRenderPipeline = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        mWorldRenderPipeline = static_cast<FFSWorldRenderPipeline*>(mRenderPipeline->GetWorldRenderPipeline());
        mRED = mRenderPipeline->GetRenderingExecutionDescriptor();
        mRenderPipelineSetting = mRenderPipeline->GetSetting();
        mSetting = &(mRenderPipelineSetting->mStellarMeshRenderPipelineSetting);

        std::tie(mCullingContext.mSlotCounterBuffer, mCullingContext.mSlotCounterBufferUAV, mCullingContext.mSlotCounterBufferSRV) = CreateBuffer("SlotCounterBuffer", 1, sizeof(SlotCounter));
        std::tie(mCullingContext.mQueueStateBuffer, mCullingContext.mQueueStateBufferUAV, mCullingContext.mQueueStateBufferSRV) = CreateBuffer("QueueStateBuffer", 1, sizeof(QueueState));
        std::tie(mCullingContext.mCandidateClusterQueueBuffer, mCullingContext.mCandidateClusterQueueBufferUAV, mCullingContext.mCandidateClusterQueueBufferSRV) = CreateBuffer("CandidateClusterQueueBuffer", StellarMeshSceneSetting::MaxClusters, sizeof(InstancedCluster));
        std::tie(mCullingContext.mClusterCullingDrawArgBuffer, mCullingContext.mClusterCullingDrawArgBufferUAV, mCullingContext.mClusterCullingDrawArgBufferSRV) = CreateBuffer("ClusterCullingDrawArgBuffer", 1, sizeof(DispatchIndirectCommand), true);
    }
    
    REDTextureView* AllocateVisbilityBuffer(StellarMeshRenderPipeline& io)
    {
        auto usage = NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst;
        auto format = GraphicsFormat::R32_UInt;
        auto visibilityBuffer =
            mRED->AllocateTexture("VisibilityBuffer", NGITextureDesc{format, NGITextureType::Texture2D, 1, 1, (*io.mInput.gBufferViews)[0]->GetWidth(), (*io.mInput.gBufferViews)[0]->GetHeight(), 1, 1, usage, false, false});

        return mRED->AllocateTextureView(visibilityBuffer,
                                         NGITextureViewDesc{
                                             usage,
                                             format,
                                             NGITextureType::Texture2D,
                                             {NGITextureAspect::Color, 0, 1, 0, 1},
                                         });
    }

    REDTextureView* AllocateTexture(StellarMeshRenderPipeline& io, GraphicsFormat format, std::string_view name)
    {
        auto usage = NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst;
        // auto format = GraphicsFormat::A2R10G10B10_UNormPack32;
        auto tex =
            mRED->AllocateTexture(name, NGITextureDesc{format, NGITextureType::Texture2D, 1, 1, (*io.mInput.gBufferViews)[0]->GetWidth(), (*io.mInput.gBufferViews)[0]->GetHeight(), 1, 1, usage, false, false});

        return mRED->AllocateTextureView(tex,
                                         NGITextureViewDesc{
                                             usage,
                                             format,
                                             NGITextureType::Texture2D,
                                             {NGITextureAspect::Color, 0, 1, 0, 1},
                                         });
    }

    void ExecuteImpl(const GameContext& gameContext, StellarMeshRenderPipeline& io)
    {
        std::tie(mCullingPipeline.mOutput.mVisibleClusterBuffer, mCullingPipeline.mOutput.mVisibleClusterBufferUAV, mCullingPipeline.mOutput.mVisibleClusterBufferSRV) = CreateBuffer("VisibleCluster", StellarMeshSceneSetting::MaxVisibleClusters, sizeof(InstancedCluster));
        std::tie(mCullingPipeline.mOutput.mClusterDrawArgBuffer, mCullingPipeline.mOutput.mClusterDrawArgBufferUAV, mCullingPipeline.mOutput.mClusterDrawArgBufferSRV) = CreateBuffer("ClusterDrawArgBuffer", 1, sizeof(UInt4), true);
        mCullingPipeline.InitArg(gameContext, mCullingContext);
        mCullingPipeline.Execute(gameContext, mCullingContext);

        mRasterizePipeline.mInput.mClusterDrawArgBuffer = mCullingPipeline.mOutput.mClusterDrawArgBuffer;
        mRasterizePipeline.mInput.mClusterDrawArgBufferUAV = mCullingPipeline.mOutput.mClusterDrawArgBufferUAV;
        mRasterizePipeline.mInput.mClusterDrawArgBufferSRV = mCullingPipeline.mOutput.mClusterDrawArgBufferSRV;
        mRasterizePipeline.mInput.mVisibleClusterBuffer = mCullingPipeline.mOutput.mVisibleClusterBuffer;
        mRasterizePipeline.mInput.mVisibleClusterBufferUAV = mCullingPipeline.mOutput.mVisibleClusterBufferUAV;
        mRasterizePipeline.mInput.mVisibleClusterBufferSRV = mCullingPipeline.mOutput.mVisibleClusterBufferSRV;
        mRasterizePipeline.mOutput.mVisibilityBufferView = AllocateTexture(io, GraphicsFormat::R32_UInt, "VisibilityBuffer");
        mRasterizePipeline.mOutput.mDepthStencilView = io.mInput.mDepthStencilView;
        mRasterizePipeline.Execute(gameContext);

        if (mSetting->mStellarMeshDebugSetting.mEnableDebug)
        {
            auto debugTex = AllocateTexture(io, GraphicsFormat::R16G16B16A16_UNorm, "StellarMeshDebugTex");
            gameContext.mRenderPipeline->PostProcess(
                [&](auto pass) {
                    pass->SetProperty(NAME_ID("VISUALIZE_MODE")     , static_cast<int>(mSetting->mStellarMeshDebugSetting.mVisualizeMode));
                    pass->SetProperty(NAME_ID("VisibilityBufferTex"), mRasterizePipeline.mOutput.mVisibilityBufferView                   );
                    pass->SetProperty(NAME_ID("visibleClusters")    , mRasterizePipeline.mInput.mVisibleClusterBufferSRV                 );
                },
                mSetting->StellarMeshVisualizationMaterialR,
                "StellarMeshVisualization",
                true,
                debugTex
            );
            mRenderPipeline->GetViewModeVisualization().SetRenderDebugTex(debugTex);
        }
        
        io.mOutput.mVisibilityBufferView = mRasterizePipeline.mOutput.mVisibilityBufferView;
    }
};

StellarMeshRenderPipeline::StellarMeshRenderPipeline()
    : pImpl(std::make_unique<Impl>())
{}

StellarMeshRenderPipeline::~StellarMeshRenderPipeline() = default;

void StellarMeshRenderPipeline::Execute(const GameContext& gameContext)
{
    //if (static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline)->GetSetting()->mStellarMeshRenderPipelineSetting.enable)
    {
        pImpl->Initialize(gameContext);
        pImpl->ExecuteImpl(gameContext, *this);
    }
}

}
