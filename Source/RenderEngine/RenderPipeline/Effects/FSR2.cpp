#include "FSR2.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "NativeGraphicsInterface/NGIManager.h"

#define FFX_FSR2_DEBUG_OUT 0
#define FFX_CPU 0
#include <fsr2/include/ffx_util.h>
#include <fsr2/include/ffx_types.h>
#include <fsr2/include/ffx_common_types.h>
#include <fsr2/include/ffx_core_cpu.h>
#include <fsr2/include/ffx_fsr2_resources.h>
#include <fsr2/include/ffx_fsr2_common.h>
#include <fsr2/include/ffx_fsr2_maximum_bias.h>
#include <fsr2/include/ffx_fsr2_internal.h>
#include <fsr2/include/ffx_fsr1.h>
#include <RenderEngine/RenderPipeline/FFSRenderPipeline.h>

#pragma warning(disable : 4189)
#pragma warning(disable : 4459)

namespace {
// max queued frames for descriptor management
static constexpr uint32_t FSR2_MAX_QUEUED_FRAMES = 16;

// Fsr2MaxQueuedFrames must be an even number.
static_assert((FSR2_MAX_QUEUED_FRAMES & 1) == 0);

struct PassDescription
{
    const char* name;
    std::vector<std::string_view> SRV;
    std::vector<std::string_view> UAV;
    std::vector<std::string_view> CBV;
};

static std::map<UInt32, PassDescription> FSR2_PASS_DESC = {
    {FFX_FSR2_PASS_PREPARE_INPUT_COLOR,
     {"FFX_FSR2_PASS_PREPARE_INPUT_COLOR",
      {
          "r_input_color_jittered",
          "r_exposure",
      },
      {
          "rw_reconstructed_previous_nearest_depth",
          "rw_prepared_input_color",
          "rw_luma_history",
      },
      {
          "cbFSR2",
      }}},
    {FFX_FSR2_PASS_RECONSTRUCT_PREVIOUS_DEPTH,
     {"FFX_FSR2_PASS_RECONSTRUCT_PREVIOUS_DEPTH",
      {
          "r_motion_vectors",
          "r_depth",
          "r_reactive_mask",
          "r_transparency_and_composition_mask",
          "r_prepared_input_color",
          "r_dilatedDepth",
      },
      {
          "rw_reconstructed_previous_nearest_depth",
          "rw_dilated_motion_vectors",
          "rw_dilatedDepth",
          "rw_dilated_reactive_masks",
      },
      {
          "cbFSR2",
      }}},
    {FFX_FSR2_PASS_DEPTH_CLIP,
     {"FFX_FSR2_PASS_DEPTH_CLIP",
      {
          "r_reconstructed_previous_nearest_depth",
          "r_dilated_motion_vectors",
          "r_dilatedDepth",
      },
      {
          "rw_depth_clip",
      },
      {
          "cbFSR2",
      }}},
    {FFX_FSR2_PASS_LOCK,
     {"FFX_FSR2_PASS_LOCK",
      {
          "r_reactive_mask",
          "r_lock_status",
          "r_prepared_input_color",
      },
      {
          "rw_lock_status",
          "rw_dilated_reactive_masks",
      },
      {
          "cbFSR2",
      }}},
    {FFX_FSR2_PASS_ACCUMULATE,
     {"FFX_FSR2_PASS_ACCUMULATE",
      {
          "r_exposure",
          "r_transparency_and_composition_mask",
          "r_dilated_motion_vectors",
          "r_dilatedDepth",
          "r_internal_upscaled_color",
          "r_lock_status",
          "r_depth_clip",
          "r_prepared_input_color",
          "r_luma_history",
          //"r_lanczos_lut",
          "r_dilated_reactive_masks",
          "r_imgMips",
          "r_input_separate_translucency",
          "r_separate_translucency",
          "r_anti_flickering_mask",
      },
      {
          "rw_internal_upscaled_color",
          "rw_lock_status",
          "rw_upscaled_output",
          "rw_separate_translucency",
      },
      {
          "cbFSR2",
      }}},
    {FFX_FSR2_PASS_RCAS,
     {"FFX_FSR2_PASS_RCAS",
      {
          "r_exposure",
          "r_rcas_input",
      },
      {
          "rw_upscaled_output",
          "rw_separate_translucency",
      },
      {
          "cbFSR2",
          "cbRCAS",
      }}},
    {FFX_FSR2_PASS_COMPUTE_LUMINANCE_PYRAMID,
     {"FFX_FSR2_PASS_COMPUTE_LUMINANCE_PYRAMID",
      {
          "r_input_color_jittered",
      },
      {
          //"rw_lock_status",
          "rw_spd_global_atomic",
          "rw_img_mip_shading_change",
          "rw_img_mip_5",
          "rw_exposure",
      },
      {
          "cbFSR2",
          "cbSPD",
      }}},
};

static std::map<std::string_view, UInt32> FSR2_SRV_RESOURCE_BINDING = {
    {"r_input_color_jittered", FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_COLOR},
    {"r_motion_vectors", FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_MOTION_VECTORS},
    {"r_depth", FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_DEPTH},
    {"r_exposure", FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_EXPOSURE},
    {"r_reactive_mask", FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_REACTIVE_MASK},
    {"r_transparency_and_composition_mask", FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_TRANSPARENCY_AND_COMPOSITION_MASK},
    {"r_reconstructed_previous_nearest_depth", FFX_FSR2_RESOURCE_IDENTIFIER_RECONSTRUCTED_PREVIOUS_NEAREST_DEPTH},
    {"r_dilated_motion_vectors", FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_MOTION_VECTORS},
    {"r_dilatedDepth", FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_DEPTH},
    {"r_internal_upscaled_color", FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR},
    {"r_lock_status", FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS},
    {"r_depth_clip", FFX_FSR2_RESOURCE_IDENTIFIER_DEPTH_CLIP},
    {"r_prepared_input_color", FFX_FSR2_RESOURCE_IDENTIFIER_PREPARED_INPUT_COLOR},
    {"r_luma_history", FFX_FSR2_RESOURCE_IDENTIFIER_LUMA_HISTORY},
    {"r_rcas_input", FFX_FSR2_RESOURCE_IDENTIFIER_RCAS_INPUT},
    {"r_lanczos_lut", FFX_FSR2_RESOURCE_IDENTIFIER_LANCZOS_LUT},
    {"r_imgMips", FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE},
    {"r_img_mip_shading_change", FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP_SHADING_CHANGE},
    {"r_img_mip_5", FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP_5},
    {"r_upsample_maximum_bias_lut", FFX_FSR2_RESOURCE_IDENTITIER_UPSAMPLE_MAXIMUM_BIAS_LUT},
    {"r_dilated_reactive_masks", FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_REACTIVE_MASKS},
    {"r_input_separate_translucency", FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_SEPARATE_TRANSLUCENCY},
    {"r_separate_translucency", FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY},
    {"r_anti_flickering_mask", FFX_FSR2_RESOURCE_IDENTIFIER_ANTI_FLICKERING_MASK},

};

static std::map<std::string_view, UInt32> FSR2_UAV_RESOURCE_BINDING = {
    {"rw_reconstructed_previous_nearest_depth", FFX_FSR2_RESOURCE_IDENTIFIER_RECONSTRUCTED_PREVIOUS_NEAREST_DEPTH},
    {"rw_dilated_motion_vectors", FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_MOTION_VECTORS},
    {"rw_dilatedDepth", FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_DEPTH},
    {"rw_internal_upscaled_color", FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR},
    {"rw_lock_status", FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS},
    {"rw_depth_clip", FFX_FSR2_RESOURCE_IDENTIFIER_DEPTH_CLIP},
    {"rw_prepared_input_color", FFX_FSR2_RESOURCE_IDENTIFIER_PREPARED_INPUT_COLOR},
    {"rw_luma_history", FFX_FSR2_RESOURCE_IDENTIFIER_LUMA_HISTORY},
    {"rw_upscaled_output", FFX_FSR2_RESOURCE_IDENTIFIER_UPSCALED_OUTPUT},
    {"rw_img_mip_shading_change", FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP_SHADING_CHANGE},
    {"rw_img_mip_5", FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP_5},
    {"rw_dilated_reactive_masks", FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_REACTIVE_MASKS},
    {"rw_exposure", FFX_FSR2_RESOURCE_IDENTIFIER_EXPOSURE},
    {"rw_spd_global_atomic", FFX_FSR2_RESOURCE_IDENTIFIER_SPD_ATOMIC_COUNT},
    {"rw_separate_translucency", FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY},
};

}   // namespace

namespace cross 
{
void FSR2Input::GenerateInputData(const GameContext& gameContext)
{
    frameCount = gameContext.mRenderPipeline->GetFrameCount();
    jitter = gameContext.mRenderPipeline->GetJitterData()->jitter;
    displaySize = UInt2{gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayColor>()->GetWidth(), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayColor>()->GetHeight()};
    projection = gameContext.mRenderPipeline->GetJitterData()->jitterProjMat;
    camera = gameContext.mRenderPipeline->GetRenderCamera();

    color = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
    depth = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>();
    auto gbuffer = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::GBufferViews>();
    vbuffer = gbuffer[3];
    exposure = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameEyeAdpatationTex>();
    reactive = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::FSRReactiveMaskView>();
    transparentReactive = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::FSRTransparentReactiveMaskView>();
    separateTranslucency = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>();
    // For anti-flickering mask gen
    cloudColor = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline)->GetCloudResources().mCloudColorTex;
    auto renderSize = UInt2{color->GetWidth(), color->GetHeight()};
    antiFlickeringMask = IRenderPipeline::CreateTextureView2D("Anti-flickering Mask", renderSize.x, renderSize.y, color->mTexture->mDesc.Format,
                                                              NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess);
}

void FSR2Pass::FillInput(const GameContext& gameContext)
{
    bool bCopyFromFrameDepth = true;
    if (bCopyFromFrameDepth)
    {
        auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        UInt32 DepthStencilWidth = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Width;
        UInt32 DepthStencilHeight = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Height;

        auto format = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Format;
        NGITextureDesc depthStencilDesc{
            format,
            NGITextureType::Texture2D,
            1,
            1,
            DepthStencilWidth,
            DepthStencilHeight,
            1,
            1,
            NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
        };
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>() = RED->AllocateTexture("LastFrameDepthStencil", depthStencilDesc);
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>()->ExtendLifetime();

        NGICopyTexture region1 = {0};
        region1.SrcSubresource = NGICalcSubresource(0, 0, 0, 1, 1);
        region1.SrcOffset = NGIOffset3D{0, 0, 0};
        region1.DstSubresource = NGICalcSubresource(0, 0, 0, 1, 1);
        region1.DstOffset = NGIOffset3D{0, 0, 0};
        region1.Extent = NGIExtent3D{DepthStencilWidth, DepthStencilHeight, 1};

        auto* pass = RED->AllocatePass("CopyDepthStencil");
        pass->CopyTextureToTexture(gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>(), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>(), 1, &region1);
    }
    else
    {
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->ExtendLifetime();
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>() = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>();
    }
    auto ffspipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    if (!ffspipeline)
        return;
    mSetting = ffspipeline->GetSetting()->mFSR2Setting;
    mPassContext = &ffspipeline->mFSR2PassContext;
    mInput.GenerateInputData(gameContext);
}

NGITextureUsage getUsageFlagsFromResourceUsage(FfxResourceUsage flags)
{
    NGITextureUsage ret = NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst;
    if (flags & FFX_RESOURCE_USAGE_RENDERTARGET)
        ret |= NGITextureUsage::RenderTarget;
    if (flags & FFX_RESOURCE_USAGE_UAV)
        ret |= (NGITextureUsage::UnorderedAccess | NGITextureUsage::CopySrc);
    return ret;
}

GraphicsFormat getFormatFromSurfaceFormat(FfxSurfaceFormat fmt)
{
    switch (fmt)
    {
    case (FFX_SURFACE_FORMAT_R32G32B32A32_TYPELESS):
        return GraphicsFormat::R32G32B32A32_SFloat;
    case (FFX_SURFACE_FORMAT_R32G32B32A32_FLOAT):
        return GraphicsFormat::R32G32B32A32_SFloat;
    case (FFX_SURFACE_FORMAT_R16G16B16A16_UNORM):
        return GraphicsFormat::R16G16B16A16_UNorm;
    case (FFX_SURFACE_FORMAT_R16G16B16A16_FLOAT):
        return GraphicsFormat::R16G16B16A16_SFloat;
    case (FFX_SURFACE_FORMAT_R32G32_FLOAT):
        return GraphicsFormat::R32G32_SFloat;
    case (FFX_SURFACE_FORMAT_R32_UINT):
        return GraphicsFormat::R32_UInt;
    case (FFX_SURFACE_FORMAT_R8G8B8A8_TYPELESS):
        return GraphicsFormat::R8G8B8A8_UNorm;
    case (FFX_SURFACE_FORMAT_R8G8B8A8_UNORM):
        return GraphicsFormat::R8G8B8A8_UNorm;
    case (FFX_SURFACE_FORMAT_R11G11B10_FLOAT):
        return GraphicsFormat::R11G11B10_UFloatPack32;
    case (FFX_SURFACE_FORMAT_R16G16_FLOAT):
        return GraphicsFormat::R16G16_SFloat;
    case (FFX_SURFACE_FORMAT_R16G16_UINT):
        return GraphicsFormat::R16G16_UInt;
    case (FFX_SURFACE_FORMAT_R16_FLOAT):
        return GraphicsFormat::R16_SFloat;
    case (FFX_SURFACE_FORMAT_R16_UINT):
        return GraphicsFormat::R16_UInt;
    case (FFX_SURFACE_FORMAT_R16_UNORM):
        return GraphicsFormat::R16_UNorm;
    case (FFX_SURFACE_FORMAT_R16_SNORM):
        return GraphicsFormat::R16_SNorm;
    case (FFX_SURFACE_FORMAT_R8_UNORM):
        return GraphicsFormat::R8_UNorm;
    case (FFX_SURFACE_FORMAT_R8G8_UNORM):
        return GraphicsFormat::R8G8_UNorm;
    case (FFX_SURFACE_FORMAT_R32_FLOAT):
        return GraphicsFormat::R32_SFloat;
    case (FFX_SURFACE_FORMAT_UNKNOWN):
    default:
        return GraphicsFormat::Unknown;
    }
}

UInt32 ComputeRowPitch(FfxSurfaceFormat fmt, UInt32 width)
{
    switch (fmt)
    {
    case (FFX_SURFACE_FORMAT_R32G32B32A32_TYPELESS):
    case (FFX_SURFACE_FORMAT_R32G32B32A32_FLOAT):
        return width * 16;
    case (FFX_SURFACE_FORMAT_R16G16B16A16_UNORM):
    case (FFX_SURFACE_FORMAT_R16G16B16A16_FLOAT):
    case (FFX_SURFACE_FORMAT_R32G32_FLOAT):
        return width * 8;
    case (FFX_SURFACE_FORMAT_R32_FLOAT):
    case (FFX_SURFACE_FORMAT_R32_UINT):
    case (FFX_SURFACE_FORMAT_R8G8B8A8_TYPELESS):
    case (FFX_SURFACE_FORMAT_R8G8B8A8_UNORM):
    case (FFX_SURFACE_FORMAT_R11G11B10_FLOAT):
    case (FFX_SURFACE_FORMAT_R16G16_FLOAT):
    case (FFX_SURFACE_FORMAT_R16G16_UINT):
        return width * 4;
    case (FFX_SURFACE_FORMAT_R16_FLOAT):
    case (FFX_SURFACE_FORMAT_R16_UINT):
    case (FFX_SURFACE_FORMAT_R16_UNORM):
    case (FFX_SURFACE_FORMAT_R16_SNORM):
    case (FFX_SURFACE_FORMAT_R8G8_UNORM):
        return width * 2;
    case (FFX_SURFACE_FORMAT_R8_UNORM):
        return width;
    case (FFX_SURFACE_FORMAT_UNKNOWN):
    default:
        Assert(0);
        return width;
    }
}

NGITextureDesc GetTexture2DDesc(GraphicsFormat format, UInt32 width, UInt32 height, NGITextureUsage usage, UInt16 mipCount = 1)
{
    return NGITextureDesc{format, NGITextureType::Texture2D, mipCount, 1, width, height, 1, 1, usage};
}

cross::REDTextureRef AllocateTexture(RenderingExecutionDescriptor* RED, std::string_view name, NGITextureUsage usage, GraphicsFormat format, UInt32 width, UInt32 height, UInt16 mipCount = 1)
{
    REDTextureRef newTex = RED->AllocateTexture(name, GetTexture2DDesc(format, width, height, usage, mipCount));
    newTex->ExtendLifetime();
    Assert(newTex);
    return newTex;
}

NGITextureViewDesc GetTexture2DViewDesc(NGITextureUsage usage, GraphicsFormat format, NGITextureSubRange range)
{
    return NGITextureViewDesc{usage, format, NGITextureType::Texture2D, range};
}

cross::REDTextureView* AllocateTextureView(RenderingExecutionDescriptor* RED, REDTextureRef texture, NGITextureUsage usage, GraphicsFormat format, NGITextureSubRange range)
{
    REDTextureView* newTex = RED->AllocateTextureView(texture, GetTexture2DViewDesc(usage, format, range));
    Assert(newTex);
    return newTex;
}

void ClearTexture(RenderingExecutionDescriptor* RED, REDTextureView* texture, NGIClearValue color)
{
    RED->AllocatePass("Clear " + texture->mTexture->GetName(), true)->ClearTexture(texture, color);
}

void FSR2Setting::Initialize()
{
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_Accumulate);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_AutogenReactive);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_ComputeLuminancePyramid);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_DepthClip);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_Lock);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_PrepareInputColor);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_RCAS);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FSR2_ReconstructPreviousDepth);
}

FSR2PassContext::FSR2PassContext()
{
    params.reset = true;
    context.firstExecution = true;
    SRV.resize(FFX_FSR2_RESOURCE_IDENTIFIER_COUNT);
    UAV.resize(FFX_FSR2_RESOURCE_IDENTIFIER_COUNT);
    mResource.resize(FFX_FSR2_RESOURCE_IDENTIFIER_COUNT);
    s_PointClamp.reset(GetNGIDevicePtr()->CreateSampler({
        NGIFilter::MinMagMipPoint,
        0,
        NGITextureAddressMode::Clamp,
        NGITextureAddressMode::Clamp,
        NGITextureAddressMode::Clamp,
        1,
        NGIComparisonOp::Unknown,
        NGIBorderColor::FloatTransparentBlack,
        -1000,
        1000,
    }));

    s_LinearClamp.reset(GetNGIDevicePtr()->CreateSampler({
        NGIFilter::MinMagLinearMipPoint,
        0,
        NGITextureAddressMode::Clamp,
        NGITextureAddressMode::Clamp,
        NGITextureAddressMode::Clamp,
        1,
        NGIComparisonOp::Unknown,
        NGIBorderColor::FloatTransparentBlack,
        -1000,
        1000,
    }));
}

void FSR2Pass::Setup(const GameContext& gameContext, FSR2Input& input)
{
    auto renderSize = gameContext.mRenderPipeline->GetRenderSize();
    auto displaySize = gameContext.mRenderPipeline->GetDisplaySize();

    UInt32 renderWidth = renderSize.x;
    UInt32 renderHeight = renderSize.y;
    UInt32 displayWidth = displaySize.x;
    UInt32 displayHeight = displaySize.y;

    bool rebuildInternalResource = false;
    {
        if (renderWidth > mPassContext->context.maxRenderSize.x || renderHeight > mPassContext->context.maxRenderSize.y || displayWidth != mPassContext->context.displaySize.x || displayHeight != mPassContext->context.displaySize.y)
        {
            mPassContext->params.reset = true;
            rebuildInternalResource = true;
        }
        else if (input.frameCount != mPassContext->previousFrameCount + 1)
        {
            rebuildInternalResource = true;
        }
        else
        {
            auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

            for (auto tex : mPassContext->mResource)
            {
                if (tex && !red->Validate(tex))
                {
                    rebuildInternalResource = true;
                    break;
                }
            }
        }
    }

    mPassContext->context.displaySize = {displayWidth, displayHeight};
    mPassContext->context.maxRenderSize = {renderWidth, renderHeight};

    mPassContext->params.renderSize = {renderWidth, renderHeight};
    mPassContext->params.motionVectorScale.x = static_cast<float>(renderWidth);
    mPassContext->params.motionVectorScale.y = static_cast<float>(renderHeight);
    mPassContext->params.sharpness = MathUtils::Clamp(mSetting.SharpeningValue, 0.f, 1.0f);
    mPassContext->params.preExposure = mSetting.AutoExposure ? mSetting.PreExposure : 1.0f;
    mPassContext->params.frameTimeDelta = EngineGlobal::Inst().GetRenderEngine()->GetAverageFrameTime() * 1000;   // ms
    mPassContext->params.jitterOffset = input.jitter;
    mPassContext->params.cameraNear = gameContext.mRenderCamera->GetNearPlane();
    mPassContext->params.cameraFar = gameContext.mRenderCamera->GetFarPlane();
    mPassContext->params.cameraFovAngleVertical = gameContext.mRenderCamera->GetFOV();

    enableSharpen = mSetting.Sharpening && mPassContext->params.sharpness > 0;
    // isCameraJumpCut = mSetting.IsCameraJumpCut(input.camera);

    if (rebuildInternalResource)
    {
        BuildInteralResource(gameContext);
    }
    else
    {
        ExtendInteralResource(gameContext);
    }

    BuildTextureView(gameContext);
}

void FSR2Pass::Execute(const GameContext& gameContext)
{
    auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    red->BeginRegion("FSR2");
    if (mSetting.enable == false)
    {
        mOutput.outputcolor = mInput.color;
    }
    if (mSetting.enable && mSetting.MiscFactors.DebugLowResolution)
    {
        AssembleJitterCancel(gameContext, mSetting, mInput.color, mOutput.outputcolor);
        AssembleJitterCancel(gameContext, mSetting, mInput.depth, gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>());
    }
    else if (mSetting.enable)
    {
        Setup(gameContext, mInput);
        if (mPassContext->previousVersion != mSetting.ResetVersion)
        {
            mPassContext->params.reset = true;
            mPassContext->previousVersion = mSetting.ResetVersion;
        }

        Assert(mInput.color && mInput.depth);

        if (!mOutput.outputcolor)
        {
            const auto& desc = mInput.color->mTexture->mDesc;
            const auto size = gameContext.mRenderPipeline->GetDisplaySize();
            mOutput.outputcolor =
                gameContext.mRenderPipeline->CreateTextureView2D("FSR2 Upscale Color", size.x, size.y, desc.Format, desc.Usage | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess);
        }

        if (!gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>())
        {
            const auto& desc = mInput.depth->mTexture->mDesc;
            const auto size = gameContext.mRenderPipeline->GetDisplaySize();
            gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>() =
                gameContext.mRenderPipeline->CreateTextureView2D("FSR2 Upscale Depth", size.x, size.y, desc.Format, desc.Usage | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess);
        }

        const UInt2 renderSize = gameContext.mRenderPipeline->GetRenderSize();

        // Merge Reactive
        REDTextureView* reactive = mInput.transparentReactive ? mInput.transparentReactive : mInput.reactive;
        if (mInput.reactive && mInput.transparentReactive)
        {
            auto* pass = red->AllocatePass("FFX_FSR2_PASS_MERGE_REACTIVE");
            pass->SetProperty(NAME_ID("ReactiveTexture"), mInput.reactive, NGIResourceState::ComputeShaderShaderResource);
            pass->SetProperty(NAME_ID("RWReactiveTexture"), mInput.transparentReactive, NGIResourceState::ComputeShaderUnorderedAccess);

            auto x = 0u, y = 0u, z = 0u;
            mSetting.FSR2_ReconstructPreviousDepthR->GetThreadGroupSize("MergeReactiveCS", x, y, z);
            pass->Dispatch(mSetting.FSR2_ReconstructPreviousDepthR, "MergeReactiveCS", (renderSize.x + x - 1) / x, (renderSize.y + y - 1) / y, z);
        }

        // Anti-flickering mask generation
        if (true)
        {
            auto* pass = red->AllocatePass("FFX_FSR2_ANTI_FLICKERING_MASK_GEN");
            pass->SetProperty(NAME_ID("ce_Scene_Depth"), mInput.depth);
            pass->SetProperty(NAME_ID("RWAntiFlickeringMask"), mInput.antiFlickeringMask);
            pass->SetProperty(NAME_ID("ENABLE_CLOUD"), mInput.cloudColor != nullptr);
            if (mInput.cloudColor)
            {
                pass->SetProperty(NAME_ID("CloudColor"), mInput.cloudColor);
            }

            auto x = 0u, y = 0u, z = 0u;
            mSetting.FSR2_ReconstructPreviousDepthR->GetThreadGroupSize("AntiFlickeringMaskGenCS", x, y, z);
            pass->Dispatch(mSetting.FSR2_ReconstructPreviousDepthR, "AntiFlickeringMaskGenCS", (renderSize.x + x - 1) / x, (renderSize.y + y - 1) / y, z);

            mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_ANTI_FLICKERING_MASK] = mInput.antiFlickeringMask;
        } else
        {
            mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_ANTI_FLICKERING_MASK] = mInput.depth;
        }
        
        
        // Convert Velocity
        REDTextureView* velocity = nullptr;
        {
            velocity = gameContext.mRenderPipeline->CreateTextureView2D("FSR2 Velocity", renderSize.x, renderSize.y, GraphicsFormat::R16G16_SFloat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst);
            ClearTexture(red, velocity, {0.f, 0.f, 0.f, 0.f});

            const auto jitterData = gameContext.mRenderPipeline->GetJitterData();
            auto* pass = red->AllocatePass("FFX_FSR2_PASS_CONVERT_VELOCITY");
            pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_USE_MOTION_VECTORS"), !!mInput.vbuffer && mSetting.MotionVectors);
            pass->SetProperty(NAME_ID("ViewSize"), renderSize);
            pass->SetProperty(NAME_ID("ViewInvSize"), Float2(1.0f / renderSize.x, 1.0f / renderSize.y));
            pass->SetProperty(NAME_ID("fReprojectionMat"), jitterData->GetReprojectionMatrixNoJitter());
            pass->SetProperty(NAME_ID("InputDepth"), mInput.depth, NGIResourceState::ComputeShaderShaderResource);
            pass->SetProperty(NAME_ID("InputVelocity"), mInput.vbuffer ? mInput.vbuffer : mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_REACTIVITY], NGIResourceState::ComputeShaderShaderResource);
            pass->SetProperty(NAME_ID("OutputTexture"), velocity, NGIResourceState::ComputeShaderUnorderedAccess);

#if FFX_FSR2_DEBUG_OUT
            pass->SetProperty(NAME_ID("rw_debug_out"), mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_DEBUG_OUTPUT], NGIResourceState::ComputeShaderUnorderedAccess);
#endif
            auto x = 0u, y = 0u, z = 0u;
            mSetting.FSR2_ReconstructPreviousDepthR->GetThreadGroupSize("ConvertVelocityCS", x, y, z);
            pass->Dispatch(mSetting.FSR2_ReconstructPreviousDepthR, "ConvertVelocityCS", (renderSize.x + x - 1) / x, (renderSize.y + y - 1) / y, z);
        }

        // Update Input Resource
        mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_COLOR] = mInput.color;
        mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_DEPTH] = mInput.depth;
        mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_SEPARATE_TRANSLUCENCY] = mInput.separateTranslucency ? mInput.separateTranslucency : mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_REACTIVITY];
        mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_MOTION_VECTORS] = velocity ? velocity : mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_REACTIVITY];
        mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_REACTIVE_MASK] = reactive ? reactive : mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_REACTIVITY];
        mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_TRANSPARENCY_AND_COMPOSITION_MASK] = mInput.transparencyAndComposite ? mInput.transparencyAndComposite : mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_REACTIVITY];
        mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_UPSCALED_OUTPUT] = mOutput.outputcolor;
        

#if FFX_FSR2_DEBUG_OUT
        if (mSetting.MiscFactors.DebugOutput)
            mOutput.outputcolor = mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_DEBUG_OUTPUT];
#endif   // FFX_FSR2_DEBUG_OUT

        // if auto exposure is enabled use the auto exposure SRV, otherwise what the app sends.
        if (mSetting.AutoExposure)
        {
            mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_EXPOSURE] = mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_EXPOSURE];
        }
        else
        {
            mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_EXPOSURE] = mInput.exposure ? mInput.exposure : mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_EXPOSURE];
        }

        if (mPassContext->params.reset)
        {
            mPassContext->context.resourceFrameIndex = 0;
            NGIClearValue clearValuesToZeroFloat({0.f, 0.f, 0.f, 0.f});
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_1], clearValuesToZeroFloat);
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_2], clearValuesToZeroFloat);
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_MOTION_VECTORS], clearValuesToZeroFloat);
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_PREPARED_INPUT_COLOR], clearValuesToZeroFloat);
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_LUMA_HISTORY], clearValuesToZeroFloat);
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_DEPTH_CLIP], clearValuesToZeroFloat);
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_REACTIVE_MASKS], clearValuesToZeroFloat);
        }

        mPassContext->cbFSR2.deviceToViewDepth.x = mInput.projection.m22;
        mPassContext->cbFSR2.deviceToViewDepth.y = mInput.projection.m23;   // 1.0f;
        mPassContext->cbFSR2.deviceToViewDepth.z = mInput.projection.m32;
        mPassContext->cbFSR2.deviceToViewDepth.w = FLT_EPSILON;

        

        Assemble(gameContext, mInput);

        AssembleJitterCancel(gameContext, mSetting, mSetting.DepthDilateUpsample ? mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_DEPTH] : 
            mInput.depth, gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>());

        if (!mSetting.DepthReconstruct)
        {
            auto* input_depth = mSetting.DepthUseLastFrame ? mInput.depth : gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>();
            NGITextureViewDesc desc = input_depth->mDesc;
            desc.Usage = NGITextureUsage::ShaderResource;
            desc.SubRange.Aspect = NGITextureAspect::Depth;
            auto* lastFrameDepth = red->AllocateTextureView(input_depth->mTexture, desc);
            AssembleJitterCancel(gameContext, mSetting, lastFrameDepth, mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_DEPTH], true);
        }

        mPassContext->previousFrameCount = mInput.frameCount;
        mPassContext->params.reset = false;
        mPassContext->context.firstExecution = false;
        mPassContext->context.resourceFrameIndex = (mPassContext->context.resourceFrameIndex + 1) % FSR2_MAX_QUEUED_FRAMES;
    }
    //auto ffspipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    ////if (!ffspipeline)
    ////    return;

    //if ((!mSetting.enable || !mSetting.SeparateTranslucency) && !EngineGlobal().GetSettingMgr()->GetEnableDLSS() && !ffspipeline->GetSetting()->mFSR3Setting.enable)
    //{
    //    NGIClearValue clearValue{{0, 0, 0, 0}};
    //    const auto& desc = mOutput.outputcolor->mTexture->mDesc;
    //    const auto size = gameContext.mRenderPipeline->GetDisplaySize();
    //    REDColorTargetDesc renderTarget{mOutput.outputcolor, NGILoadOp::Load, NGIStoreOp::Store, clearValue};
    //    NGIRenderPassTargetIndex renderTargetIndex = static_cast<NGIRenderPassTargetIndex>(0);

    //    gameContext.mRenderPipeline->GetRenderingExecutionDescriptor()->BeginRenderPass("FFX_FSR2_BLEND_TRANSLUCENCY", 1, &renderTarget, nullptr);
    //    auto pass = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor()->AllocateSubRenderPass("FFX_FSR2_BLEND_TRANSLUCENCY", 0, nullptr, 1, &renderTargetIndex, REDPassFlagBit{0});
    //    pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_JITTER_CANCEL"), true);
    //    pass->SetProperty(NAME_ID("fUVJitterOffset"), gameContext.mRenderPipeline->GetJitterData()->GetOffsetInUVSpace());
    //    pass->SetProperty(NAME_ID("r_separate_translucency"), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>(), NGIResourceState::PixelShaderShaderResource);

    //    REDDrawScreenQuad drawInfo{gameContext.mRenderPipeline->GetPostProcessMtl(), "ffx_fsr2_blend_translucency"};
    //    auto blendStateDesc = NGIBlendStateDesc{
    //        false,
    //        false,
    //        1
    //    };
    //    blendStateDesc.TargetBlendState[0] = {
    //        true,
    //        false,
    //        BlendFactor::One,
    //        BlendFactor::SrcAlpha,
    //        NGIBlendOp::Add,
    //        BlendFactor::Zero,
    //        BlendFactor::SrcAlpha,
    //        NGIBlendOp::Add,
    //        static_cast<LogicOp>(0),
    //        ColorMask::All
    //    };
    //    drawInfo.BlendStateDesc = blendStateDesc;
    //    pass->DrawScreenQuad(drawInfo);
    //    gameContext.mRenderPipeline->GetRenderingExecutionDescriptor()->EndRenderPass();
    //}
    red->EndRegion();
    mOutput.SetOutputData(gameContext);
}

void FSR2Pass::AssembleReactive(const GameContext& gameContext, REDTextureView* input_weight, REDTextureView* stencil, REDTextureView*& reactive)
{
    // if (!mSetting.ReactiveParams.enable || !input_weight || !stencil)
    //     return;

    // const auto width = input_weight->GetWidth(), height = input_weight->GetHeight();

    // if (!reactive)
    //     reactive = gameContext.mRenderPipeline->CreateTextureView2D("FSR2 Reactive Internal", width, height, GraphicsFormat::R8G8_UNorm, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess);

    // auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    // auto* pass = red->AllocatePass("FFX_FSR2_PASS_GENERATE_REACTIVE");
    // UInt32 StencilMask = static_cast<UInt32>(mSetting.ReactiveParams.StencilMask);
    // pass->SetProperty(NAME_ID("StencilMask"), StencilMask);
    // pass->SetProperty(NAME_ID("StencilOverrideValue"), mSetting.ReactiveParams.StencilOverride);
    // pass->SetProperty(NAME_ID("r_input_weight_mask"), input_weight, NGIResourceState::ComputeShaderShaderResource);
    // pass->SetProperty(NAME_ID("r_transparent_stencil"), stencil, NGIResourceState::ComputeShaderShaderResource);
    // pass->SetProperty(NAME_ID("rw_output_reactive_mask"), reactive, NGIResourceState::ComputeShaderUnorderedAccess);

    // auto x = 0u, y = 0u, z = 0u;
    // mSetting.FSR2_AutogenReactiveR->GetThreadGroupSize("CS", x, y, z);
    // pass->Dispatch(mSetting.FSR2_AutogenReactiveR, "CS", (width + x - 1) / x, (height + y - 1) / y, z);
}

void FSR2Pass::AssembleJitterCancel(const GameContext& gameContext, FSR2Setting& setting, REDTextureView* input, REDTextureView*& output, bool noCulling)
{
    Assert(input);

    if (!output)
    {
        const auto& desc = input->mTexture->mDesc;
        const auto size = gameContext.mRenderPipeline->GetDisplaySize();   // display size by default
        output = gameContext.mRenderPipeline->CreateTextureView2D("FSR2 Jitter Cancel Output", size.x, size.y, desc.Format, desc.Usage);
    }

    REDPass* pass = nullptr;
    NGIClearValue clearValue{{0, 0, 0, 0}};

    auto* RED = gameContext.mRenderPipeline->RED();
    if (FormatHasDepth(output->mTexture->mDesc.Format))
    {
        clearValue.depthStencil = {setting.DepthInverted ? 0.f : 1.f, 0};
        REDDepthStencilTargetDesc depthStencilTarget{output, NGILoadOp::Clear, NGIStoreOp::Store, NGILoadOp::DontCare, NGIStoreOp::DontCare, clearValue};

        RED->BeginRenderPass("FFX_FSR2_DEPTH_JITTER_CANCEL", 0, nullptr, &depthStencilTarget, noCulling);
        pass = RED->AllocateSubRenderPass("FFX_FSR2_DEPTH_JITTER_CANCEL", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);
        pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_UPSCALE_SPATIAL_DEPTH"), true);
    }
    else
    {
        REDColorTargetDesc renderTarget{output, NGILoadOp::Clear, NGIStoreOp::Store, clearValue};
        NGIRenderPassTargetIndex renderTargetIndex = static_cast<NGIRenderPassTargetIndex>(0);

        RED->BeginRenderPass("FFX_FSR2_COLOR_JITTER_CANCEL", 1, &renderTarget, nullptr, noCulling);
        pass = RED->AllocateSubRenderPass("FFX_FSR2_COLOR_JITTER_CANCEL", 0, nullptr, 1, &renderTargetIndex, REDPassFlagBit{0});
        pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_UPSCALE_SPATIAL_DEPTH"), false);
    }

    pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_JITTER_CANCEL"), true);
    pass->SetProperty(NAME_ID("r_input"), input, NGIResourceState::PixelShaderShaderResource);
    pass->SetProperty(NAME_ID("fUVJitterOffset"), gameContext.mRenderPipeline->GetJitterData()->GetOffsetInUVSpace());

    REDDrawScreenQuad drawInfo{gameContext.mRenderPipeline->GetPostProcessMtl(), "ffx_fsr2_spatial_scale"};
    pass->DrawScreenQuad(drawInfo);
    RED->EndRenderPass();
}

void FSR2Pass::Assemble(const GameContext& gameContext, FSR2Input& input)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

    // Prepare per frame descriptor tables
    const bool isOddFrame = !!(mPassContext->context.resourceFrameIndex & 1);
    const uint32_t currentCpuOnlyTableBase = isOddFrame ? FFX_FSR2_RESOURCE_IDENTIFIER_COUNT : 0;
    const uint32_t currentGpuTableBase = 2 * FFX_FSR2_RESOURCE_IDENTIFIER_COUNT * mPassContext->context.resourceFrameIndex;
    const uint32_t lockStatusSrvResourceIndex = isOddFrame ? FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_2 : FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_1;
    const uint32_t lockStatusUavResourceIndex = isOddFrame ? FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_1 : FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_2;
    const uint32_t upscaledColorSrvResourceIndex = isOddFrame ? FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR_2 : FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR_1;
    const uint32_t upscaledColorUavResourceIndex = isOddFrame ? FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR_1 : FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR_2;

    mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS] = mPassContext->SRV[lockStatusSrvResourceIndex];
    mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS] = mPassContext->UAV[lockStatusUavResourceIndex];
    mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR] = mPassContext->SRV[upscaledColorSrvResourceIndex];
    mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR] = mPassContext->UAV[upscaledColorUavResourceIndex];
    mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_RCAS_INPUT] = mPassContext->SRV[upscaledColorUavResourceIndex];
    
    const uint32_t separateTranslucencySrvIndex = isOddFrame ? FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY2 : FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY1;
    const uint32_t separateTranslucencyUavIndex = isOddFrame ? FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY1 : FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY2;
    mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY] = mPassContext->UAV[separateTranslucencyUavIndex];
    mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY] = mPassContext->SRV[separateTranslucencySrvIndex];

    // actual resource size may differ from render/display resolution (e.g. due to Hw/API restrictions), so query the descriptor for UVs adjustment
    const NGITextureDesc& resourceDescInputColor = mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_COLOR]->mTexture->mDesc;
    const NGITextureDesc& resourceDescDepthClip = mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_DEPTH_CLIP]->mTexture->mDesc;
    const NGITextureDesc& resourceDescLockStatus = mPassContext->SRV[lockStatusSrvResourceIndex]->mTexture->mDesc;

    const auto& input_reactive = mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INPUT_REACTIVE_MASK];
    const auto& default_reactive = mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_REACTIVITY];

    const NGITextureDesc& resourceDescReactiveMask = input_reactive ? input_reactive->mTexture->mDesc : default_reactive->mTexture->mDesc;

    mPassContext->cbFSR2.jitterOffset.x = mPassContext->params.jitterOffset.x;
    mPassContext->cbFSR2.jitterOffset.y = mPassContext->params.jitterOffset.y;
    mPassContext->cbFSR2.renderSize.x = int32_t(mPassContext->params.renderSize.x ? mPassContext->params.renderSize.x : resourceDescInputColor.Width);
    mPassContext->cbFSR2.renderSize.y = int32_t(mPassContext->params.renderSize.y ? mPassContext->params.renderSize.y : resourceDescInputColor.Height);

    // compute the horizontal FOV for the shader from the vertical one.
    const float aspectRatio = static_cast<float>(mPassContext->params.renderSize.x) / mPassContext->params.renderSize.y;
    const float cameraAngleHorizontal = atan(tan(mPassContext->params.cameraFovAngleVertical / 2) * aspectRatio) * 2;
    mPassContext->cbFSR2.tanHalfFOV = tanf(cameraAngleHorizontal * 0.5f);

    // motion vector data
    // const Int2 motionVectorsTargetSize = mSetting.DisplayResolutionMotionVectors ? cbFSR2.displaySize : cbFSR2.renderSize;
    const Int2 motionVectorsTargetSize = mPassContext->cbFSR2.renderSize;
    // To be updated if resource is larger than the actual image size
    mPassContext->cbFSR2.depthClipUVScale.x = static_cast<float>(mPassContext->cbFSR2.renderSize.x) / resourceDescDepthClip.Width;
    mPassContext->cbFSR2.depthClipUVScale.y = static_cast<float>(mPassContext->cbFSR2.renderSize.y) / resourceDescDepthClip.Height;
    mPassContext->cbFSR2.postLockStatusUVScale.x = static_cast<float>(mPassContext->context.displaySize.x) / resourceDescLockStatus.Width;
    mPassContext->cbFSR2.postLockStatusUVScale.y = static_cast<float>(mPassContext->context.displaySize.y) / resourceDescLockStatus.Height;
    mPassContext->cbFSR2.motionVectorScale.x = mPassContext->params.motionVectorScale.x / motionVectorsTargetSize.x;
    mPassContext->cbFSR2.motionVectorScale.y = mPassContext->params.motionVectorScale.y / motionVectorsTargetSize.y;
    mPassContext->cbFSR2.reactiveMaskDimRcp.x = 1.0f / static_cast<float>(resourceDescReactiveMask.Width);
    mPassContext->cbFSR2.reactiveMaskDimRcp.y = 1.0f / static_cast<float>(resourceDescReactiveMask.Height);
    mPassContext->cbFSR2.downscaleFactor.x = static_cast<float>(mPassContext->cbFSR2.renderSize.x) / mPassContext->context.displaySize.x;
    mPassContext->cbFSR2.downscaleFactor.y = static_cast<float>(mPassContext->cbFSR2.renderSize.y) / mPassContext->context.displaySize.y;
    mPassContext->cbFSR2.preExposure = (mPassContext->params.preExposure != 0) ? mPassContext->params.preExposure : 1.0f;

    // compute jitter cancellation
    if (mSetting.MotionVectorsJitterCancellation)
    {
        mPassContext->cbFSR2.motionVectorJitterCancellation.x = (mPassContext->context.previousJitterOffset[0] - mPassContext->cbFSR2.jitterOffset.x) / motionVectorsTargetSize.x;
        mPassContext->cbFSR2.motionVectorJitterCancellation.y = (mPassContext->cbFSR2.jitterOffset.y - mPassContext->context.previousJitterOffset[1]) / motionVectorsTargetSize.y;

        mPassContext->context.previousJitterOffset[0] = mPassContext->cbFSR2.jitterOffset.x;
        mPassContext->context.previousJitterOffset[1] = mPassContext->cbFSR2.jitterOffset.y;
    }
    else
    {
        mPassContext->cbFSR2.motionVectorJitterCancellation.x = 0;
        mPassContext->cbFSR2.motionVectorJitterCancellation.y = 0;
    }

    // lock data, assuming jitter sequence length computation for now
    int32_t jitterPhaseCount = ffxFsr2GetJitterPhaseCount(mPassContext->params.renderSize.x, mPassContext->context.displaySize.x);

    // init on first frame
    const bool resetAccumulation = mPassContext->params.reset || mPassContext->context.firstExecution;
    if (resetAccumulation || mPassContext->cbFSR2.jitterPhaseCount == 0)
    {
        mPassContext->cbFSR2.jitterPhaseCount = static_cast<float>(jitterPhaseCount);
    }
    else
    {
        const int32_t jitterPhaseCountDelta = static_cast<int32_t>(jitterPhaseCount - mPassContext->cbFSR2.jitterPhaseCount);
        if (jitterPhaseCountDelta > 0)
        {
            mPassContext->cbFSR2.jitterPhaseCount++;
        }
        else if (jitterPhaseCountDelta < 0)
        {
            mPassContext->cbFSR2.jitterPhaseCount--;
        }
    }

    static const float lockInitialLifetime = 1.0f;
    mPassContext->cbFSR2.lockInitialLifetime = lockInitialLifetime;
    const int32_t maxLockFrames = static_cast<int32_t>(mPassContext->cbFSR2.jitterPhaseCount) + 1;
    mPassContext->cbFSR2.lockTickDelta = lockInitialLifetime / maxLockFrames;

    // convert delta time to seconds and clamp to [0, 1].
    mPassContext->cbFSR2.deltaTime = FFX_MAXIMUM(0.0f, FFX_MINIMUM(1.0f, mPassContext->params.frameTimeDelta / 1000));
    mPassContext->cbFSR2.frameIndex = resetAccumulation ? 0 : mPassContext->cbFSR2.frameIndex + 1;

    // shading change usage of the SPD mip levels.
    mPassContext->cbFSR2.lumaMipLevelToUse = static_cast<uint32_t>(FFX_FSR2_SHADING_CHANGE_MIP_LEVEL);

    const float mipDiv = static_cast<float>(2 << mPassContext->cbFSR2.lumaMipLevelToUse);
    mPassContext->cbFSR2.lumaMipDimensions.x = static_cast<uint32_t>(mPassContext->cbFSR2.renderSize.x / mipDiv);
    mPassContext->cbFSR2.lumaMipDimensions.y = static_cast<uint32_t>(mPassContext->cbFSR2.renderSize.y / mipDiv);
    mPassContext->cbFSR2.lumaMipRcp = static_cast<float>(mPassContext->cbFSR2.lumaMipDimensions.x * mPassContext->cbFSR2.lumaMipDimensions.y) / static_cast<float>(mPassContext->cbFSR2.renderSize.x * mPassContext->cbFSR2.renderSize.y);

    // Clear reconstructed depth for max depth store.
    if (resetAccumulation)
    {
        // LockStatus resource has no sign bit, callback functions are compensating for this.
        // Clearing the resource must follow the same logic.
        NGIClearValue clearValuesLockStatus{};
        clearValuesLockStatus.colorf[LOCK_LIFETIME_REMAINING] = lockInitialLifetime * 2.0f;
        clearValuesLockStatus.colorf[LOCK_TEMPORAL_LUMA] = 0.0f;
        clearValuesLockStatus.colorf[LOCK_TRUST] = 1.0f;
        ClearTexture(red, mPassContext->SRV[lockStatusSrvResourceIndex], clearValuesLockStatus);

        NGIClearValue clearValuesToZeroFloat({0.f, 0.f, 0.f, 0.f});
        ClearTexture(red, mPassContext->SRV[upscaledColorSrvResourceIndex], clearValuesToZeroFloat);
        ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE], clearValuesToZeroFloat);
        ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY], clearValuesToZeroFloat);

        if (mSetting.AutoExposure)
        {
            NGIClearValue clearValuesExposure({-1.f, 1e8f, 0.f, 0.f});
            ClearTexture(red, mPassContext->SRV[FFX_FSR2_RESOURCE_IDENTIFIER_EXPOSURE], clearValuesToZeroFloat);
        }
    }

    // Auto exposure
    uint32_t dispatchThreadGroupCountXY[2];
    uint32_t workGroupOffset[2];
    uint32_t numWorkGroupsAndMips[2];
    uint32_t rectInfo[4] = {0, 0, mPassContext->params.renderSize.x, mPassContext->params.renderSize.y};
    SpdSetup(dispatchThreadGroupCountXY, workGroupOffset, numWorkGroupsAndMips, rectInfo, -1);

    // downsample
    mPassContext->cbSPD.numWorkGroups = numWorkGroupsAndMips[0];
    mPassContext->cbSPD.mips = numWorkGroupsAndMips[1];
    mPassContext->cbSPD.workGroupOffset.x = workGroupOffset[0];
    mPassContext->cbSPD.workGroupOffset.y = workGroupOffset[1];
    mPassContext->cbSPD.renderSize.x = mPassContext->cbFSR2.renderSize.x;
    mPassContext->cbSPD.renderSize.y = mPassContext->cbFSR2.renderSize.y;

    // compute the constants.
    const float sharpenessRemapped = (-2.0f * mPassContext->params.sharpness) + 2.0f;
    FsrRcasCon(reinterpret_cast<uint32_t*>(&mPassContext->cbRCAS.rcasConfig), sharpenessRemapped);

    UpdateContext(gameContext);

    // Update Constant ID
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_HDR_COLOR_INPUT"), mSetting.HDR);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_APPLY_SHARPENING"), enableSharpen);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_INVERTED_DEPTH"), mSetting.DepthInverted);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_USE_MOTION_VECTORS"), mSetting.MotionVectors);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_RECONSTRUCT_PREV_DEPTH"), mSetting.DepthReconstruct);
    // red->SetProperty(NAME_ID("FFX_FSR2_OPTION_SATURATE_DEPTH_CLIP"), !isCameraJumpCut);
    // red->SetProperty(NAME_ID("FFX_FSR2_OPTION_JITTERED_MOTION_VECTORS"), mSetting.MotionVectorsJitterCancellation);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_RECTIFY_HISTORY"), mSetting.RectifyHistory);
    // red->SetProperty(NAME_ID("FFX_FSR2_OPTION_SHADING_CHANGE_DETECT"), mSetting.ShadingChangeDetect);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_SEPARATE_TRANSLUCENCY"), mSetting.SeparateTranslucency);
    // red->SetProperty(NAME_ID("FFX_FSR2_OPTION_LOW_RESOLUTION_MOTION_VECTORS"), !mSetting.DisplayResolutionMotionVectors);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_ANTI_FLICKER_VELOCITY_EXTEND"), mSetting.AntiFlicker.EnableVelocityExtend);
    red->SetProperty(NAME_ID("FFX_FSR2_OPTION_ANTI_FLICKER_WEIGHT_SCALE"), mSetting.AntiFlicker.EnableHistoryWeightScale);

    // reactive mask bias
    const int32_t threadGroupWorkRegionDim = 8;
    const int32_t dispatchSrcX = (mPassContext->cbFSR2.renderSize.x + (threadGroupWorkRegionDim - 1)) / threadGroupWorkRegionDim;
    const int32_t dispatchSrcY = (mPassContext->cbFSR2.renderSize.y + (threadGroupWorkRegionDim - 1)) / threadGroupWorkRegionDim;
    const int32_t dispatchDstX = (mPassContext->context.displaySize.x + (threadGroupWorkRegionDim - 1)) / threadGroupWorkRegionDim;
    const int32_t dispatchDstY = (mPassContext->context.displaySize.y + (threadGroupWorkRegionDim - 1)) / threadGroupWorkRegionDim;

    ScheduleDispatch(gameContext, mSetting.FSR2_ComputeLuminancePyramidR, FFX_FSR2_PASS_COMPUTE_LUMINANCE_PYRAMID, dispatchThreadGroupCountXY[0], dispatchThreadGroupCountXY[1]);
    ScheduleDispatch(gameContext, mSetting.FSR2_PrepareInputColorR, FFX_FSR2_PASS_PREPARE_INPUT_COLOR, dispatchSrcX, dispatchSrcY);
    ScheduleDispatch(gameContext, mSetting.FSR2_ReconstructPreviousDepthR, FFX_FSR2_PASS_RECONSTRUCT_PREVIOUS_DEPTH, dispatchSrcX, dispatchSrcY);
    ScheduleDispatch(gameContext, mSetting.FSR2_DepthClipR, FFX_FSR2_PASS_DEPTH_CLIP, dispatchSrcX, dispatchSrcY);

    if (mSetting.ThinFeature)
    {
        ScheduleDispatch(gameContext, mSetting.FSR2_LockR, FFX_FSR2_PASS_LOCK, dispatchSrcX, dispatchSrcY);
    }

    ScheduleDispatch(gameContext, mSetting.FSR2_AccumulateR, FFX_FSR2_PASS_ACCUMULATE, dispatchDstX, dispatchDstY);

    // RCAS
    if (enableSharpen)
    {
        const int32_t threadGroupWorkRegionDimRCAS = 16;
        const int32_t dispatchX = (mPassContext->context.displaySize.x + (threadGroupWorkRegionDimRCAS - 1)) / threadGroupWorkRegionDimRCAS;
        const int32_t dispatchY = (mPassContext->context.displaySize.y + (threadGroupWorkRegionDimRCAS - 1)) / threadGroupWorkRegionDimRCAS;
        ScheduleDispatch(gameContext, mSetting.FSR2_RCASR, FFX_FSR2_PASS_RCAS, dispatchX, dispatchY);
    }
}

void FSR2Pass::UpdateContext(const GameContext& gameContext)
{
    auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

    const auto camera = gameContext.mRenderPipeline->GetRenderCamera();
    const auto jitterData = gameContext.mRenderPipeline->GetJitterData();
    red->SetProperty(NAME_ID("fReprojectionMat"), jitterData->GetReprojectionMatrixNoJitter());
    red->SetProperty(NAME_ID("fMiscFactors"), Float4{mSetting.MiscFactors.LumaStabilityScale, mSetting.MiscFactors.DepthClipScale, mSetting.MiscFactors.DepthClipBaseScale, mSetting.MiscFactors.LumaDiffThreshold});
    red->SetProperty(NAME_ID("fExtendMin"), mSetting.AntiFlicker.ExtendMin);
    red->SetProperty(NAME_ID("fExtendMax"), mSetting.AntiFlicker.ExtendMax);
    red->SetProperty(NAME_ID("fVelocityExtendScale"), mSetting.AntiFlicker.VelocityExtendScale);
    red->SetProperty(NAME_ID("fHistoryWeightScale"), mSetting.AntiFlicker.WeightScale);
    red->SetProperty(NAME_ID("fViewDepthThreshold"), mSetting.AntiFlicker.ViewDepthThreshold);
    red->SetProperty(NAME_ID("fDistancePower"), mSetting.AntiFlicker.DistancePower);

    // Sampler State
    red->SetProperty(NAME_ID("s_PointClamp"), mPassContext->s_PointClamp.get());
    red->SetProperty(NAME_ID("s_LinearClamp"), mPassContext->s_LinearClamp.get());

    // FSR2
    red->SetProperty(NAME_ID("uRenderSize"), mPassContext->cbFSR2.renderSize);
    red->SetProperty(NAME_ID("uDisplaySize"), mPassContext->cbFSR2.displaySize);
    red->SetProperty(NAME_ID("uLumaMipDimensions"), mPassContext->cbFSR2.lumaMipDimensions);
    red->SetProperty(NAME_ID("uLumaMipLevelToUse"), mPassContext->cbFSR2.lumaMipLevelToUse);
    red->SetProperty(NAME_ID("uFrameIndex"), mPassContext->cbFSR2.frameIndex);
    red->SetProperty(NAME_ID("fDisplaySizeRcp"), mPassContext->cbFSR2.displaySizeRcp);
    red->SetProperty(NAME_ID("fJitter"), mPassContext->cbFSR2.jitterOffset);
    red->SetProperty(NAME_ID("fDeviceToViewDepth"), mPassContext->cbFSR2.deviceToViewDepth);
    red->SetProperty(NAME_ID("depthclip_uv_scale"), mPassContext->cbFSR2.depthClipUVScale);
    red->SetProperty(NAME_ID("postprocessed_lockstatus_uv_scale"), mPassContext->cbFSR2.postLockStatusUVScale);
    red->SetProperty(NAME_ID("reactive_mask_dim_rcp"), mPassContext->cbFSR2.reactiveMaskDimRcp);
    red->SetProperty(NAME_ID("MotionVectorScale"), mPassContext->cbFSR2.motionVectorScale);
    red->SetProperty(NAME_ID("fDownscaleFactor"), mPassContext->cbFSR2.downscaleFactor);
    red->SetProperty(NAME_ID("fPreExposure"), mPassContext->cbFSR2.preExposure);
    red->SetProperty(NAME_ID("fTanHalfFOV"), mPassContext->cbFSR2.tanHalfFOV);
    red->SetProperty(NAME_ID("fMotionVectorJitterCancellation"), mPassContext->cbFSR2.motionVectorJitterCancellation);
    red->SetProperty(NAME_ID("fJitterSequenceLength"), mPassContext->cbFSR2.jitterPhaseCount);
    red->SetProperty(NAME_ID("fLockInitialLifetime"), mPassContext->cbFSR2.lockInitialLifetime);
    red->SetProperty(NAME_ID("fLockTickDelta"), mPassContext->cbFSR2.lockTickDelta);
    red->SetProperty(NAME_ID("fDeltaTime"), mPassContext->cbFSR2.deltaTime);
    red->SetProperty(NAME_ID("fDynamicResChangeFactor"), mPassContext->cbFSR2.dynamicResChangeFactor);
    red->SetProperty(NAME_ID("fLumaMipRcp"), mPassContext->cbFSR2.lumaMipRcp);

    // SPD
    red->SetProperty(NAME_ID("mips"), mPassContext->cbSPD.mips);
    red->SetProperty(NAME_ID("numWorkGroups"), mPassContext->cbSPD.numWorkGroups);
    red->SetProperty(NAME_ID("workGroupOffset"), mPassContext->cbSPD.workGroupOffset);
    red->SetProperty(NAME_ID("renderSize"), mPassContext->cbSPD.renderSize);

    // RCAS
    red->SetProperty(NAME_ID("rcasConfig"), mPassContext->cbRCAS.rcasConfig);
}

void FSR2Pass::ScheduleDispatch(const GameContext& gameContext, ComputeShaderR* cs, const UInt32 passID, UInt32 dispatchX, UInt32 dispatchY, UInt32 dispatchZ)
{
    auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto& desc = FSR2_PASS_DESC[passID];
    auto* pass = red->AllocatePass(desc.name);

    for (auto& idx : desc.SRV)
    {
        pass->SetProperty(idx.data(), mPassContext->SRV[FSR2_SRV_RESOURCE_BINDING[idx]], NGIResourceState::ComputeShaderShaderResource);
    }

    for (auto& idx : desc.UAV)
        pass->SetProperty(idx.data(), mPassContext->UAV[FSR2_UAV_RESOURCE_BINDING[idx]], NGIResourceState::ComputeShaderUnorderedAccess);

#if FFX_FSR2_DEBUG_OUT
    pass->SetProperty(NAME_ID("rw_debug_out"), mPassContext->UAV[FFX_FSR2_RESOURCE_IDENTIFIER_DEBUG_OUTPUT], NGIResourceState::ComputeShaderUnorderedAccess);
#endif

    pass->Dispatch(cs, "CS", dispatchX, dispatchY, dispatchZ);
}

void FSR2Pass::BuildInteralResource(const GameContext& gameContext)
{
    mPassContext->cbFSR2.displaySize.x = mPassContext->context.displaySize.x;
    mPassContext->cbFSR2.displaySize.y = mPassContext->context.displaySize.y;
    mPassContext->cbFSR2.displaySizeRcp.x = 1.0f / mPassContext->context.displaySize.x;
    mPassContext->cbFSR2.displaySizeRcp.y = 1.0f / mPassContext->context.displaySize.y;

    auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    // declare internal resources needed

    auto displaySize = mPassContext->context.displaySize;
    auto maxRenderSize = mPassContext->context.maxRenderSize;

    const Fsr2ResourceDescription internalSurfaceDesc[] = {
        {FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_1, "FSR2_LockStatus1", FFX_RESOURCE_USAGE_RENDERTARGET | FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R11G11B10_FLOAT, displaySize.x, displaySize.y, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_LOCK_STATUS_2, "FSR2_LockStatus2", FFX_RESOURCE_USAGE_RENDERTARGET | FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R11G11B10_FLOAT, displaySize.x, displaySize.y, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR_1, "FSR2_InternalUpscaled1", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16G16B16A16_FLOAT, displaySize.x, displaySize.y, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_UPSCALED_COLOR_2, "FSR2_InternalUpscaled2", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16G16B16A16_FLOAT, displaySize.x, displaySize.y, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY1, "FSR2_PREV_SEPARATE_TRANSLUCENCY1", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16G16B16A16_FLOAT, displaySize.x, displaySize.y, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_PREV_SEPARATE_TRANSLUCENCY2, "FSR2_PREV_SEPARATE_TRANSLUCENCY2", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16G16B16A16_FLOAT, displaySize.x, displaySize.y, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_LUMA_HISTORY, "FSR2_LumaHistory", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R8G8B8A8_UNORM, maxRenderSize.x, maxRenderSize.y, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_RECONSTRUCTED_PREVIOUS_NEAREST_DEPTH, "FSR2_ReconstructedPrevNearestDepth", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R32_UINT, maxRenderSize.x, maxRenderSize.y, 1, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_PREPARED_INPUT_COLOR, "FSR2_PreparedInputColor", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16G16B16A16_UNORM, maxRenderSize.x, maxRenderSize.y, 1, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_MOTION_VECTORS, "FSR2_DilatedVelocity", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16G16_FLOAT, maxRenderSize.x, maxRenderSize.y, 1, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_DEPTH, "FSR2_DilatedDepth", FFX_RESOURCE_USAGE_RENDERTARGET | FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16_FLOAT, maxRenderSize.x, maxRenderSize.y, 1, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE, "FSR2_ExposureMips", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R16_FLOAT, maxRenderSize.x / 2, maxRenderSize.y / 2, 0, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_DEPTH_CLIP, "FSR2_DepthClip", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R8_UNORM, maxRenderSize.x, maxRenderSize.y, 1, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_DILATED_REACTIVE_MASKS, "FSR2_DilatedReactiveMasks", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R8G8B8A8_UNORM, maxRenderSize.x, maxRenderSize.y, 1, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_SPD_ATOMIC_COUNT, "FSR2_SpdAtomicCounter", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R32_UINT, 1, 1, 1, FFX_RESOURCE_FLAGS_ALIASABLE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_REACTIVITY, "FSR2_DefaultReactiviyMask", FFX_RESOURCE_USAGE_READ_ONLY, FFX_SURFACE_FORMAT_R8_UNORM, 1, 1, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_INTERNAL_DEFAULT_EXPOSURE, "FSR2_DefaultExposure", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R32G32_FLOAT, 1, 1, 1, FFX_RESOURCE_FLAGS_NONE},
        {FFX_FSR2_RESOURCE_IDENTIFIER_EXPOSURE, "FSR2_Exposure", FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R32G32_FLOAT, 1, 1, 1, FFX_RESOURCE_FLAGS_NONE},

#if FFX_FSR2_DEBUG_OUT
        {FFX_FSR2_RESOURCE_IDENTIFIER_DEBUG_OUTPUT, "FSR2_DebugOut", FFX_RESOURCE_USAGE_RENDERTARGET | FFX_RESOURCE_USAGE_UAV, FFX_SURFACE_FORMAT_R32G32B32A32_FLOAT, displaySize.x, displaySize.y, 1, FFX_RESOURCE_FLAGS_NONE},
#endif
    };

    for (auto& surface : internalSurfaceDesc)
    {
        NGITextureUsage usage = getUsageFlagsFromResourceUsage(static_cast<FfxResourceUsage>(surface.usage));
        GraphicsFormat format = getFormatFromSurfaceFormat(surface.format);
        UInt16 mipCount = surface.mipCount;

        if (mipCount == 0)
            mipCount = 1 + static_cast<UInt16>(std::floor(std::log2(std::max(std::max(surface.width, surface.height), 1u))));

        REDTextureRef texture = AllocateTexture(red, surface.name, usage, format, surface.width, surface.height, mipCount);

        mPassContext->mResource[surface.id] = texture;

        REDTextureView* textureView = AllocateTextureView(red, texture, usage, format, {NGITextureAspect::Color, 0, mipCount, 0, 1});
        ClearTexture(red, textureView, NGIClearValue({0.f, 0.f, 0.f, 0.f}));
    }
}

void FSR2Pass::BuildTextureView(const GameContext& gameContext)
{
    auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

    for (int ID = 0; ID < mPassContext->mResource.size(); ID++)
    {
        if (mPassContext->mResource[ID])
        {
            auto texture = mPassContext->mResource[ID];
            auto& desc = texture->mDesc;

            if (EnumHasAllFlags(desc.Usage, NGITextureUsage::ShaderResource))
            {
                if (ID == FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE)
                {
                    UInt16 MostDetailedMip = static_cast<UInt16>(FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP_SHADING_CHANGE - FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE);
                    mPassContext->SRV[ID] = AllocateTextureView(red, texture, NGITextureUsage::ShaderResource, desc.Format, {NGITextureAspect::Color, MostDetailedMip, 1, 0, 1});
                }
                else
                {
                    mPassContext->SRV[ID] = AllocateTextureView(red, texture, NGITextureUsage::ShaderResource, desc.Format, {NGITextureAspect::Color, 0, 1, 0, 1});
                }
                if (texture->mType == REDResourceType::External)
                    mPassContext->SRV[ID]->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit);
            }

            if (EnumHasAllFlags(desc.Usage, NGITextureUsage::UnorderedAccess))
            {
                mPassContext->UAV[ID] = AllocateTextureView(red, texture, desc.Usage, desc.Format, {NGITextureAspect::Color, 0, 1, 0, 1});

                if (texture->mType == REDResourceType::External)
                    mPassContext->UAV[ID]->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ComputeShaderBit);
            }
        }
    }

    // FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP
    auto texture = mPassContext->mResource[FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE];
    const UInt32 mipmapID[] = {FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP_5, FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE_MIPMAP_SHADING_CHANGE};
    for (auto ID : mipmapID)
    {
        auto& desc = texture->mDesc;
        UInt16 MostDetailedMip = static_cast<UInt16>(ID - FFX_FSR2_RESOURCE_IDENTIFIER_AUTO_EXPOSURE);
        MostDetailedMip = std::clamp(MostDetailedMip, static_cast<UInt16>(0), static_cast<UInt16>(desc.MipCount - 1));

        mPassContext->SRV[ID] = AllocateTextureView(red, texture, desc.Usage, desc.Format, {NGITextureAspect::Color, MostDetailedMip, 1, 0, 1});

        if (texture->mType == REDResourceType::External)
            mPassContext->SRV[ID]->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit);

        if (EnumHasAllFlags(desc.Usage, NGITextureUsage::UnorderedAccess))
        {
            mPassContext->UAV[ID] = AllocateTextureView(red, texture, desc.Usage, desc.Format, {NGITextureAspect::Color, MostDetailedMip, 1, 0, 1});

            if (texture->mType == REDResourceType::External)
                mPassContext->UAV[ID]->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ComputeShaderBit);
        }
    }
}

void FSR2Pass::ExtendInteralResource(const GameContext& gameContext)
{
    auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

    for (auto tex : mPassContext->mResource)
    {
        if (tex)
        {
            if (red->Validate(tex))
            {
                tex->ExtendLifetime();
            }
            else
            {
                Assert(0);
            }
        }
    }
}

void FSR2Pass::BuildOnceResource(const GameContext& gameContext)
{
}

void FSR2Output::SetOutputData(const GameContext& gameContext) 
{
    gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = outputcolor;
    // Use display size texture view after this line instead please, no matter FSR2 was enabled or not ///
    gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>() = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>();
}
// void FSR2Pass::AssemblePostTranslucency()
// {
// REDColorTargetDesc ColorTargets[]
// {
//     {
//         sceneColorView,
//         NGILoadOp::Load,
//         NGIStoreOp::Store,
//         NGIClearValue{0, 0, 0, 0},
//     }
// };

// REDDepthStencilTargetDesc depthStencilTarget{
//     mDisplayDepthStencilView,
//     NGILoadOp::Load,
//     NGIStoreOp::Store,
//     NGILoadOp::DontCare,
//     NGIStoreOp::DontCare,
// };

// bool useTestCullingCamera = false;
// auto* camSys = mWorld->GetRenderSystem<CameraSystemR>();
// if (EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
// {
//     if (camSys->GetTestCullingCamera())
//     {
//         useTestCullingCamera = true;
//     }
// }
// auto* drawCamera = useTestCullingCamera ? GetCullingRenderCamera() : GetRenderCamera();

// mRED->BeginRenderPass("AfterUpscalePass", static_cast<UInt32>(ArrayCount(ColorTargets)), ColorTargets, &depthStencilTarget);
// {
//     NGIRenderPassTargetIndex colorTargets[]{NGIRenderPassTargetIndex::Target0};
//     auto* forwardPass = mRED->AllocateSubRenderPass("AfterUpscalePass", 0, nullptr, static_cast<UInt32>(ArrayCount(colorTargets)), colorTargets, REDPassFlagBit::NeedDepth | REDPassFlagBit::DepthReadOnly);
//     forwardPass->DrawWorld({mWorld, drawCamera, "after_upscale", gRenderGroupTransparent + 300, gRenderGroupUI - 2});
// }
// mRED->EndRenderPass();
// }

}   // namespace cross


