#include "NativeGraphicsInterface/NGIManager.h"
#include "ScreenSpaceReflection.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/SmartGI/SmartGIPass.h"

namespace cross {
void ScreenSpaceReflectionSetting::Initialize()
{
}

bool ScreenSpaceReflection::ExecuteImp(const GameContext& gameContext, std::array<REDTextureView*, 4>& gBufferViews, 
    REDTextureView* sceneViewLastFrame, REDTextureView* sceneDepthStencilView, REDTextureView* depthPyramid, REDTextureView*& ssrView)
{
    auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

    mFrameCount++;
    int texCountMod = mFrameCount % 2;
    //int texCountMod = mFrameCount; 
    auto gameViewWidth = static_cast<UInt16>(gBufferViews[0]->mTexture->mDesc.Width);
    auto gameViewHeight = static_cast<UInt16>(gBufferViews[0]->mTexture->mDesc.Height);

    if (mSetting.EnableDenoiser == SSR_DENOISE_METHOD::DISABLE_DENOISER || !mSetting.enable)
    {
        ssrView = IRenderPipeline::CreateTextureView2D("SSR Texture", gameViewWidth, gameViewHeight, GraphicsFormat::R8G8B8A8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);
    }

    if (!mSetting.enable)
    {
        gameContext.mRenderPipeline->PostProcess(
            [&](auto pass)
            {
                pass->SetProperty(NAME_ID("_ColorTex"), mBlackTexture);
            }, mPostMtl, "blit", true, ssrView);
        return true;
    }
    else
    {
        red->BeginRegion("SSR");

        if (mSetting.EnableDenoiser != SSR_DENOISE_METHOD::DISABLE_DENOISER)
        {
            mTemporalState.TryCreateNGIResource(red, gameViewWidth, gameViewHeight);
            mTemporalState.IncTextureIndex();
            mTemporalState.InitTextureView(red, mStochasticReprojectShader, mSetting.EnableDenoiser == SSR_DENOISE_METHOD::STOCHASTIC_DENOISER);
            ssrView = mTemporalState.mRadianceViewRT[mTemporalState.mCurrentIndex];
        }

        auto* sceneDepthView = red->AllocateTextureView(sceneDepthStencilView->mTexture,
                                                        NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                           sceneDepthStencilView->mDesc.Format,
                                                                           NGITextureType::Texture2D,
                                                                           NGITextureSubRange{
                                                                               NGITextureAspect::Depth,
                                                                               0,
                                                                               sceneDepthStencilView->mDesc.SubRange.MipLevels,
                                                                               0,
                                                                               1,
                                                                           }});

        const RenderCamera* renderCamera = gameContext.mRenderCamera;
        const auto& viewMatrix = renderCamera->GetViewMatrix();
        const auto& projMatrix = renderCamera->GetProjMatrix();
        const auto& viewProjMatrix = renderCamera->GetViewProjMatrix();
        const auto& invProjMatrix = renderCamera->GetInvertProjMatrix();
        const auto& invViewMatrix = renderCamera->GetInvertViewMatrix();
        const auto& preViewProjMatrix = renderCamera->GetLastFrameViewProjMatrix();
        auto invViewProjMatrix = invProjMatrix * invViewMatrix;
        Float4 screenViewSize{static_cast<float>(gameViewWidth), static_cast<float>(gameViewHeight), 1.0f / gameViewWidth, 1.0f / gameViewHeight};
        Float3 cameraPos = Float3(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32);
        Float2 HZBUvFactor(gameViewWidth / static_cast<float>(2 * depthPyramid->GetWidth()), gameViewHeight / static_cast<float>(2 * depthPyramid->GetHeight()));
        Float4 HZBUvFactorAndInvFactor(HZBUvFactor.x, HZBUvFactor.y, 1.0f / HZBUvFactor.x, 1.0f / HZBUvFactor.y);
        UInt32 frameCount = GetFrameCountMod8();

        Double4x4 offsetMat = Double4x4::Identity();
#ifdef CE_USE_DOUBLE_TRANSFORM
        offsetMat = Double4x4::CreateTranslation(static_cast<Double3>(renderCamera->GetTilePosition() - renderCamera->GetTilePosition<true>()) * LENGTH_PER_TILE);
#endif
        Float4x4 clipToPrevClip = static_cast<Float4x4>(Double4x4(renderCamera->GetViewProjMatrix()).Inverted() * offsetMat * Double4x4(renderCamera->GetLastFrameViewProjMatrix()));

        auto SetSSRParam = [&](REDPass* pass) {
            pass->SetProperty(NAME_ID("FurthestHZBTexture"), depthPyramid);
            pass->SetProperty(NAME_ID("PrevSceneColorTexture"), sceneViewLastFrame);
            pass->SetProperty(NAME_ID("_DepthMap"), sceneDepthView);
            pass->SetProperty(NAME_ID("_InverseProjectionMatrix"), invProjMatrix);
            pass->SetProperty(NAME_ID("_InverseViewMatrix"), invViewMatrix);
            pass->SetProperty(NAME_ID("_PreViewMatrix"), renderCamera->GetLastFrameViewMatrix());
            pass->SetProperty(NAME_ID("_PreProjMatrix"), renderCamera->GetLastFrameProjMatrix());
            pass->SetProperty(NAME_ID("View_TranslatedWorldToView"), viewMatrix);
            pass->SetProperty(NAME_ID("View_TranslatedWorldToClip"), viewProjMatrix);
            pass->SetProperty(NAME_ID("View_ViewToClip"), projMatrix);
            pass->SetProperty(NAME_ID("View_ViewSizeAndInvSize"), screenViewSize);
            pass->SetProperty(NAME_ID("_HZBUvFactorAndInvFactor"), HZBUvFactorAndInvFactor);
            pass->SetProperty(NAME_ID("ce_CameraPos"), cameraPos);
            pass->SetProperty(NAME_ID("_StateFrameIndexMod8"), frameCount);
            pass->SetProperty(NAME_ID("_MaxTraceDistance"), mSetting.MaxTraceDistance);
            pass->SetProperty(NAME_ID("_MaxScreenTraceFraction"), mSetting.MaxScreenTraceFraction);
            pass->SetProperty(NAME_ID("_NumRayCastSteps"), mSetting.NumSteps);
            pass->SetProperty(NAME_ID("_ViewRange"), mSetting.ViewRange);
            pass->SetProperty(NAME_ID("_NumRays"), mSetting.NumRays);
            pass->SetProperty(NAME_ID("GLOSSY"), mSetting.Glossy);
            pass->SetProperty(NAME_ID("USE_FULL_DEPTH"), mSetting.UseFullDepth);
            pass->SetProperty(NAME_ID("SLOPE_COMPARE_TOLERANCE_SCALE"), mSetting.SlopeCompareTolerance);
            pass->SetProperty(NAME_ID("_View_UseReverseZ"), true);
            pass->SetProperty(NAME_ID("_StartMipLevel"), static_cast<float>(mSetting.StartMipLevel));
            pass->SetProperty(NAME_ID("_View_ClipToPrevClip"), clipToPrevClip);
        };

        
        /*REDTextureView* ssrTranslucentView =
            IRenderPipeline::CreateTextureView2D("SSRTranslucent", gameViewWidth, gameViewHeight, GraphicsFormat::R8G8B8A8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        red->AllocatePass("ClearTranslucentView")->ClearTexture(ssrTranslucentView, NGIClearValue({0, 0, 0, 0}));*/

        NGICopyTexture region{0, {}, 0, {}, {gameViewWidth, gameViewHeight, 1}};
        // we support write depth in transparent passes.
        // copy a scene depth and use it as the depth/stencil target for transparent object, so any write into this target will not affect other passes.
        NGITextureDesc depthStencilDesc{
            sceneDepthStencilView->mTexture->mDesc.Format,
            NGITextureType::Texture2D,
            1,
            1,
            gameViewWidth,
            gameViewHeight,
            1,
            1,
            NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst,
        };
        NGITextureViewDesc dsViewDesc{
            NGITextureUsage::DepthStencil | NGITextureUsage::CopyDst,
            depthStencilDesc.Format,
            NGITextureType::Texture2D,
            {NGITextureAspect::Depth | NGITextureAspect::Stencil, 0, 1, 0, 1},
        };
        REDTextureRef dsTex = red->AllocateTexture("DepthSSRCopy" + std::to_string(texCountMod), depthStencilDesc); 
        REDTextureView* sceneTranslucentDepth = red->AllocateTextureView(dsTex, dsViewDesc);
        red->AllocatePass("CopySceneDepth")->CopyTextureToTexture(dsTex, sceneDepthStencilView->mTexture, 1, &region);

        //if (mSetting.SeperatedTranslucentPass)
        //{
        //    REDColorTargetDesc ssrTranslucentTargetDesc{ssrTranslucentView, NGILoadOp::Clear, NGIStoreOp::Store, NGIClearValue{0, 0, 0, 0}};
        //    REDDepthStencilTargetDesc depthStencilTargetDesc{sceneTranslucentDepth, NGILoadOp::Load, NGIStoreOp::Store, NGILoadOp::Load, NGIStoreOp::Store};

        //    red->BeginRenderPass("SSRTranslucentPass", 1, &ssrTranslucentTargetDesc, &depthStencilTargetDesc);
        //    {
        //        auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
        //        auto* ssrTranslucentPass = red->AllocateSubRenderPass("SSRTranslucentPass", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);
        //        SetSSRParam(ssrTranslucentPass);
        //        ssrTranslucentPass->SetProperty(NAME_ID("SSR_TRANSLUCENT_PASS"), true);
        //        auto drawFilter = REDDrawFilter{const_cast<RenderWorld*>(gameContext.mRenderWorld), gameContext.mRenderCamera, NameID("SSRTranslucentPass"), 0, gRenderGroupUI - 1, 0, nullptr};
        //        ssrTranslucentPass->DrawWorld(drawFilter);
        //    }
        //    red->EndRenderPass();
        //}

        gameContext.mRenderPipeline->PostProcess(
            [&](auto pass) {
                //pass->SetProperty(NAME_ID("SSRTranslucentTexture"), ssrTranslucentView);
                pass->SetProperty(NAME_ID("_GBuffer1Map"), gBufferViews[1], NGIResourceState::PixelShaderShaderResource);
                pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0], NGIResourceState::PixelShaderShaderResource);
                pass->SetProperty(NAME_ID("_ViewSize"), Float2(gameViewWidth, gameViewHeight));
                SetSSRParam(pass);
            },
            mDeferSSRMtl,
            "ssr_tracing",
            true,
            ssrView);

        if (mSetting.EnableDenoiser != SSR_DENOISE_METHOD::DISABLE_DENOISER)
        {
            if (mSetting.EnableDenoiser == SSR_DENOISE_METHOD::SIMPLE_DENOISER)
            {
                auto* prefilterPass = red->AllocatePass("StochasticSSR Prefilter", true);
                prefilterPass->SetProperty(NAME_ID("g_in_radiance"), mTemporalState.mRadianceViewSRV[mTemporalState.mCurrentIndex]);
                prefilterPass->SetProperty(NAME_ID("g_out_radiance"), mTemporalState.mRadianceView[1 - mTemporalState.mCurrentIndex]);
                prefilterPass->SetProperty(NAME_ID("g_buffer_dimensions"), UInt2(gameViewWidth, gameViewHeight));

                UInt3 groupSize;
                mStochasticPrefilterShader->GetThreadGroupSize("mainSpatialFilterCS", groupSize.x, groupSize.y, groupSize.z);
                prefilterPass->Dispatch(mStochasticPrefilterShader, "mainSpatialFilterCS", (gameViewWidth + groupSize.x - 1) / groupSize.x, (gameViewHeight + groupSize.y - 1) / groupSize.y, 1);

                ssrView = mTemporalState.mRadianceViewRT[1 - mTemporalState.mCurrentIndex];
            }

            if (false)
            {
                auto* prefilterPass = red->AllocatePass("StochasticSSR Prefilter", true);
                prefilterPass->SetProperty(NAME_ID("g_in_radiance"), mTemporalState.mRadianceViewSRV[mTemporalState.mCurrentIndex]);
                prefilterPass->SetProperty(NAME_ID("g_out_radiance"), mTemporalState.mRadianceView[1 - mTemporalState.mCurrentIndex]);
                prefilterPass->SetProperty(NAME_ID("g_buffer_dimensions"), UInt2(gameViewWidth, gameViewHeight));
                UInt3 groupSize;
                mStochasticPrefilterShader->GetThreadGroupSize("mainSpatialBlurHCS", groupSize.x, groupSize.y, groupSize.z);
                prefilterPass->Dispatch(mStochasticPrefilterShader, "mainSpatialBlurHCS", (gameViewWidth + groupSize.x - 1) / groupSize.x, (gameViewHeight + groupSize.y - 1) / groupSize.y, 1);

                auto* prefilterPass1 = red->AllocatePass("StochasticSSR Prefilter1", true);
                prefilterPass1->SetProperty(NAME_ID("g_in_radiance"), mTemporalState.mRadianceViewSRV[1 - mTemporalState.mCurrentIndex]);
                prefilterPass1->SetProperty(NAME_ID("g_out_radiance"), mTemporalState.mRadianceView[mTemporalState.mCurrentIndex]);
                prefilterPass1->SetProperty(NAME_ID("g_buffer_dimensions"), UInt2(gameViewWidth, gameViewHeight));
                mStochasticPrefilterShader->GetThreadGroupSize("mainSpatialBlurVCS", groupSize.x, groupSize.y, groupSize.z);
                prefilterPass1->Dispatch(mStochasticPrefilterShader, "mainSpatialBlurVCS", (gameViewWidth + groupSize.x - 1) / groupSize.x, (gameViewHeight + groupSize.y - 1) / groupSize.y, 1);
            }
            // [Stochastic seems no better than simple denoiser]
            if (mSetting.EnableDenoiser == SSR_DENOISE_METHOD::STOCHASTIC_DENOISER)
            {
                auto* normalTexView = gBufferViews[1];
                REDTextureView* normalHistoryTexView = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mNormalHistoryRT, normalTexView);
                mTemporalState.mNormalHistoryRT = normalTexView->mTexture;
                mTemporalState.mNormalHistoryRT->ExtendLifetime();

                REDTextureRef curDepthTex = dsTex;   // sceneDepthStencilView->mTexture
                REDTextureView* depthSRV = red->AllocateTextureView(curDepthTex,
                                                                    NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                       curDepthTex->GetDesc().Format,   // sceneDepthStencilView->mDesc.Format,
                                                                                       NGITextureType::Texture2D,
                                                                                       NGITextureSubRange{
                                                                                           NGITextureAspect::Depth,
                                                                                           0,
                                                                                           curDepthTex->GetDesc().MipCount,   // sceneDepthStencilView->mDesc.SubRange.MipLevels,
                                                                                           0,
                                                                                           1,
                                                                                       }});
                REDTextureView* depthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mDepthHistoryRT, depthSRV, NGITextureAspect::Depth);
                mTemporalState.mDepthHistoryRT = depthSRV->mTexture;
                mTemporalState.mDepthHistoryRT->ExtendLifetime();

                REDTextureRef reprojectedRadianceTex = red->AllocateTexture(
                    "Reprojected Radiance", TextureCreateHelper::GetTexture2DDesc(GraphicsFormat::R8G8B8A8_UNorm, gameViewWidth, gameViewHeight, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess));
                REDTextureView* reprojectedRadianceTexUAV = red->AllocateTextureView(reprojectedRadianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, reprojectedRadianceTex->GetDesc().Format));
                REDTextureView* reprojectedRadianceTexSRV = red->AllocateTextureView(reprojectedRadianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, reprojectedRadianceTex->GetDesc().Format));

                UInt32 viewWidthMod8 = static_cast<UInt32>(std::ceil(gameViewWidth / 8.0f));
                UInt32 viewHeightMod8 = static_cast<UInt32>(std::ceil(gameViewHeight / 8.0f));
                REDTextureRef averageRadianceTex =
                    red->AllocateTexture("Average Radiance", TextureCreateHelper::GetTexture2DDesc(GraphicsFormat::R11G11B10_UFloatPack32, viewWidthMod8, viewHeightMod8, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess));
                REDTextureView* averageRadianceTexUAV = red->AllocateTextureView(averageRadianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, averageRadianceTex->GetDesc().Format));
                REDTextureView* averageRadianceTexSRV = red->AllocateTextureView(averageRadianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, averageRadianceTex->GetDesc().Format));

                auto UpdateConstantsParam = [&](REDPass* pass) {
                    pass->SetProperty(NAME_ID("_View_ClipToPrevClip"), clipToPrevClip);
                    pass->SetProperty(NAME_ID("g_prev_view_proj"), preViewProjMatrix);
                    pass->SetProperty(NAME_ID("g_buffer_dimensions"), UInt2(gameViewWidth, gameViewHeight));
                    pass->SetProperty(NAME_ID("g_buffer_dimensions_aligned8"), UInt2(((gameViewWidth >> 3) << 3), ((gameViewHeight >> 3) << 3)));
                    pass->SetProperty(NAME_ID("g_inv_buffer_dimensions"), Float2(1.0f / gameViewWidth, 1.0f / gameViewHeight));
                    pass->SetProperty(NAME_ID("g_temporal_stability_factor"), 0.7f);
                    pass->SetProperty(NAME_ID("g_depth_buffer_thickness"), 0.015f);
                    pass->SetProperty(NAME_ID("g_roughness_threshold"), mSetting.StochasticRoughnessThreshold);
                    pass->SetProperty(NAME_ID("g_temporal_variance_threshold"), 0.f);
                    pass->SetProperty(NAME_ID("g_frame_index"), mFrameCount);
                    pass->SetProperty(NAME_ID("g_max_traversal_intersections"), 128);
                    pass->SetProperty(NAME_ID("g_min_traversal_occupancy"), 4);
                    pass->SetProperty(NAME_ID("g_most_detailed_mip"), 0);
                    pass->SetProperty(NAME_ID("g_samples_per_quad"), 1);
                    pass->SetProperty(NAME_ID("g_temporal_variance_guided_tracing_enabled"), 1);
                };

                if (true)
                {
                    auto* reprojectPass = red->AllocatePass("StochasticSSR Reproject", true);
                    reprojectPass->SetProperty(NAME_ID("g_depth_buffer"), depthSRV);
                    reprojectPass->SetProperty(NAME_ID("normal_gbuffer"), normalTexView);
                    reprojectPass->SetProperty(NAME_ID("g_depth_buffer_history"), depthHistoryTexView);
                    reprojectPass->SetProperty(NAME_ID("normal_gbuffer_history"), normalHistoryTexView);

                    reprojectPass->SetProperty(NAME_ID("g_in_radiance"), mTemporalState.mRadianceViewSRV[mTemporalState.mCurrentIndex]);
                    reprojectPass->SetProperty(NAME_ID("g_radiance_history"), mTemporalState.mRadianceViewSRV[1 - mTemporalState.mCurrentIndex]);
                    reprojectPass->SetProperty(NAME_ID("g_motion_vector"), gBufferViews[3]);

                    reprojectPass->SetProperty(NAME_ID("g_variance_history"), mTemporalState.mVarianceViewSRV[1 - mTemporalState.mCurrentIndex]);
                    reprojectPass->SetProperty(NAME_ID("g_sample_count_history"), mTemporalState.mSampleCountViewSRV[1 - mTemporalState.mCurrentIndex]);
                    // reprojectPass->SetProperty(NAME_ID("g_blue_noise_texture"), nullptr);

                    reprojectPass->SetProperty(NAME_ID("g_out_reprojected_radiance"), reprojectedRadianceTexUAV);
                    reprojectPass->SetProperty(NAME_ID("g_out_average_radiance"), averageRadianceTexUAV);
                    reprojectPass->SetProperty(NAME_ID("g_out_variance"), mTemporalState.mVarianceView[mTemporalState.mCurrentIndex]);
                    reprojectPass->SetProperty(NAME_ID("g_out_sample_count"), mTemporalState.mSampleCountView[mTemporalState.mCurrentIndex]);

                    UpdateConstantsParam(reprojectPass);
                    UInt3 groupSize;
                    mStochasticReprojectShader->GetThreadGroupSize("mainCS", groupSize.x, groupSize.y, groupSize.z);
                    reprojectPass->Dispatch(mStochasticReprojectShader, "mainCS", (gameViewWidth + groupSize.x - 1) / groupSize.x, (gameViewHeight + groupSize.y - 1) / groupSize.y, 1);
                }

                if (true)
                {
                    auto* prefilterPass = red->AllocatePass("StochasticSSR Prefilter", true);
                    prefilterPass->SetProperty(NAME_ID("g_depth_buffer"), depthSRV);
                    prefilterPass->SetProperty(NAME_ID("normal_gbuffer"), normalTexView);
                    prefilterPass->SetProperty(NAME_ID("g_average_radiance"), averageRadianceTexSRV);
                    prefilterPass->SetProperty(NAME_ID("g_in_radiance"), mTemporalState.mRadianceViewSRV[mTemporalState.mCurrentIndex]);
                    prefilterPass->SetProperty(NAME_ID("g_in_variance"), mTemporalState.mVarianceViewSRV[mTemporalState.mCurrentIndex]);

                    prefilterPass->SetProperty(NAME_ID("g_out_radiance"), mTemporalState.mRadianceView[1 - mTemporalState.mCurrentIndex]);
                    prefilterPass->SetProperty(NAME_ID("g_out_variance"), mTemporalState.mVarianceView[1 - mTemporalState.mCurrentIndex]);

                    UpdateConstantsParam(prefilterPass);
                    UInt3 groupSize;
                    mStochasticPrefilterShader->GetThreadGroupSize("mainCS", groupSize.x, groupSize.y, groupSize.z);
                    prefilterPass->Dispatch(mStochasticPrefilterShader, "mainCS", (gameViewWidth + groupSize.x - 1) / groupSize.x, (gameViewHeight + groupSize.y - 1) / groupSize.y, 1);
                }

                if (true)
                {
                    auto* resolveTemporalPass = red->AllocatePass("StochasticSSR Resolve Temporal", true);
                    resolveTemporalPass->SetProperty(NAME_ID("normal_gbuffer"), normalTexView);
                    resolveTemporalPass->SetProperty(NAME_ID("g_average_radiance"), averageRadianceTexSRV);
                    resolveTemporalPass->SetProperty(NAME_ID("g_in_radiance"), mTemporalState.mRadianceViewSRV[1 - mTemporalState.mCurrentIndex]);
                    resolveTemporalPass->SetProperty(NAME_ID("g_in_reprojected_radiance"), reprojectedRadianceTexSRV);
                    resolveTemporalPass->SetProperty(NAME_ID("g_in_variance"), mTemporalState.mVarianceViewSRV[1 - mTemporalState.mCurrentIndex]);
                    resolveTemporalPass->SetProperty(NAME_ID("g_in_sample_count"), mTemporalState.mSampleCountViewSRV[mTemporalState.mCurrentIndex]);
                    resolveTemporalPass->SetProperty(NAME_ID("g_out_radiance"), mTemporalState.mRadianceView[mTemporalState.mCurrentIndex]);
                    resolveTemporalPass->SetProperty(NAME_ID("g_out_variance"), mTemporalState.mVarianceView[mTemporalState.mCurrentIndex]);

                    UpdateConstantsParam(resolveTemporalPass);
                    UInt3 groupSize;
                    mStochasticResolveTemporalShader->GetThreadGroupSize("mainCS", groupSize.x, groupSize.y, groupSize.z);
                    resolveTemporalPass->Dispatch(mStochasticResolveTemporalShader, "mainCS", (gameViewWidth + groupSize.x - 1) / groupSize.x, (gameViewHeight + groupSize.y - 1) / groupSize.y, 1);
                }
            }
        }


        red->EndRegion();
        return true;
    }
}

void ScreenSpaceReflection::InitializeSetting()
{
    mSetting.Initialize();
}

StochasticSSRTemporalState::~StochasticSSRTemporalState() 
 {
    DelayDeleteNGIResource();
 }

void StochasticSSRTemporalState::TryCreateNGIResource(RenderingExecutionDescriptor* red, UInt32 width, UInt32 height)
{
    if (mRadiance[0] != nullptr && mRadiance[0]->GetDesc().Width == width && mRadiance[0]->GetDesc().Height == height)
    {
        return;
    }
    DelayDeleteNGIResource();

    mTexInited = false;
    auto& device = GetNGIDevice();
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (auto i = 0; i < 2; i++)
    {
        const NGITextureDesc descRGBA{GraphicsFormat::R8G8B8A8_UNorm, NGITextureType::Texture2D, 1, 1, width, height, 1, 1, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::RenderTarget};
        mRadiance[i].reset(device.CreateTexture(descRGBA, (std::string("Radiance ") + std::to_string(i)).c_str()));
        rendererSystem->InitializeTexture(mRadiance[i].get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);

        const NGITextureDesc descR16{GraphicsFormat::R16_SFloat, NGITextureType::Texture2D, 1, 1, width, height, 1, 1, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess};
        mVariance[i].reset(device.CreateTexture(descR16, (std::string("Variance ") + std::to_string(i)).c_str()));
        rendererSystem->InitializeTexture(mVariance[i].get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);

        mSampleCount[i].reset(device.CreateTexture(descR16, (std::string("Sample Count ") + std::to_string(i)).c_str()));
        rendererSystem->InitializeTexture(mSampleCount[i].get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
    }
}

void StochasticSSRTemporalState::DelayDeleteNGIResource() 
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (auto i = 0; i < 2; i++)
    {
        if (mRadiance[i])
        {
            rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(mRadiance[i].release()));
        }
        if (mVariance[i])
        {
            rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(mVariance[i].release()));
        }
        if (mSampleCount[i])
        {
            rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(mSampleCount[i].release()));
        }
    }
}

void StochasticSSRTemporalState::InitTextureView(RenderingExecutionDescriptor* red, ComputeShaderR* clearShader, bool bEnableStochasticDenoiser)
{
    for (auto i = 0; i < 2; i++)
    {
        auto radianceTex = red->AllocateTexture("Radiance " + std::to_string(i), mRadiance[i].get());
        mRadianceViewRT[i] = red->AllocateTextureView(radianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget, radianceTex->mDesc.Format));
        mRadianceViewRT[i]->SetExternalState(NGIResourceState::TargetReadWrite | NGIResourceState::PixelShaderBit);
        mRadianceView[i] = red->AllocateTextureView(radianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, radianceTex->mDesc.Format));
        mRadianceView[i]->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mRadianceViewSRV[i] = red->AllocateTextureView(radianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, radianceTex->mDesc.Format));
        mRadianceViewSRV[i]->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        if (bEnableStochasticDenoiser)
        {
            auto varianceTex = red->AllocateTexture("Variance " + std::to_string(i), mVariance[i].get());
            mVarianceView[i] = red->AllocateTextureView(varianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, varianceTex->mDesc.Format));
            mVarianceView[i]->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
            mVarianceViewSRV[i] = red->AllocateTextureView(varianceTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, varianceTex->mDesc.Format));
            mVarianceViewSRV[i]->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

            auto sampleCountTex = red->AllocateTexture("SampleCount " + std::to_string(i), mSampleCount[i].get());
            mSampleCountView[i] = red->AllocateTextureView(sampleCountTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, sampleCountTex->mDesc.Format));
            mSampleCountView[i]->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
            mSampleCountViewSRV[i] = red->AllocateTextureView(sampleCountTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, sampleCountTex->mDesc.Format));
            mSampleCountViewSRV[i]->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
        }
    }

    if (bEnableStochasticDenoiser)
    {
        if (mTexInited)
        {
            auto* clearPass = red->AllocatePass("StochasticSSR ClearTextureFrame", true);
            UInt32 width = mRadiance[0]->GetDesc().Width;
            UInt32 height = mRadiance[0]->GetDesc().Height;
            clearPass->SetProperty(NAME_ID("g_buffer_dimensions"), UInt2(width, height));
            clearPass->SetProperty(NAME_ID("variance0"), mVarianceView[mCurrentIndex]);
            clearPass->SetProperty(NAME_ID("sample_count0"), mSampleCountView[mCurrentIndex]);

            UInt3 groupSize;
            clearShader->GetThreadGroupSize("clearVarianceAndSampleCountCS", groupSize.x, groupSize.y, groupSize.z);
            clearPass->Dispatch(clearShader, "clearVarianceAndSampleCountCS", (width + groupSize.x - 1) / groupSize.x, (height + groupSize.y - 1) / groupSize.y, 1);
        }
        else
        {
            mTexInited = true;
            auto* clearPass = red->AllocatePass("StochasticSSR ClearTexture", true);
            UInt32 width = mRadiance[0]->GetDesc().Width;
            UInt32 height = mRadiance[0]->GetDesc().Height;
            clearPass->SetProperty(NAME_ID("g_buffer_dimensions"), UInt2(width, height));
            clearPass->SetProperty(NAME_ID("radiance0"), mRadianceView[0]);
            clearPass->SetProperty(NAME_ID("radiance1"), mRadianceView[1]);
            clearPass->SetProperty(NAME_ID("variance0"), mVarianceView[0]);
            clearPass->SetProperty(NAME_ID("variance1"), mVarianceView[1]);
            clearPass->SetProperty(NAME_ID("sample_count0"), mSampleCountView[0]);
            clearPass->SetProperty(NAME_ID("sample_count1"), mSampleCountView[1]);
            clearPass->SetProperty(NAME_ID("ENABLE_STOCHASTIC_DENOISER"), bEnableStochasticDenoiser);

            UInt3 groupSize;
            clearShader->GetThreadGroupSize("clearTemporalTexCS", groupSize.x, groupSize.y, groupSize.z);
            clearPass->Dispatch(clearShader, "clearTemporalTexCS", (width + groupSize.x - 1) / groupSize.x, (height + groupSize.y - 1) / groupSize.y, 1);
        }
    }
    else if (!mTexInited)
    {
        mTexInited = true;
        auto* clearPass = red->AllocatePass("StochasticSSR ClearTexture", true);
        UInt32 width = mRadiance[0]->GetDesc().Width;
        UInt32 height = mRadiance[0]->GetDesc().Height;
        clearPass->SetProperty(NAME_ID("g_buffer_dimensions"), UInt2(width, height));
        clearPass->SetProperty(NAME_ID("radiance0"), mRadianceView[0]);
        clearPass->SetProperty(NAME_ID("radiance1"), mRadianceView[1]);
        clearPass->SetProperty(NAME_ID("ENABLE_STOCHASTIC_DENOISER"), bEnableStochasticDenoiser);

        UInt3 groupSize;
        clearShader->GetThreadGroupSize("clearTemporalTexCS", groupSize.x, groupSize.y, groupSize.z);
        clearPass->Dispatch(clearShader, "clearTemporalTexCS", (width + groupSize.x - 1) / groupSize.x, (height + groupSize.y - 1) / groupSize.y, 1);
    }
}

}   // namespace cross