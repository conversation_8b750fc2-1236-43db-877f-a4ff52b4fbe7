#pragma once

#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include <array>

#define REALTIME_CAPTURE_MAX_SLICE 11

namespace cross {

class IGPUTexture;
using SkyLightDiffuseProbe = std::array<Float4, 7>;

struct SkyRealTimeCaptureGenMipCSParam
{
    PassREDTextureParam GenNextMipRenderTargetTexture{NAME_ID("_GenNextMipRenderTarget"), nullptr};
    PassREDTextureParam SourceColorMapTexture{NAME_ID("_InColorMap"), nullptr};
    PassFloat4x4Param InvViewMatrix{NAME_ID("_InvViewMatrix"), Float4x4::Identity()};
    PassFloat4x4Param InvProjMatrix{NAME_ID("_InvProjMatrix"), Float4x4::Identity()};
    PassFloat3Param CameraPos{NAME_ID("_CameraPos"), Float3::Zero()};
    PassFloatParam NextMipNormalized{NAME_ID("_NextMipNormalized"), 0.0f};
    PassIntParam CurrentMip{NAME_ID("_CurrentMip"), 0};
    PassIntParam Mip0Size{NAME_ID("_Mip0Size"), 128};
    PassFloatParam NumSamples{NAME_ID("_NumSamples"), 1024};
    PassUIntParam NumMips{NAME_ID("_NumMips"), 7};
    PassUIntParam CurrentCubeFace{NAME_ID("_CubeFace"), 0};
    PassBoolParam ReverseZ{NAME_ID("_REVERSE_Z"), true};
};

struct SkyRealTimeCaptureComputeEnvMapDiffuseIrradianceParam
{
    PassREDTextureParam SourceColorMapTexture{NAME_ID("_InColorMap"), nullptr};
    PassIntParam MipIndex{NAME_ID("_MipIndex"), 3};
    PassFloatParam UniformSampleSolidAngle{NAME_ID("_UniformSampleSolidAngle"), 4.0f * 3.1415926f / 64.0f};
    PassREDBufferParam OutIrradianceEnvMapSH{NAME_ID("OutIrradianceEnvMapSH"), nullptr};
};

struct SkyRealTimeCaptureLowerHemisphereColorCSParam
{
    PassREDTextureParam OutTextureMipColor{NAME_ID("OutTextureMipColor"), nullptr};
    PassFloat4Param LowerHemisphereSolidColor{NAME_ID("LowerHemisphereSolidColor"), Float4{0.0f, 0.0f, 0.0f, 1.0f}};
    PassBoolParam TODLowerHemisphereSolidColor{NAME_ID("TODLowerHemisphereSolidColor"), true};
    PassIntParam CurrentMip{NAME_ID("_CurrentMip"), 0};
    PassIntParam CubeFace{NAME_ID("_CubeFace"), 0};
    PassUIntParam FaceThreadGroupSize{NAME_ID("FaceThreadGroupSize"), 128};
};

struct SkyLightComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();
    SkyLightComponentR(bool enable, Float3 color, float intensity)
        : mEnable(false)
        , mLightColor(color)
        , mSkyLightIntensity(intensity)
    {}
    SkyLightComponentR() {}
protected:
    bool mEnable{ true };
    bool mRealTimeCapture{false};
    SInt32 mRealTimeCaptureSliceCount{REALTIME_CAPTURE_MAX_SLICE};
    Float3 mLightColor{1.f,1.f,1.f};
    float mSkyLightIntensity{1.0f};
    float mLightMapIntensityDebug{0.0f};
    SkyLightDiffuseProbe mDiffuseProbe;
    IGPUTexture* mSpecularProbe = nullptr;
    bool mEnableSmartGISample{false};
    bool bLowerHemiSolidColor{true};
    bool TODLowerHemiSolidColor{true};
    Float4 LowerHemisphereColor{0.0f, 0.0f, 0.0f, 1.0f};
    bool EnableSkyLightBlend{false};
    bool ForceUpdateSkyLight{false};

    bool mEnableSpecular{false};
    bool mEnableSSGI{true};
    float mRayLength{100.0f};
    float mRayLength_Foliage{20.0f};
    float mThresholdScale{1.5f};
    float mMinimalAO{0.0f};
    float mIntensityScale{2.5f};
    float mDepthThresholdNear{100.0f};
    float mDepthThresholdFar{1000.0f};
    float mDepthThresholdNear_Foliage{100.0f};
    float mDepthThresholdFar_Foliage{100.0f};
    float mAOFactor{2.0f};
    float mSSGIScale{2.5f};

    friend class SkyLightSystemR;
};

class SkyLightSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    using RenderSkyLightComponentHandle = ecs::ComponentHandle<SkyLightComponentR>;
    using RenderSkyLightComponentReader = ecs::ScopedComponentRead<SkyLightComponentR>;

    RENDER_ENGINE_API static SkyLightSystemR* CreateInstance()
    {
        return new SkyLightSystemR;
    }

    virtual void Release() override;

    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    const NGIBufferView* GetDiffuseProbe(const RenderSkyLightComponentReader& lightHandle) const;

    IGPUTexture* GetSpecularProbe(const RenderSkyLightComponentReader& lightHandle);

    RENDER_ENGINE_API void ResetState();

    RENDER_ENGINE_API void SetEnable(ecs::EntityID entity, bool enable);

    RENDER_ENGINE_API void SetRealTimeCapture(ecs::EntityID entity, bool enable);

    RENDER_ENGINE_API void SetRealTimeCaptureSliceCount(ecs::EntityID entity, SInt32 SliceNum);

    RENDER_ENGINE_API void SetDiffuseProbe(ecs::EntityID entity, const SkyLightDiffuseProbe&& diffuseProbe);

    RENDER_ENGINE_API void SetSpecularProbe(ecs::EntityID entity, IGPUTexture* specularProbe);

    RENDER_ENGINE_API void SetLightColor(ecs::EntityID entity, Float3&& lightColor);

    RENDER_ENGINE_API void SetLightMapIntensityDebug(ecs::EntityID entity, float lightMapIntensity);

    //RENDER_ENGINE_API void SetSkyLightIntensity(ecs::EntityID entity, float prtIntensity);

    RENDER_ENGINE_API void SetSkyLightIntensity(ecs::EntityID entity, float giLightIntensity);

    RENDER_ENGINE_API void SetIsLowerHemisphereColor(ecs::EntityID entity, bool bLowerHemiSolid);

    RENDER_ENGINE_API void SetTODLowerHemisphereColor(ecs::EntityID entity, bool bTODLowerHemiSolid);

    RENDER_ENGINE_API void SetLowerHemisphereColor(ecs::EntityID entity, Float4 LowerHemiColor);

    RENDER_ENGINE_API void SetEnableSkyLightBlend(ecs::EntityID entity, bool value);

    RENDER_ENGINE_API void SetForceUpdateSkyLight(ecs::EntityID entity, bool value);

    RENDER_ENGINE_API void ForceUpdateSkyLight()
    {
        mForceUpdateInteralFlag = true;
    }

    NGIBufferView* GetDiffuseProbe() const
    {
        return mSHbufferView;
    };

    void AddSkyDiffuseSH(std::array<Float4, 3>& shData) 
    {
        shData[0] += (GetSkyLight()->mDiffuseProbe[0] * GetSkyLight()->mLightColor.x * GetSkyLight()->mSkyLightIntensity);
        shData[1] += (GetSkyLight()->mDiffuseProbe[1] * GetSkyLight()->mLightColor.y * GetSkyLight()->mSkyLightIntensity);
        shData[2] += (GetSkyLight()->mDiffuseProbe[2] * GetSkyLight()->mLightColor.z * GetSkyLight()->mSkyLightIntensity);
    }

    const auto& GetColor() const 
    { 
        return GetSkyLight()->mLightColor;
    }

    auto GetLightMapIntensityDebug() const
    {
        return GetSkyLight()->mLightMapIntensityDebug;
    }

    NGITextureView* GetSkyLightTexture() const;
    

    auto GetSkyLightIntensity() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mEnable ? skyLightComp->mSkyLightIntensity : 0.0f;
    }

    bool IsSkyLightRealtimeCapture() const
    {
        return GetSkyLight()->mRealTimeCapture;
    }

    void RenderSkylightTextures();

    auto GetBlendSourceCubemap() const
    {
        return mBlendSourceCubemap.get();
    }

    auto GetBlendSourceCubemapView() const
    {
        return mBlendSourceCubemapView.get();
    }

    auto GetBlendFactor() const
    {
        return mBlendFactor;
    }

    /*auto EnableGISample() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mEnableSmartGISample;
    }

    auto GetEnableSpecular() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mEnableSpecular;
    }

    auto GetEnableSSGI() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mEnableSSGI;
    }

    auto GetRayLength() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mRayLength;
    }

    auto GetRayLength_Foliage() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mRayLength_Foliage;
    }

    auto GetThresholdScale() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mThresholdScale;
    }

    auto GetMinimalAO() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mMinimalAO;
    }

    auto GetIntensityScale() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mIntensityScale;
    }

    auto GetDepthThresholdNear() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mDepthThresholdNear;
    }

    auto GetDepthThresholdFar() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mDepthThresholdFar;
    }

    auto GetDepthThresholdNear_Foliage() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mDepthThresholdNear_Foliage;
    }

    auto GetDepthThresholdFar_Foliage() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mDepthThresholdFar_Foliage;
    }

    auto GetAOFactor() const
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mAOFactor;
    }

    auto GetSSGIScale() const 
    {
        const auto& skyLightComp = GetSkyLight();
        return skyLightComp->mSSGIScale;
    }*/

private:
    const SkyLightComponentR* GetSkyLight() const;

    const SkyLightComponentR& GetZeroSkyLight() const
    {
        static SkyLightComponentR zero_skylight = SkyLightComponentR(false, Float3(0.0, 0.0, 0.0), 0.0);
        return zero_skylight;
    }

private:
    void ResetDiffuseSHBuffer()
    {
        mSHbufferView = mDefaultDiffuseSHBufferNGIView.get();
    }

    void PrepareSkyViewRealTimeCaptureTexture();

    FORCE_INLINE bool GetHasReadyCubeMap() const
    {
        return mRealTimeCapture && mRealTimeSlicedReflectionCaptureState > 0;
    }

    FORCE_INLINE UInt32 GetReadyCubeMapIndex() const
    {
        return mReadyRealtimeCaptureIndex;
    }

    FORCE_INLINE UInt32 GetWorkingCubeMapIndex() const
    {
        return 1 - mReadyRealtimeCaptureIndex;
    }

    FORCE_INLINE UInt32 GetSkyRTCubeMapIndex() const
    {
        return 2;
    }

private:
    std::unique_ptr<NGIBuffer> mDefaultDiffuseSHBuffer;
    std::unique_ptr<NGIBufferView> mDefaultDiffuseSHBufferNGIView;

private:
    friend class SkyLightSystemG;

    struct DefaultDepthTextureViews
    {
        std::unique_ptr<NGITexture> DepthTexture;
        std::unique_ptr<NGITextureView> DepthTextureNGIView;
        REDTextureRef DepthTextureREDTexture;
        REDTextureView* DepthTextureREDTextureView;
    };

    struct SkyViewRealTimeCaptureTexturesAndBuffer
    {
        std::unique_ptr<NGITexture> CubeMapTexture;
        std::unique_ptr<NGITextureView> CubeMapNGIView;
        REDTextureRef CubeMapREDTexture;

        std::unique_ptr<NGIBuffer> DiffuseSHBuffer;
        std::unique_ptr<NGIBufferView> DiffuseSHBufferNGIView;
        REDBufferRef DiffuseSHREDBuffer;
    };
    
    REDUniquePtr<REDResidentTexture> mBlendSourceCubemap = nullptr;
    REDUniquePtr<REDResidentTextureView> mBlendSourceCubemapView = nullptr;
    float mBlendFactor = 0.f;
    bool mForceUpdateInteralFlag = false;

    // 0 and 1 : Double buffer for time slice
    // 2: SkyRT
    SkyViewRealTimeCaptureTexturesAndBuffer mSkyViewRealTimeCaptureTexturesAndBuffer[3];
    
    // Double buffer index
    UInt32 mReadyRealtimeCaptureIndex = 1;

    // State index
    SInt32 mRealTimeSlicedReflectionCaptureState = -1;
    SInt32 mRealTimeSlicedReflectionCaptureSliceCount = 1;

    DefaultDepthTextureViews mDefaultDepthTextures;

    ComputeShaderR* mComputeShader = nullptr;

    bool mRealTimeCapture{false};

    NGIBufferView* mSHbufferView = nullptr;

private:
    void SetBlendSourceCubemap();
};

}   // namespace cross