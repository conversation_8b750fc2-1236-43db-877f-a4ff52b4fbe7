#include "VirtualShadowMapArray.h"
#include "RenderEngine/RenderPipeline/Effects/Shadow.h"
#include "RenderEngine/RenderPipeline/Effects/FoliageGpuDriven.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "NGIManager.h"
#include "RenderEngine/ShadowSystemR.h"
#include "RenderEngine/FoliageSystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/FoliageSystemR.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/InstanceCulling/InstanceCulling.h"
#include "VirtualShadowMapClipmap.h"
#include "VirtualShadowMapCache.h"

namespace cross {
struct PhysicalPageMetaData
{
    UInt32 flags;
    UInt32 age;
};

template<class T>
NGIBufferView* CreateStructuredBuffer(const FrameStdVector<T>& data)
{
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    SizeType dataByteSize = sizeof(T) * data.size();

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto bufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataByteSize);
    bufferWrap.MemWrite(0, data.data(), dataByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            bufferWrap.GetNGIOffset(),
            dataByteSize,
            GraphicsFormat::Unknown,
            sizeof(T),
        },
        bufferWrap.GetNGIBuffer());
}

void VirtualShadowMapArray::InitContext(RenderWorld* renderWorld, FFSRenderPipeline* renderPipeline, VirtualShadowMapSettings* shadowSettings)
{
    mRenderWorld = renderWorld;
    mWorldRenderPipeline = static_cast<FFSWorldRenderPipeline*>(renderPipeline->GetWorldRenderPipeline());
    mRenderPipeline = renderPipeline;
    mRED = renderPipeline->GetRenderingExecutionDescriptor();
    mMemoryPool = mRED->GetREDFrameAllocator();
    mShadowSettings = shadowSettings;
    mPhysicalPagePool = mWorldRenderPipeline->GetVirtualShadowMapPhysicalPagePool();
}

void VirtualShadowMapArray::RenderVirtualShadowMaps(REDTextureView* depthView, REDTextureView* normalBufferView, UInt32 _frameCount)
{
    QUICK_SCOPED_CPU_TIMING("RenderVirtualShadowMaps");

    UInt32 frameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
    bool isPhysicalPageDimChanged = false;

    // handle PhysicalPageDim change
    {
        Assert(IsPowerOfTwo(mShadowSettings->PhysicalPagesDimX) && IsPowerOfTwo(mShadowSettings->PhysicalPagesDimY));

        isPhysicalPageDimChanged = mShadowSettings->PhysicalPagesDimX != mPhysicalPagesDim.x || mShadowSettings->PhysicalPagesDimY != mPhysicalPagesDim.y;

        if (isPhysicalPageDimChanged)
        {
            mPhysicalPagesDim = UInt2(std::max(mShadowSettings->PhysicalPagesDimX, 1u), std::max(mShadowSettings->PhysicalPagesDimY, 1u));
            mPhysicalPagesCount = mPhysicalPagesDim.x * mPhysicalPagesDim.y;
            mPhysicalPageRowMask = mPhysicalPagesDim.x - 1;
            mPhysicalPageRowShift = (UInt32)std::log2(mPhysicalPagesDim.x);
        }
    }

    if (frameCount - mFrameCount > 1 || isPhysicalPageDimChanged)
    {
        ClearCacheData();
    }
    mFrameCount = frameCount;

    if (IsCacheValid())
    {
        ProcessInvalidation();
    }

    GatherLightsRenderVSM();

    if (!mLightsRenderVSM.empty())
    {
        BuildPageAllocations(depthView, normalBufferView);

        RenderShadowDepth();
    }
    else
    {
        ClearCacheData();
    }
}

void VirtualShadowMapArray::RenderShadowMask(REDTextureView* depthView, std::array<REDTextureView*, 4>& gBufferViews, NGIBufferView* lightDataBufferView, UInt32 lightCount, REDTextureView* shadowMaskView, ShadowProperties* shadowProperties)
{
    QUICK_SCOPED_CPU_TIMING("RenderVSMShadowMask");

    const UInt2 screenSize = mRenderPipeline->GetRenderSize();
    const Float4 screenSizeAndInvSize{static_cast<float>(screenSize.x), static_cast<float>(screenSize.y), 1.0f / static_cast<float>(screenSize.x), 1.0f / static_cast<float>(screenSize.y)};
    const UInt4 uintParams0(mFrameCount, lightCount, mShadowSettings->SMRTRayCount, mShadowSettings->SMRTSamplesPerRay);
    const Float4 floatParams0(mShadowSettings->ContactShadowLength, 0, 0, 0);
    shadowProperties->screenSizeAndInvSize = screenSizeAndInvSize;
    shadowProperties->uintParams0 = uintParams0;
    shadowProperties->floatParams0 = floatParams0;
    shadowProperties->depthView = depthView;
    shadowProperties->gBufferView2 = gBufferViews[2];

    if (mLightsRenderVSM.empty())
    {
        return;
    }

    {
        auto* pass = mRED->AllocatePass("Shadow.Virtual.RenderShadowMask");
        {
            mRED->SetProperty(NAME_ID("_ScreenSizeAndInvSize"), screenSizeAndInvSize);
            mRED->SetProperty(NAME_ID("_UIntParams0"), uintParams0);
            mRED->SetProperty(NAME_ID("_FloatParams0"), floatParams0);
            pass->SetProperty(NAME_ID("_DepthMap"), depthView);
            pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), mShadowProperties.projectionDataBufferSRV);
            pass->SetProperty(NAME_ID("_PageTable"), mShadowProperties.pageTableBufferSRV);
            pass->SetProperty(NAME_ID("_PhysicalPagePool"), mShadowProperties.physicalPagePoolUAV);
            pass->SetProperty(NAME_ID("_NormalBuffer"), gBufferViews[1]);
            pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            pass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3]);
            pass->SetProperty(NAME_ID("_LightDatas"), lightDataBufferView);
            pass->SetProperty(NAME_ID("_ShadowMasks"), shadowMaskView);
            pass->SetProperty(NAME_ID("ENABLE_FOLIAGEONLY_CONTACTSHADOW"), mShadowSettings->OptimizeFoliageShadowPerformance);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapProjectionComputeShaderR->GetThreadGroupSize("RenderShadowMask", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapProjectionComputeShaderR, NAME_ID("RenderShadowMask"), math::DivideAndRoundUp(screenSize.x, groupSize.x), math::DivideAndRoundUp(screenSize.y, groupSize.y), 1);
        }
    }
}

void VirtualShadowMapArray::ClearCacheData()
{
    mCache.Reset();
}

void VirtualShadowMapArray::ProcessInvalidation()
{
    QUICK_SCOPED_CPU_TIMING("ProcessInvalidatation");
    auto* entityLifeCycleRenderDataSys = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();
    auto* renderNodeSys = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();

    const auto& renderEntityRemoveList = entityLifeCycleRenderDataSys->GetRenderEntityVSMUpdateList();
    const auto& renderEntityChangeList = entityLifeCycleRenderDataSys->GetRenderEntityChangeList();

    // temp
    FrameStdVector<PrimitiveCullingData> cullingSceneDatas(mMemoryPool);
    cullingSceneDatas.reserve(renderEntityRemoveList.size() + renderEntityChangeList.size());

    for (const auto& removeData : renderEntityRemoveList)
    {
        if (removeData.isCastShadow)
        {
            cullingSceneDatas.push_back(PrimitiveCullingData{removeData.boundingBox.GetCenter(), 0, removeData.boundingBox.GetExtent(), 0, removeData.tilePosition, 0});
        }
    }

    for (const auto& changeData : renderEntityChangeList)
    {
        if (!mRenderWorld->IsEntityAlive(changeData.entity))
            continue;

        auto renderNodeReader = mRenderWorld->GetComponent<RenderNodeComponentR>(changeData.entity).Read();
        const auto* renderNode = renderNodeSys->GetRenderNode(renderNodeReader);

        if (renderNode->IsCastShadow() || EnumHasAnyFlags(changeData.type, RenderEntityChangeData::ChangeType::RenderEffect))
        {
            cullingSceneDatas.push_back(PrimitiveCullingData{changeData.prevBoundingBox.GetCenter(), 0, changeData.prevBoundingBox.GetExtent(), 0, changeData.prevTilePosition, 0});
        }
    }

    if (cullingSceneDatas.empty())
    {
        return;
    }

    NGIBufferView* cullingSceneDataBuffer = CreateStructuredBuffer(cullingSceneDatas);
    NGIBufferView* projectionDataBuffer = CreateStructuredBuffer(mCache.mFrameData.projectionData);
    REDBufferView* pageFlagsBufferSRV = CreateSRVBufferView(mCache.mFrameData.pageFlagsBuffer, sizeof(UInt32));
    REDBufferView* pageRectBoundsBufferSRV = CreateSRVBufferView(mCache.mFrameData.pageRectBoundsBuffer, sizeof(UInt4));
    REDBufferView* dynamicCasterPageFlagsBufferUAV = CreateUAVBufferView(mCache.mFrameData.dynamicCasterPageFlagsBuffer, sizeof(UInt32));

    const UInt32 instanceCount = static_cast<UInt32>(cullingSceneDatas.size());

    // VirtualSmInvalidateInstancePages
    {
        auto* pass = mRED->AllocatePass("VirtualSmInvalidateInstancePages");
        {
            pass->SetProperty(NAME_ID("_UIntParams0"), UInt4(0, mCache.mFrameData.virtualShadowMapCount, instanceCount, 0));
            pass->SetProperty(NAME_ID("_PageFlags"), pageFlagsBufferSRV);
            pass->SetProperty(NAME_ID("_PageRectBounds"), pageRectBoundsBufferSRV);
            pass->SetProperty(NAME_ID("_GPUSceneCullingSceneDatas"), cullingSceneDataBuffer);
            pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), projectionDataBuffer);
            pass->SetProperty(NAME_ID("_OutDynamicCasterPageFlags"), dynamicCasterPageFlagsBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapCacheManagementComputeShaderR->GetThreadGroupSize(NAME_ID("VirtualSmInvalidateInstancePages"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapCacheManagementComputeShaderR, NAME_ID("VirtualSmInvalidateInstancePages"), math::DivideAndRoundUp(instanceCount, groupSize.x), mCache.mFrameData.virtualShadowMapCount, 1);
        }
    }
}

void VirtualShadowMapArray::GatherLightsRenderVSM()
{
    QUICK_SCOPED_CPU_TIMING("GatherLightsRenderVSM");
    mLightsRenderVSM = std::move(FrameStdVector<std::pair<ecs::EntityID, VirtualShadowMapClipmap*>>(mMemoryPool));
    mVirtualShadowMapCount = 0;

    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();
    auto cameraEntity = mRenderPipeline->GetCamera();

    for (auto lightEntity : mRenderPipeline->GetLightList())
    {
        auto shadowComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(lightEntity);

        if (shadowSys->IsShadowEnable(cameraEntity, lightEntity))
        {
            // TODO(yazhenyuan): conflicts during parallizing
            auto* virtualShadowMapClipmap = shadowSys->GetVirtualShadowMapClipmap(shadowComp.Write(), cameraEntity);

            if (virtualShadowMapClipmap)
            {
                virtualShadowMapClipmap->SetShadowMapId(mVirtualShadowMapCount);

                mLightsRenderVSM.push_back({lightEntity, virtualShadowMapClipmap});
                mVirtualShadowMapCount += virtualShadowMapClipmap->GetLevelCount();
            }
        }
    }
}

void VirtualShadowMapArray::BuildPageAllocations(REDTextureView* depthView, REDTextureView* normalBufferView)
{
    QUICK_SCOPED_CPU_TIMING("BuildPageAllocation");
    mRED->BeginRegion("Shadow.Virtual.BuildPageAllocation");

    // ProjectionData + CacheData + DirectionalLightIds
    FrameStdVector<VirtualShadowMapProjectionData> projectionData(mMemoryPool);
    projectionData.resize(mVirtualShadowMapCount);

    FrameStdVector<VirtualShadowMapCacheData> cacheData(mMemoryPool);
    cacheData.resize(mVirtualShadowMapCount);

    FrameStdVector<UInt32> directionalLightIds(mMemoryPool);

    for (auto& [lightEntity, virtualShadowMapClipmap] : mLightsRenderVSM)
    {
        SInt32 baseId = virtualShadowMapClipmap->GetVirtualShadowMap()->GetId();

        for (UInt32 levelIndex = 0; levelIndex < virtualShadowMapClipmap->GetLevelCount(); levelIndex++)
        {
            projectionData[baseId + levelIndex] = virtualShadowMapClipmap->GetProjectionData(levelIndex);
            cacheData[baseId + levelIndex].prevVirtualShadowMapId = virtualShadowMapClipmap->GetVirtualShadowMap(levelIndex)->GetPrevId();
        }

        directionalLightIds.push_back(baseId);
    }

    NGIBufferView* projectionDataBufferSRV = CreateStructuredBuffer(projectionData);
    NGIBufferView* cacheDataBufferSRV = CreateStructuredBuffer(cacheData);
    NGIBufferView* directionalLightIdsBufferSRV = CreateStructuredBuffer(directionalLightIds);

    // Global Shader Proeprties
    const UInt32 numShadowMapsToAllocate = mVirtualShadowMapCount;
    const UInt32 numDirectionalLight = static_cast<UInt32>(directionalLightIds.size());
    const UInt32 numPageFlagsToAllocate = numShadowMapsToAllocate * VirtualShadowMap::PageTableSize;
    const UInt32 numPageRects = numShadowMapsToAllocate * VirtualShadowMap::MaxMipLevels;

    const UInt2 screenSize = mRenderPipeline->GetRenderSize();
    const Float4 screenSizeAndInvSize{static_cast<float>(screenSize.x), static_cast<float>(screenSize.y), 1.0f / static_cast<float>(screenSize.x), 1.0f / static_cast<float>(screenSize.y)};
    const UInt4 uintParams0(numDirectionalLight, mPhysicalPagesCount, mPhysicalPageRowMask, mPhysicalPageRowShift);
    const UInt4 uintParams1(numShadowMapsToAllocate, VirtualShadowMapClipmap::GetCoarsePageClipmapIndexMask(*mShadowSettings), IsCacheValid(), 0);
    const Float4 floatParams0(mShadowSettings->ContactShadowLength, 0, 0, 0);

    mRED->SetProperty(NAME_ID("_ScreenSizeAndInvSize"), screenSizeAndInvSize);
    mRED->SetProperty(NAME_ID("_UIntParams0"), uintParams0);
    mRED->SetProperty(NAME_ID("_UIntParams1"), uintParams1);
    mRED->SetProperty(NAME_ID("_FloatParams0"), floatParams0);

    // Buffer Resource
    auto [pageRectBoundsBuffer, pageRectBoundsBufferUAV, pageRectBoundsBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PageRectBounds", numPageRects, sizeof(UInt4));
    auto [pageRequestBuffer, pageRequestFlagsBufferUAV, pageRequestFlagsBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PageRequestFlags", numPageFlagsToAllocate, sizeof(UInt32));
    auto [pageTableBuffer, pageTableBufferUAV, pageTableBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PageTable", numPageFlagsToAllocate, sizeof(UInt32));
    auto [pageFlagsBuffer, pageFlagsBufferUAV, pageFlagsBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PageFlags", numPageFlagsToAllocate, sizeof(UInt32));
    auto [dynamicCasterPageFlagsBuffer, dynamicCasterPageFlagsBufferUAV, dynamicCasterPageFlagsBufferSRV] = CreateUAVBuffer("Shadow.Virtual.DynamicCasterPageFlags", numPageFlagsToAllocate, sizeof(UInt32));
    auto [physicalPageMetaDataBuffer, physicalPageMetaDataBufferUAV, physicalPageMetaDataBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PhysicalPageMetaData", mPhysicalPagesCount, sizeof(PhysicalPageMetaData));
    auto [physicalPageWorldMetaDataBufferSRV, physicalPageWorldMetaDataBufferUAV] = mPhysicalPagePool->GetPhysicalPageWorldMetaDataBufferViews();
    auto [freePhysicalPagesBuffer, freePhysicalPagesBufferUAV, freePhysicalPagesBufferSRV] = CreateUAVBuffer("Shadow.Virtual.FreePhysicalPages", mPhysicalPagesCount + 1, sizeof(UInt32));

    const UInt2 physicalPagePoolSize = {mPhysicalPagesDim.x * VirtualShadowMap::PageSize, mPhysicalPagesDim.y * VirtualShadowMap::PageSize};
    auto [physicalPagePoolSRV, physicalPagePoolUAV] = mPhysicalPagePool->GetPhysicalPagePoolViews();

    // Cache Buffer Resource
    NGIBufferView* prevProjectionDataBufferSRV = nullptr;
    REDBufferView* prevPageTableBufferSRV = nullptr;
    REDBufferView* prevPageFlagsBufferSRV = nullptr;
    REDBufferView* prevDynamicCasterPageFlagsBufferSRV = nullptr;
    REDBufferView* prevPhysicalPageMetaBufferSRV = nullptr;
    REDBufferView* prevPhysicalPageMetaBufferUAV = nullptr;

    if (IsCacheValid())
    {
        prevProjectionDataBufferSRV = CreateStructuredBuffer(mCache.mFrameData.projectionData);
        prevPageTableBufferSRV = CreateSRVBufferView(mCache.mFrameData.pageTableBuffer, sizeof(UInt32));
        prevPageFlagsBufferSRV = CreateSRVBufferView(mCache.mFrameData.pageFlagsBuffer, sizeof(UInt32));
        prevDynamicCasterPageFlagsBufferSRV = CreateSRVBufferView(mCache.mFrameData.dynamicCasterPageFlagsBuffer, sizeof(UInt32));
        prevPhysicalPageMetaBufferSRV = CreateSRVBufferView(mCache.mFrameData.physicalPageMetaDataBuffer, sizeof(PhysicalPageMetaData));
        prevPhysicalPageMetaBufferUAV = CreateUAVBufferView(mCache.mFrameData.physicalPageMetaDataBuffer, sizeof(PhysicalPageMetaData));
    }
    else
    {
        FrameStdVector<UInt32> temp(mMemoryPool);
        temp.emplace_back(0);
        prevProjectionDataBufferSRV = CreateStructuredBuffer(temp);

        prevPageTableBufferSRV = CreateSRVEmptyBuffer();
        prevPageFlagsBufferSRV = CreateSRVEmptyBuffer();
        prevDynamicCasterPageFlagsBufferSRV = CreateSRVEmptyBuffer();
        prevPhysicalPageMetaBufferSRV = CreateSRVEmptyBuffer();
        prevPhysicalPageMetaBufferUAV = CreateUAVEmptyBuffer();
    }

    // InitPageRectBounds
    {
        auto* pass = mRED->AllocatePass("InitPageRectBounds");
        {
            pass->SetProperty(NAME_ID("_OutPageRectBounds"), pageRectBoundsBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("InitPageRectBounds"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("InitPageRectBounds"), math::DivideAndRoundUp(numPageRects, groupSize.x), 1, 1);
        }
    }

    // Clear PageRequestFlagsBuffer
    {
        ClearRWStructuredBuffer(pageRequestFlagsBufferUAV, 0);
    }

    // Clear DynamicCasterPageFlagsBuffer
    {
        ClearRWStructuredBuffer(dynamicCasterPageFlagsBufferUAV, 0);
    }

    // GeneratePageFlagsFromPixels
    {
        auto* pass = mRED->AllocatePass("GeneratePageFlagsFromPixels");
        {
            pass->SetProperty(NAME_ID("_DepthMap"), depthView);
            pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), projectionDataBufferSRV);
            pass->SetProperty(NAME_ID("_DirectionalLightIds"), directionalLightIdsBufferSRV);
            pass->SetProperty(NAME_ID("_OutPageRequestFlags"), pageRequestFlagsBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("GeneratePageFlagsFromPixels"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("GeneratePageFlagsFromPixels"), math::DivideAndRoundUp(screenSize.x, groupSize.x), math::DivideAndRoundUp(screenSize.y, groupSize.y), 1);
        }
    }

    // MaskCoarsePages
    if (mShadowSettings->MaskCoarsePagesDirectional)
    {
        auto* pass = mRED->AllocatePass("MaskCoarsePages");
        {
            pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), projectionDataBufferSRV);
            pass->SetProperty(NAME_ID("_OutPageRequestFlags"), pageRequestFlagsBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("MarkCoarsePages"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("MarkCoarsePages"), math::DivideAndRoundUp(mVirtualShadowMapCount, groupSize.x), 1, 1);
        }
    }

    // InitPhysicalPageMetaData
    {
        auto* pass = mRED->AllocatePass("InitPhysicalPageMetaData");
        {
            pass->SetProperty(NAME_ID("_OutPhysicalPageMetaData"), physicalPageMetaDataBufferUAV);
            pass->SetProperty(NAME_ID("_OutFreePhysicalPages"), freePhysicalPagesBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("InitPhysicalPageMetaData"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("InitPhysicalPageMetaData"), math::DivideAndRoundUp(mPhysicalPagesCount + 1, groupSize.x), 1, 1);
        }
    }

    // CreateCachedPageMappings
    {
        auto* pass = mRED->AllocatePass("CreateCachedPageMappings");
        {
            pass->SetProperty(NAME_ID("_VirtualShadowMapCacheDatas"), cacheDataBufferSRV);
            pass->SetProperty(NAME_ID("_PrevVirtualShadowMapProjectionDatas"), prevProjectionDataBufferSRV);
            pass->SetProperty(NAME_ID("_PrevPageTable"), prevPageTableBufferSRV);
            pass->SetProperty(NAME_ID("_PrevPageFlags"), prevPageFlagsBufferSRV);
            pass->SetProperty(NAME_ID("_PrevDynamicCasterPageFlags"), prevDynamicCasterPageFlagsBufferSRV);
            pass->SetProperty(NAME_ID("_OutPrevPhysicalPageMetaData"), prevPhysicalPageMetaBufferUAV);

            pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), projectionDataBufferSRV);
            pass->SetProperty(NAME_ID("_PageRequestFlags"), pageRequestFlagsBufferSRV);
            pass->SetProperty(NAME_ID("_OutPageTable"), pageTableBufferUAV);
            pass->SetProperty(NAME_ID("_OutPageFlags"), pageFlagsBufferUAV);
            pass->SetProperty(NAME_ID("_OutPhysicalPageMetaData"), physicalPageMetaDataBufferUAV);
            pass->SetProperty(NAME_ID("_OutPhysicalPageWorldMetaData"), physicalPageWorldMetaDataBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("CreateCachedPageMappings"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("CreateCachedPageMappings"), math::DivideAndRoundUp(VirtualShadowMap::PageTableSize, groupSize.x), mVirtualShadowMapCount, 1);
        }
    }

    // FreeUnusedPages
    {
        auto* pass = mRED->AllocatePass("FreeUnusedPages");
        {
            pass->SetProperty(NAME_ID("_PrevPhysicalPageMetaData"), prevPhysicalPageMetaBufferSRV);
            pass->SetProperty(NAME_ID("_OutPhysicalPageWorldMetaData"), physicalPageWorldMetaDataBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("FreeUnusedPages"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("FreeUnusedPages"), math::DivideAndRoundUp(mPhysicalPagesCount, groupSize.x), 1, 1);
        }
    }

    // PackFreePages
    {
        auto* pass = mRED->AllocatePass("PackFreePages");
        {
            pass->SetProperty(NAME_ID("_PhysicalPageWorldMetaData"), physicalPageWorldMetaDataBufferSRV);
            pass->SetProperty(NAME_ID("_OutFreePhysicalPages"), freePhysicalPagesBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("PackFreePages"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("PackFreePages"), math::DivideAndRoundUp(mPhysicalPagesCount, groupSize.x), 1, 1);
        }
    }

    // AllocateNewPageMappings
    {
        auto* pass = mRED->AllocatePass("AllocateNewPageMappings");
        {
            pass->SetProperty(NAME_ID("_PageRequestFlags"), pageRequestFlagsBufferSRV);
            pass->SetProperty(NAME_ID("_OutFreePhysicalPages"), freePhysicalPagesBufferUAV);
            pass->SetProperty(NAME_ID("_OutPhysicalPageMetaData"), physicalPageMetaDataBufferUAV);
            pass->SetProperty(NAME_ID("_OutPhysicalPageWorldMetaData"), physicalPageWorldMetaDataBufferUAV);
            pass->SetProperty(NAME_ID("_OutPageTable"), pageTableBufferUAV);
            pass->SetProperty(NAME_ID("_OutPageFlags"), pageFlagsBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("AllocateNewPageMappings"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("AllocateNewPageMappings"), math::DivideAndRoundUp(VirtualShadowMap::PageTableSize, groupSize.x), mVirtualShadowMapCount, 1);
        }
    }

    // GeneratePageRectAndHierarchicalPageFlags
    {
        auto* pass = mRED->AllocatePass("GeneratePageRectAndHierarchicalPageFlags");
        {
            pass->SetProperty(NAME_ID("_OutPageFlags"), pageFlagsBufferUAV);
            pass->SetProperty(NAME_ID("_OutPageRectBounds"), pageRectBoundsBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("GeneratePageRectAndHierarchicalPageFlags"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("GeneratePageRectAndHierarchicalPageFlags"), math::DivideAndRoundUp(VirtualShadowMap::PageTableSize, groupSize.x), mVirtualShadowMapCount, 1);
        }
    }

    // PropagateMappedMips
    {
        auto* pass = mRED->AllocatePass("PropagateMappedMips");
        {
            pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), projectionDataBufferSRV);
            pass->SetProperty(NAME_ID("_OutPageTable"), pageTableBufferUAV);

            UInt3 groupSize;
            mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("PropagateMappedMips"), groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR,
                           NAME_ID("PropagateMappedMips"),
                           math::DivideAndRoundUp(VirtualShadowMap::Level0DimPagesXY * VirtualShadowMap::Level0DimPagesXY, groupSize.x),
                           mVirtualShadowMapCount,
                           1);
        }
    }

    // Initialize the physical page pool
    {
        if (true)
        {
            REDBufferRef initialzePagesIndirectArgBuffer =
                mRED->AllocateBuffer("Shadow.Virtual.InitializePagesIndirectArgs", NGIBufferDesc{3 * sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::IndirectBuffer});
            REDBufferView* initialzePagesIndirectArgBufferUAV =
                mRED->AllocateBufferView(initialzePagesIndirectArgBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, 3 * sizeof(UInt32), GraphicsFormat::Unknown, sizeof(UInt32)});

            auto [physicalPagesToInitializeBuffer, physicalPagesToInitializeBufferUAV, physicalPagesToInitializeBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PhysicalPagesToInitialize", mPhysicalPagesCount, sizeof(UInt32));

            // ClearIndirectDispatchArgs1D
            {
                auto* pass = mRED->AllocatePass("ClearIndirectDispatchArgs1D");
                {
                    pass->SetProperty(NAME_ID("_OutIndirectArgsBuffer"), initialzePagesIndirectArgBufferUAV);
                    pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("ClearIndirectDispatchArgs1D"), 1, 1, 1);
                }
            }

            // SelectPagesToInitialize
            {
                auto* pass = mRED->AllocatePass("SelectPagesToInitialize");
                {
                    pass->SetProperty(NAME_ID("_PhysicalPageMetaData"), physicalPageMetaDataBufferSRV);
                    pass->SetProperty(NAME_ID("_OutIndirectArgsBuffer"), initialzePagesIndirectArgBufferUAV);
                    pass->SetProperty(NAME_ID("_OutPhysicalPagesToInitialize"), physicalPagesToInitializeBufferUAV);

                    UInt3 groupSize;
                    mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("SelectPagesToInitialize"), groupSize.x, groupSize.y, groupSize.z);
                    pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("SelectPagesToInitialize"), math::DivideAndRoundUp(mPhysicalPagesCount, groupSize.x), 1, 1);
                }
            }

            // InitializePhysicalPagesIndirect
            {
                auto* pass = mRED->AllocatePass("InitializePhysicalPagesIndirect");
                {
                    pass->SetProperty(NAME_ID("_PhysicalPagesToInitialize"), physicalPagesToInitializeBufferSRV);
                    pass->SetProperty(NAME_ID("_OutPhysicalPagePool"), physicalPagePoolUAV);
                    pass->DispatchIndirect(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR, NAME_ID("InitializePhysicalPagesIndirect"), initialzePagesIndirectArgBuffer, 0);
                }
            }
        }
        else
        {
            auto* pass = mRED->AllocatePass("InitializePhysicalPages");
            {
                pass->SetProperty(NAME_ID("_PhysicalPageMetaData"), physicalPageMetaDataBufferSRV);
                pass->SetProperty(NAME_ID("_OutPhysicalPagePool"), physicalPagePoolUAV);

                UInt3 groupSize;
                mShadowSettings->VirtualShadowMapPageManagementComputeShaderR->GetThreadGroupSize(NAME_ID("InitializePhysicalPages"), groupSize.x, groupSize.y, groupSize.z);
                pass->Dispatch(mShadowSettings->VirtualShadowMapPageManagementComputeShaderR,
                               NAME_ID("InitializePhysicalPages"),
                               math::DivideAndRoundUp(physicalPagePoolSize.x, groupSize.x),
                               math::DivideAndRoundUp(physicalPagePoolSize.y, groupSize.y),
                               1);
            }
        }
    }

    mRED->EndRegion();

    mShadowProperties.projectionDataBufferSRV = projectionDataBufferSRV;
    mShadowProperties.directionalLightIdsBufferSRV = directionalLightIdsBufferSRV;
    mShadowProperties.pageTableBufferSRV = pageTableBufferSRV;
    mShadowProperties.pageFlagsBufferSRV = pageFlagsBufferSRV;
    mShadowProperties.pageRectBoundsBufferSRV = pageRectBoundsBufferSRV;
    mShadowProperties.physicalPageMetaDataBufferSRV = physicalPageMetaDataBufferSRV;
    mShadowProperties.physicalPagePoolUAV = physicalPagePoolUAV;

    // Cache FrameData
    VirtualShadowMapArrayFrameData frameData;
    frameData.virtualShadowMapCount = mVirtualShadowMapCount;
    frameData.pageFlagsBuffer = pageFlagsBuffer;
    frameData.dynamicCasterPageFlagsBuffer = dynamicCasterPageFlagsBuffer;
    frameData.pageTableBuffer = pageTableBuffer;
    frameData.pageRectBoundsBuffer = pageRectBoundsBuffer;
    frameData.physicalPageMetaDataBuffer = physicalPageMetaDataBuffer;
    frameData.projectionData = projectionData;
    mCache.CacheFrameData(frameData);

    mPhysicalPagePool->RegisterRenderPipeline(mRenderPipeline, physicalPageMetaDataBuffer);
}

struct FoliagePayloadData
{
    Float4 boundingSphere;
    UInt32 lodDataStart;
    UInt32 lodDataCount;
    UInt32 objectIndexStart;
    UInt32 objectIndexCount;
    UInt32 indirectArgIndexOffset;
    float culledHeight;
    float _pad[2];
};

struct VisibleInstanceCommand
{
    UInt32 packedPageInfo;
    UInt32 instanceId;
    UInt32 indirectArgIndex;
};

void VirtualShadowMapArray::RenderShadowDepth()
{
    QUICK_SCOPED_CPU_TIMING("RenderShadowDepthVSM");
    mRED->BeginRegion("Shadow.Virtual.RenderShadowDepth");

    auto* renderPipelineSys = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>();
    auto* gpuScene = mWorldRenderPipeline->GetGPUScene();
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* transientResourceManager = rendererSys->GetTransientResourceManager();

    // ViewData
    FrameStdVector<VirtualShadowMapViewData> viewDatas(mMemoryPool);
    viewDatas.resize(mVirtualShadowMapCount);

    for (auto& [lightEntity, virtualShadowMapClipmap] : mLightsRenderVSM)
    {
        SInt32 baseId = virtualShadowMapClipmap->GetVirtualShadowMap()->GetId();

        for (UInt32 levelIndex = 0; levelIndex < virtualShadowMapClipmap->GetLevelCount(); levelIndex++)
        {
            viewDatas[baseId + levelIndex] = virtualShadowMapClipmap->GetViewData(levelIndex);
        }
    }

    NGIBufferView* viewDataBufferSRV = CreateStructuredBuffer(viewDatas);

    bool enableGPUDriven = mRenderPipeline->GetSetting()->mFoliageGpuDrivenSettings.enable && mRenderPipeline->GetSetting()->EnableFoliageDrawing;
    // todo: handle gpu driven switch off case
    NGIBuffer* foliageObjectSceneDataBuffer = enableGPUDriven ? FoliageGpuDriven::mFoliageInstanceBuffer.get() : rendererSys->GetRenderPrimitives()->mDefaultBuffer.get();
    NGIBuffer* foliageEntityDataBuffer = enableGPUDriven ? FoliageGpuDriven::mFoliageEntityBuffer.get() : rendererSys->GetRenderPrimitives()->mDefaultBuffer.get();
    NGIBufferView* foliageObjectSceneDataBufferSRV = nullptr;
    NGIBufferView* foliageEntityDataBufferSRV =
        transientResourceManager->AllocateBufferView(NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, foliageEntityDataBuffer->GetSize(), GraphicsFormat::Unknown, sizeof(FoliageEntityData)}, foliageEntityDataBuffer);
    if (foliageObjectSceneDataBuffer)
    {
        foliageObjectSceneDataBufferSRV = transientResourceManager->AllocateBufferView(
            NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, foliageObjectSceneDataBuffer->GetSize(), GraphicsFormat::Unknown, sizeof(FoliageInstanceCompactData)}, foliageObjectSceneDataBuffer);
    }

    //bool runInstanceCulling2 = mRenderPipeline->GetSetting()->InstanceCulling2;

    for (auto [lightEntity, virtualShadowMapClipmap] : mLightsRenderVSM)
    {
        const UInt32 firstPrimaryView = virtualShadowMapClipmap->GetShadowMapId();
        const UInt32 virtualShadowMapLevelCount = virtualShadowMapClipmap->GetLevelCount();
        const UInt32 primaryViewCount = virtualShadowMapLevelCount;

        auto objectIndexBufferTuple = CreateUAVBuffer("ObjectIndexBuffer", 0, sizeof(UInt32));
        auto objectIndexBuffer = std::get<0>(objectIndexBufferTuple);
        auto* objectIndexBufferUAV = std::get<1>(objectIndexBufferTuple);
        auto* objectIndexBufferSRV = std::get<2>(objectIndexBufferTuple);

        auto pageInfoBufferTuple = CreateUAVBuffer("PageInfoBuffer", 0, sizeof(UInt32));
        auto pageInfoBuffer = std::get<0>(pageInfoBufferTuple);
        auto* pageInfoBufferUAV = std::get<1>(pageInfoBufferTuple);
        auto* pageInfoBufferSRV = std::get<2>(pageInfoBufferTuple);

        auto* renderCamera = virtualShadowMapClipmap->GetRenderCamera(virtualShadowMapLevelCount - 1);

        // HACK!!!
        renderCamera->mCameraMask = mRenderPipeline->GetRenderCamera()->mCameraMask;

        auto* cullingResult = mRED->Cull(REDCullingDesc{mRenderWorld, renderCamera});
        auto* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{NAME_ID("VSMDepth"), 0, UINT16_MAX, RenderEffectTag::CastShadow, RenderNodeType{0}, nullptr, mRenderPipeline->GetRenderCamera()});

        // InstanceCulling
        {
            auto visibleObjectCommandsBufferTuple = CreateUAVBuffer("VisibleObjectCommandsBuffer", 0, sizeof(VisibleInstanceCommand));
            auto visibleObjectCommandsBuffer = std::get<0>(visibleObjectCommandsBufferTuple);
            auto* visibleObjectCommandsBufferUAV = std::get<1>(visibleObjectCommandsBufferTuple);
            auto* visibleObjectCommandsBufferSRV = std::get<2>(visibleObjectCommandsBufferTuple);

            auto [visibleObjectCommandCountBuffer, visibleObjectCommandCountBufferUAV, visibleObjectCommandCountBufferSRV] = CreateUAVBuffer("VisibleObjectCommandCountBuffer", 1, sizeof(UInt32));
            ClearRWStructuredBuffer(visibleObjectCommandCountBufferUAV, 0);

            auto drawIndirectArgsBuffer =
                mRED->AllocateBuffer("DrawIndirectBuffer", NGIBufferDesc{0, NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc});
            auto* drawIndirectArgsBufferSRV = mRED->AllocateBufferView(drawIndirectArgsBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, 0, GraphicsFormat::Unknown, sizeof(UInt32)});
            auto* drawIndirectArgsBufferUAV = mRED->AllocateBufferView(drawIndirectArgsBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 0, GraphicsFormat::Unknown, sizeof(UInt32)});
            drawUnitList->SetIndirectBuffer(drawIndirectArgsBuffer);

            auto [objectIndexOffsetBuffer, objectIndexOffsetBufferUAV, objectIndexOffsetBufferSRV] = CreateUAVBuffer("ObjectIndexOffsetBuffer", 0, sizeof(UInt32), NGIBufferUsage::VertexBuffer);
            drawUnitList->SetObjectIndexOffsetsBuffer(objectIndexOffsetBuffer);

            auto [tempObjectIndexOffsetBuffer, tempObjectIndexOffsetBufferUAV, tempObjectIndexOffsetBufferSRV] = CreateUAVBuffer("TempObjectIndexOffsetBuffer", 0, sizeof(UInt32));

            auto [offsetBufferCountBuffer, offsetBufferCountBufferUAV, offsetBufferCountBufferSRV] = CreateUAVBuffer("OffsetBufferCountBuffer", 1, sizeof(UInt32));
            ClearRWStructuredBuffer(offsetBufferCountBufferUAV, 0);

            auto outputPassIndirectArgs = mRED->AllocateBuffer("OutputPassIndirectArgs", NGIBufferDesc{3u * sizeof(UInt32), NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer});
            auto* outputPassIndirectArgsUAV = mRED->AllocateBufferView(outputPassIndirectArgs, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 3u * sizeof(UInt32), GraphicsFormat::Unknown, sizeof(UInt32)});

            auto [objectPayloadIndexAndObjectOffsetAndMutex, objectPayloadIndexAndObjectOffsetAndMutexUAV, objectPayloadIndexAndObjectOffsetAndMutexSRV] = CreateUAVBuffer("_ObjectPayloadIndexAndObjectOffsetAndMutex", 3, sizeof(UInt32));
            auto* rangePayloadSetPass = ClearRWStructuredBuffer(objectPayloadIndexAndObjectOffsetAndMutexUAV, 0, 1, 0);
            ClearRWStructuredBuffer(objectPayloadIndexAndObjectOffsetAndMutexUAV, 1, 3, 0);

            std::shared_ptr<UInt32> visibleObjectCommandBufferMaxNum;
            visibleObjectCommandBufferMaxNum.reset(new UInt32(0));

            // CullPerPageDrawUnits (Normal)
            {
                auto objectPayloadDataBufferTuple = CreateUAVBuffer("ObjectPayloadDataBuffer", 0, sizeof(ObjectPayloadData), NGIBufferUsage::CopyDst);
                auto* objectPayloadDataBuffer = std::get<0>(objectPayloadDataBufferTuple);
                auto* objectPayloadDataBufferSRV = std::get<2>(objectPayloadDataBufferTuple);

                UInt32 engineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
                const UInt4 uintParams0(firstPrimaryView, primaryViewCount, engineFrameCount, 0);

                auto* pass = mRED->AllocatePass("CullPerPageDrawUnits(Normal)");
                {
                    pass->SetProperty(NAME_ID("_UIntParams0"), uintParams0);
                    pass->SetProperty(NAME_ID("_PageFlags"), mShadowProperties.pageFlagsBufferSRV);
                    pass->SetProperty(NAME_ID("_PageRectBounds"), mShadowProperties.pageRectBoundsBufferSRV);
                    pass->SetProperty(NAME_ID("_GPUScenePrimitiveCullingDatas"), gpuScene->GetPrimitiveCullingDataBufferSRV());
                    pass->SetProperty(NAME_ID("_GPUSceneObjectCullingDatas"), gpuScene->GetObjectCullingDataBufferSRV());
                    pass->SetProperty(NAME_ID("_ObjectPayloadDatas"), objectPayloadDataBufferSRV);
                    pass->SetProperty(NAME_ID("_ObjectPayloadIndexAndObjectOffsetAndMutex"), objectPayloadIndexAndObjectOffsetAndMutexUAV);
                    pass->SetProperty(NAME_ID("_VirtualShadowMapViewDatas"), viewDataBufferSRV);
                    pass->SetProperty(NAME_ID("_OutVisibleObjectCommands"), visibleObjectCommandsBufferUAV);
                    pass->SetProperty(NAME_ID("_OutVisibleObjectCommandCount"), visibleObjectCommandCountBufferUAV);
                    pass->SetProperty(NAME_ID("_OutDrawIndirectArgs"), drawIndirectArgsBufferUAV);

                    REDComputeExecutionPayload* computeExecutionPayload;
                    //if (runInstanceCulling2)
                    {
                        computeExecutionPayload = pass->Dispatch(mShadowSettings->VirtualShadowMapInstanceCulling2ComputeShaderR, NAME_ID("CullPerPageDrawUnits"), 64, 1, 1);
                    }
                    //else
                    //{
                    //    computeExecutionPayload = pass->Dispatch(mShadowSettings->VirtualShadowMapInstanceCullingComputeShaderR, NAME_ID("CullPerPageDrawUnits"), 0, 0, 0);
                    //}

                    pass->OnCulling([=,
                                     objectIndexOffsetBuffer = objectIndexOffsetBuffer,
                                     objectIndexOffsetBufferUAV = objectIndexOffsetBufferUAV,
                                     tempObjectIndexOffsetBuffer = tempObjectIndexOffsetBuffer,
                                     tempObjectIndexOffsetBufferUAV = tempObjectIndexOffsetBufferUAV,
                                     visibleObjectCommandBufferMaxNum = visibleObjectCommandBufferMaxNum
                                     ](REDPass* pass) {
                        auto* drawUnits = drawUnitList->GetDrawUnits();

                        auto& modifiableDrawUnits = drawUnitList->GetModifiableDrawUnits();

                        // ObjectPayloadData (for normal DrawUnits)
                        UInt32 normalObjectCount;
                        //if (!runInstanceCulling2)
                        //{
                        //    const auto& objectPayloadDatas = drawUnitList->GetObjectPayloadDatas();
                        //    normalObjectCount = static_cast<UInt32>(objectPayloadDatas.size());

                        //    UInt32 objectPayloadDataBufferDataSize = static_cast<UInt32>(normalObjectCount * sizeof(ObjectPayloadData));
                        //    UInt32 objectPayloadDataBufferSize = std::max(objectPayloadDataBufferDataSize, 1u);
                        //    objectPayloadDataBuffer->ModifySize(objectPayloadDataBufferSize);
                        //    objectPayloadDataBufferSRV->ModifyRange(0, objectPayloadDataBufferSize);

                        //    mRED->QueueBufferUpload(objectPayloadDataBuffer, 0, objectPayloadDatas.data(), objectPayloadDataBufferDataSize);
                        //}
                        //else
                        {
                            const auto& objectPayloadDatas = drawUnitList->GetObjectPayloadDatas2();
                            normalObjectCount = static_cast<UInt32>(objectPayloadDatas.size());

                            UInt32 objectPayloadDataBufferDataSize = static_cast<UInt32>(normalObjectCount * sizeof(ObjectPayloadData2));
                            UInt32 objectPayloadDataBufferSize = std::max(objectPayloadDataBufferDataSize, 1u);
                            objectPayloadDataBuffer->ModifySize(objectPayloadDataBufferSize);
                            objectPayloadDataBufferSRV->ModifyRange(0, objectPayloadDataBufferSize);

                            mRED->QueueBufferUpload(objectPayloadDataBuffer, 0, objectPayloadDatas.data(), objectPayloadDataBufferDataSize);
                        }

                        // VisibleObjectsBuffer (for all DrawUnits)
                        std::unordered_set<const void*> renderNodeMap;

                        UInt32 maxObjectsPerPass = 0;
                        for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnits->GetSize(); drawUnitIndex++)
                        {
                            const REDDrawUnit& drawUnit = drawUnits->At(drawUnitIndex);

                            if (drawUnit.mType == RenderNodeType::Foliage)
                            {
                                auto* foliageInfo = reinterpret_cast<const FoliageInfo*>(drawUnit.mCustumData);
                                auto* renderNode = foliageInfo->RenderNode;

                                if (renderNodeMap.find(renderNode) == renderNodeMap.end())
                                {
                                    renderNodeMap.insert(renderNode);

                                    maxObjectsPerPass += drawUnit.GetInstanceCount() * 2;
                                }
                            }
                            else
                            {
                                maxObjectsPerPass += drawUnit.GetInstanceCount() * 16;
                            }
                        }

                        *visibleObjectCommandBufferMaxNum = maxObjectsPerPass;

                        UInt32 visibleObjectsBufferDataSize = static_cast<UInt32>(maxObjectsPerPass * sizeof(VisibleInstanceCommand));
                        UInt32 visibleObjectsBufferSize = std::max(visibleObjectsBufferDataSize, 1u);
                        visibleObjectCommandsBuffer->ModifySize(visibleObjectsBufferSize);
                        visibleObjectCommandsBufferUAV->ModifyRange(0, visibleObjectsBufferSize);
                        visibleObjectCommandsBufferSRV->ModifyRange(0, visibleObjectsBufferSize);

                        // IndirectBuffer (for all DrawUnits)
                        FrameStdVector<CompactDrawCMD> indirectDrawCommands(mMemoryPool);
                        indirectDrawCommands.resize(drawUnits->GetSize());

                        for (UInt32 index = 0; index < drawUnits->GetSize(); index++)
                        {
                            const REDDrawUnit& drawUnit = (*drawUnits)[index];
                            const RenderGeometry* geometry = drawUnit.mGeometry;

                            indirectDrawCommands[index] = CompactDrawCMD{
                                geometry->GetIndexCount(),
                                0,
                                geometry->GetIndexStart(),
                                static_cast<SInt32>(geometry->GetVertexStart()),
                                0,
                            };

                            drawUnit.mIndirectBufferOffset = index * sizeof(CompactDrawCMD);
                            drawUnit.mIndirectCount = 1u;
                            drawUnit.mIndirectStride = sizeof(CompactDrawCMD);
                        }

                        UInt32 indirectBufferDataSize = static_cast<UInt32>(drawUnits->GetSize() * sizeof(CompactDrawCMD));
                        UInt32 indirectBufferSize = std::max(indirectBufferDataSize, 1u);
                        drawIndirectArgsBuffer->ModifySize(indirectBufferSize);
                        drawIndirectArgsBufferSRV->ModifyRange(0, indirectBufferSize);
                        drawIndirectArgsBufferUAV->ModifyRange(0, indirectBufferSize);

                        mRED->QueueBufferUpload(drawIndirectArgsBuffer, 0, indirectDrawCommands.data(), indirectBufferDataSize);

                        // ObjectIndexOffsetBuffer
                        UInt32 objectIndexOffsetBufferSize = std::max(static_cast<UInt32>(drawUnits->GetSize() * sizeof(UInt32)), 1u);
                        objectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                        objectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                        // TempObjectIndexOffsetBuffer
                        tempObjectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                        tempObjectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                        // ObjectIndexBuffer
                        UInt32 objectIndexBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
                        objectIndexBuffer->ModifySize(objectIndexBufferSize);
                        objectIndexBufferUAV->ModifyRange(0, objectIndexBufferSize);
                        objectIndexBufferSRV->ModifyRange(0, objectIndexBufferSize);

                        // PageInfoBuffer
                        UInt32 pageInfoBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
                        pageInfoBuffer->ModifySize(pageInfoBufferSize);
                        pageInfoBufferUAV->ModifyRange(0, pageInfoBufferSize);
                        pageInfoBufferSRV->ModifyRange(0, pageInfoBufferSize);

                        // Dispatch
                        pass->SetProperty("_UIntParams1", UInt4(normalObjectCount, maxObjectsPerPass, 0, drawUnitList->GetRangePayloadOffset()));
                        pass->SetProperty("_UIntParams2", UInt4(drawUnitList->GetLargePayloadOffset() - drawUnitList->GetRangePayloadOffset(), normalObjectCount - drawUnitList->GetLargePayloadOffset(), 0, 0));
                        
                        rangePayloadSetPass->SetProperty(NAME_ID("_ClearParams"), UInt4(0, 1, drawUnitList->GetLargePayloadOffset(), 0));

                        //if (!runInstanceCulling2)
                        //{
                        //    UInt3 groupSize;
                        //    mShadowSettings->VirtualShadowMapInstanceCullingComputeShaderR->GetThreadGroupSize("CullPerPageDrawUnits", groupSize.x, groupSize.y, groupSize.z);
                        //    computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(normalObjectCount, groupSize.x), 1, 1);
                        //}
                    });
                }
            }

            // CullPerPageDrawUnits (Foliage)
            if (/*!mShadowSettings->OptimizeFoliageShadowPerformance &&*/ foliageObjectSceneDataBuffer)
            {
                auto lodDataBufferTuple = CreateUAVBuffer("LODDataBuffer", 0, sizeof(float), NGIBufferUsage::CopyDst);
                auto lodDataBuffer = std::get<0>(lodDataBufferTuple);
                auto* lodDataBufferSRV = std::get<2>(lodDataBufferTuple);

                auto indirectArgIndexBufferTuple = CreateUAVBuffer("IndirectArgIndexBuffer", 0, sizeof(SInt32), NGIBufferUsage::CopyDst);
                auto indirectArgIndexBuffer = std::get<0>(indirectArgIndexBufferTuple);
                auto* indirectArgIndexBufferSRV = std::get<2>(indirectArgIndexBufferTuple);

                auto foliagePayloadDataBufferTuple = CreateUAVBuffer("FoliagePayloadDataBuffer", 0, sizeof(FoliagePayloadData), NGIBufferUsage::CopyDst);
                auto foliagePayloadDataBuffer = std::get<0>(foliagePayloadDataBufferTuple);
                auto* foliagePayloadDataBufferSRV = std::get<2>(foliagePayloadDataBufferTuple);

                UInt32 engineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
                const UInt4 uintParams0(firstPrimaryView, primaryViewCount, engineFrameCount, 0);

                const FFSRenderPipelineSetting* ffsRenderPipelineSetting = static_cast<const FFSRenderPipelineSetting*>(renderPipelineSys->GetRenderPipelineSetting());

                auto* pass = mRED->AllocatePass("CullPerPageDrawUnits(Foliage)");
                {
                    pass->SetProperty(NAME_ID("_UIntParams0"), uintParams0);
                    pass->SetProperty(NAME_ID("_LODScaleFactor"), ffsRenderPipelineSetting->mFoliageGpuDrivenSettings.LODScaleFactor);

                    pass->SetProperty(NAME_ID("_PageFlags"), mShadowProperties.pageFlagsBufferSRV);
                    pass->SetProperty(NAME_ID("_PageRectBounds"), mShadowProperties.pageRectBoundsBufferSRV);
                    pass->SetProperty(NAME_ID("_VirtualShadowMapViewDatas"), viewDataBufferSRV);

                    pass->SetProperty(NAME_ID("_FoliageObjectSceneDatas"), foliageObjectSceneDataBufferSRV);
                    pass->SetProperty(NAME_ID("_FoliageEntityBuffer"), foliageEntityDataBufferSRV);
                    pass->SetProperty(NAME_ID("_LODDatas"), lodDataBufferSRV);
                    pass->SetProperty(NAME_ID("_IndirectArgIndices"), indirectArgIndexBufferSRV);
                    pass->SetProperty(NAME_ID("_FoliagePayloadDatas"), foliagePayloadDataBufferSRV);

                    pass->SetProperty(NAME_ID("_OutVisibleObjectCommands"), visibleObjectCommandsBufferUAV);
                    pass->SetProperty(NAME_ID("_OutVisibleObjectCommandCount"), visibleObjectCommandCountBufferUAV);
                    pass->SetProperty(NAME_ID("_OutDrawIndirectArgs"), drawIndirectArgsBufferUAV);
                    auto* computeExecutionPayload = pass->Dispatch(mShadowSettings->VirtualShadowMapFoliageInstanceCullingComputeShaderR, NAME_ID("CullPerPageDrawUnits"), 0, 0, 0);

                    pass->OnCulling([=](REDPass* pass) {
                        auto* drawUnits = drawUnitList->GetDrawUnits();

                        // Fill upload data
                        FrameStdVector<float> lodData(mMemoryPool);
                        FrameStdVector<SInt32> indirectArgIndexData(mMemoryPool);
                        FrameStdVector<FoliagePayloadData> foliagePayloadData(mMemoryPool);

                        UInt32 foliageObjectCount = 0;

                        // RenderNode* -> IndirectArgOffset
                        CEFrameHashMap<const void*, UInt32> renderNodeMap;
                        auto* foliageSystem = mRenderWorld->GetRenderSystem<FoliageSystemR>();

                        for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnits->GetSize(); drawUnitIndex++)
                        {
                            const auto& drawUnit = drawUnits->At(drawUnitIndex);

                            if (drawUnit.mType == RenderNodeType::Foliage)
                            {
                                if (auto val = drawUnit.mMaterial->GetFloat("MATERIAL_TYPE"); val)
                                {
                                    resource::MaterialType materialType = static_cast<resource::MaterialType>(val.value());
                                    if (mShadowSettings->OptimizeFoliageShadowPerformance && (materialType == resource::MaterialType::MaterialType_TwosidedFoliage || materialType == resource::MaterialType::MaterialType_ImposterFoliage))
                                        continue;
                                }
                                auto* foliageInfo = reinterpret_cast<const FoliageInfo*>(drawUnit.mCustumData);
                                auto* renderNode = foliageInfo->RenderNode;
                                auto& instanceInfo = foliageSystem->mClusterOffsetMap[foliageInfo->mFoliageClusterKey];
                                if (mRenderWorld->IsEntityAliveInner(instanceInfo.entityID) == false)
                                    return;
                                if (renderNodeMap.find(renderNode) == renderNodeMap.end())
                                {
                                    foliageObjectCount += drawUnit.GetInstanceCount();

                                    auto lodCount = foliageInfo->LODCount;
                                    auto lodBias = foliageInfo->LODBias;
                                    auto lodSetting = foliageInfo->LODSetting;
                                    auto boundingSphere = foliageInfo->BoundingSphere;

                                    UInt32 lodDataStart = static_cast<UInt32>(lodData.size());
                                    UInt32 objectIndexStart = instanceInfo.startInstance;   // static_cast<UInt32>(drawUnit.mObjectIndexStart);
                                    UInt32 indirectArgIndexOffset = static_cast<UInt32>(indirectArgIndexData.size());
                                    float cullenHeight = (lodSetting) ? lodSetting->mCulledHeight : 0;

                                    // add FoliagePayloadData
                                    foliagePayloadData.push_back(FoliagePayloadData{
                                        boundingSphere,
                                        lodDataStart,
                                        lodCount,
                                        objectIndexStart,
                                        drawUnit.GetInstanceCount(),
                                        indirectArgIndexOffset,
                                        cullenHeight,
                                    });

                                    // add LodData
                                    for (UInt32 lodBase = 0; lodBase < lodCount; lodBase++)
                                    {
                                        UInt32 lodIndex = std::min(lodBase + lodBias, lodCount - 1u);

                                        float screenReleativeTransitionHeight = (lodSetting && lodIndex < lodCount) ? lodSetting->mLevelSettings[lodIndex].mScreenReleativeTransitionHeight : 0;

                                        lodData.push_back(screenReleativeTransitionHeight);
                                    }

                                    // add IndirectArgIndexData
                                    indirectArgIndexData.insert(indirectArgIndexData.end(), lodCount, -1);

                                    indirectArgIndexData[indirectArgIndexOffset + foliageInfo->LODIndex] = drawUnitIndex;

                                    // store IndirectArgOffsetStart
                                    renderNodeMap[renderNode] = indirectArgIndexOffset;
                                }
                                else
                                {
                                    UInt32 indirectArgIndexOffset = renderNodeMap[renderNode];

                                    indirectArgIndexData[indirectArgIndexOffset + foliageInfo->LODIndex] = drawUnitIndex;
                                }
                            }
                        }

                        // LodDataBuffer
                        UInt32 lodDataBufferDataSize = static_cast<UInt32>(lodData.size() * sizeof(float));
                        UInt32 lodDataBufferSize = std::max(lodDataBufferDataSize, 1u);
                        lodDataBuffer->ModifySize(lodDataBufferSize);
                        lodDataBufferSRV->ModifyRange(0, lodDataBufferSize);

                        mRED->QueueBufferUpload(lodDataBuffer, 0, lodData.data(), lodDataBufferDataSize);

                        // IndirectArgIndexData
                        UInt32 indirectArgIndexBufferDataSize = static_cast<UInt32>(indirectArgIndexData.size() * sizeof(SInt32));
                        UInt32 indirectArgIndexBufferSize = std::max(indirectArgIndexBufferDataSize, 1u);
                        indirectArgIndexBuffer->ModifySize(indirectArgIndexBufferSize);
                        indirectArgIndexBufferSRV->ModifyRange(0, indirectArgIndexBufferSize);

                        mRED->QueueBufferUpload(indirectArgIndexBuffer, 0, indirectArgIndexData.data(), indirectArgIndexBufferDataSize);

                        // FoliagePayloadData
                        UInt32 foliagePayloadDataBufferDataSize = static_cast<UInt32>(foliagePayloadData.size() * sizeof(FoliagePayloadData));
                        UInt32 foliagePayloadDataBufferSize = std::max(foliagePayloadDataBufferDataSize, 1u);
                        foliagePayloadDataBuffer->ModifySize(foliagePayloadDataBufferSize);
                        foliagePayloadDataBufferSRV->ModifyRange(0, foliagePayloadDataBufferSize);

                        mRED->QueueBufferUpload(foliagePayloadDataBuffer, 0, foliagePayloadData.data(), foliagePayloadDataBufferDataSize);

                        // Dispatch
                        pass->SetProperty("_UIntParams1", UInt4(foliageObjectCount, *visibleObjectCommandBufferMaxNum, static_cast<UInt32>(foliagePayloadData.size()), 0));

                        UInt3 groupSize;
                        mShadowSettings->VirtualShadowMapFoliageInstanceCullingComputeShaderR->GetThreadGroupSize("CullPerPageDrawUnits", groupSize.x, groupSize.y, groupSize.z);
                        computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(foliageObjectCount, groupSize.x), 1, 1);
                    });
                }

            }

            // AllocateCommandInstanceOutputSpace
            {
                auto* pass = mRED->AllocatePass("AllocateCommandInstanceOutputSpace");
                {
                    pass->SetProperty(NAME_ID("_DrawIndirectArgs"), drawIndirectArgsBufferSRV);
                    pass->SetProperty(NAME_ID("_OutOffsetBufferCount"), offsetBufferCountBufferUAV);
                    pass->SetProperty(NAME_ID("_OutObjectIndexOffsetBuffer"), objectIndexOffsetBufferUAV);
                    pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferUAV);
                    auto* computeExecutionPayload = pass->Dispatch(mShadowSettings->VirtualShadowMapInstanceCullingComputeShaderR, NAME_ID("AllocateCommandInstanceOutputSpace"), 0, 0, 0);

                    pass->OnCulling([=](REDPass* pass) {
                        auto* drawUnits = drawUnitList->GetDrawUnits();

                        pass->SetProperty("_UIntParams1", UInt4(0, 0, drawUnits->GetSize(), 0));

                        UInt3 groupSize;
                        mShadowSettings->VirtualShadowMapInstanceCullingComputeShaderR->GetThreadGroupSize("AllocateCommandInstanceOutputSpace", groupSize.x, groupSize.y, groupSize.z);
                        computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(drawUnits->GetSize(), groupSize.x), 1, 1);
                    });
                }
                InstanceCulling::AddReadBackDrawCulling(pass, drawUnitList, mRED->GetFrameId(), static_cast<const FFSRenderPipelineSetting*>(mRenderPipeline->GetSetting())->FeedBackDebug);

            }

            // InitIndirectArgs1D
            {
                UInt3 groupSize;
                mShadowSettings->VirtualShadowMapInstanceCullingComputeShaderR->GetThreadGroupSize("OutputCommandInstanceLists", groupSize.x, groupSize.y, groupSize.z);

                auto* pass = mRED->AllocatePass("InitIndirectArgs1D");
                {
                    pass->SetProperty(NAME_ID("_UIntParams0"), UInt4(1, groupSize.x, 0, 0));
                    pass->SetProperty(NAME_ID("_InputCountBuffer"), visibleObjectCommandCountBufferSRV);
                    pass->SetProperty(NAME_ID("_OutIndirectDispatchArgs"), outputPassIndirectArgsUAV);
                    pass->Dispatch(mShadowSettings->InitIndirectArgs1DR, "InitIndirectArgs1D", 1, 1, 1);
                }
            }

            // OutputCommandInstanceLists
            {
                auto* pass = mRED->AllocatePass("OutputCommandInstanceLists");
                {
                    pass->SetProperty(NAME_ID("_VisibleObjectCommands"), visibleObjectCommandsBufferSRV);
                    pass->SetProperty(NAME_ID("_VisibleObjectCommandCount"), visibleObjectCommandCountBufferSRV);
                    pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferUAV);
                    pass->SetProperty(NAME_ID("_OutObjectIndexBuffer"), objectIndexBufferUAV);
                    pass->SetProperty(NAME_ID("_OutPageInfoBuffer"), pageInfoBufferUAV);
                    pass->DispatchIndirect(mShadowSettings->VirtualShadowMapInstanceCullingComputeShaderR, NAME_ID("OutputCommandInstanceLists"), outputPassIndirectArgs, 0);
                }
            }
        }

        // RenderShadowDepth
        {
            UInt4 vsmUIntParams0(virtualShadowMapClipmap->GetVirtualShadowMap()->GetId(), mPhysicalPagesCount, mPhysicalPageRowMask, mPhysicalPageRowShift);

            mRED->BeginRenderPass("RenderVirtualShadowMapDepth", 16 * 1024, 16 * 1024, 1, 1);
            auto* pass = mRED->AllocateSubRenderPass("RenderVirtualShadowMapDepth", 0, nullptr, 0, nullptr, REDPassFlagBit(0));
            {
                pass->SetProperty(NAME_ID("ce_VSMUIntParams0"), vsmUIntParams0);
                pass->SetProperty(NAME_ID("_VirtualShadowMapViewDatas"), viewDataBufferSRV);
                pass->SetProperty(NAME_ID("_PageTable"), mShadowProperties.pageTableBufferSRV);
                pass->SetProperty(NAME_ID("_PageRectBounds"), mShadowProperties.pageRectBoundsBufferSRV);
                pass->SetProperty(NAME_ID("_PhysicalPagePool"), mShadowProperties.physicalPagePoolUAV);
                pass->SetProperty(NAME_ID("_ObjectIndexBuffer"), objectIndexBufferSRV);
                pass->SetProperty(NAME_ID("_PageInfoBuffer"), pageInfoBufferSRV);
                pass->SetProperty(NAME_ID("_FoliageObjectSceneDatas"), foliageObjectSceneDataBufferSRV);
                pass->SetProperty(NAME_ID("_FoliageEntityBuffer"), foliageEntityDataBufferSRV);
                pass->SetProperty(BuiltInProperty::CE_INSTANCING, true);

                pass->RenderDrawUnits(REDRenderDrawUnitsDesc{drawUnitList,
                                                             NGIRasterizationStateDesc{
                                                                 FillMode::Solid,
                                                                 CullMode::Back,
                                                                 FaceOrder::CW,
                                                                 false,
                                                                 false,
                                                                 false,
                                                                 0,
                                                                 0,
                                                                 0,
                                                                 0,
                                                                 0,
                                                                 RasterizationMode::DefaultRaster,
                                                                 0,
                                                             }});
            }
            mRED->EndRenderPass();
        }

        if (static_cast<const FFSRenderPipelineSetting*>(mRenderPipeline->GetSetting())->FeedbackDirectShadowOptimization)
        {
            if (mRenderPipeline->GetRenderPipelineType() == ViewType::GameView || mRenderPipeline->GetRenderPipelineType() == ViewType::SceneView)
            {
                InstanceCulling::RequestForReadbackDraw(drawUnitList, mRED->GetFrameId());
            }
        }
    }

    mRED->EndRegion();
}

void VirtualShadowMapArray::RenderDebugTexture(REDTextureView* depthView, std::array<REDTextureView*, 4>& gBufferViews, NGIBufferView* lightDataBufferView, REDTextureView*& debugTextureView)
{
    mRED->BeginRegion("Shadow.Virtual.RenderDebugTexture");

    NGITextureDesc debugTextureDesc = mRenderPipeline->mTargetView->mTexture->mDesc;
    debugTextureDesc.Format = GraphicsFormat::R8G8B8A8_UNorm;
    debugTextureDesc.Usage |= NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource;
    REDTextureRef mDebugTexture = mRED->AllocateTexture("DebugTexture", debugTextureDesc);

    NGITextureViewDesc debugTextureViewDesc = mRenderPipeline->mTargetView->mDesc;
    debugTextureViewDesc.Format = GraphicsFormat::R8G8B8A8_UNorm;
    debugTextureViewDesc.Usage = NGITextureUsage::ShaderResource;
    debugTextureView = mRED->AllocateTextureView(mDebugTexture, debugTextureViewDesc);

    debugTextureViewDesc.Usage = NGITextureUsage::UnorderedAccess;
    REDTextureView* debugTextureViewUAV = mRED->AllocateTextureView(mDebugTexture, debugTextureViewDesc);

    const UInt2 screenSize = mRenderPipeline->GetRenderSize();
    const Float4 screenSizeAndInvSize{static_cast<float>(screenSize.x), static_cast<float>(screenSize.y), 1.0f / static_cast<float>(screenSize.x), 1.0f / static_cast<float>(screenSize.y)};
    const UInt4 uintParams0(0, mPhysicalPagesCount, mPhysicalPageRowMask, mPhysicalPageRowShift);
    const UInt4 uintParams1(0, mShadowSettings->SMRTRayCount, mShadowSettings->SMRTSamplesPerRay, 0);
    const Float4 floatParams0(mShadowSettings->ContactShadowLength, 0, 0, 0);

    auto [physicalPageWorldMetaDataBufferSRV, physicalPageWorldMetaDataBufferUAV] = mPhysicalPagePool->GetPhysicalPageWorldMetaDataBufferViews();

    mRED->SetProperty(NAME_ID("_ScreenSizeAndInvSize"), screenSizeAndInvSize);
    mRED->SetProperty(NAME_ID("_UIntParams0"), uintParams0);
    mRED->SetProperty(NAME_ID("_UIntParams1"), uintParams1);
    mRED->SetProperty(NAME_ID("_FloatParams0"), floatParams0);

    if (mShadowSettings->DebugMode && !mLightsRenderVSM.empty())
    {
        auto* pass = mRED->AllocatePass("VSM_OutputDebugTexture");
        {
            pass->SetProperty(NAME_ID("_DepthMap"), depthView);
            pass->SetProperty(NAME_ID("_VirtualShadowMapProjectionDatas"), mShadowProperties.projectionDataBufferSRV);
            pass->SetProperty(NAME_ID("_DirectionalLightIds"), mShadowProperties.directionalLightIdsBufferSRV);
            pass->SetProperty(NAME_ID("_PhysicalPageMetaData"), mShadowProperties.physicalPageMetaDataBufferSRV);
            pass->SetProperty(NAME_ID("_PhysicalPageWorldMetaData"), physicalPageWorldMetaDataBufferSRV);
            pass->SetProperty(NAME_ID("_PageTable"), mShadowProperties.pageTableBufferSRV);
            pass->SetProperty(NAME_ID("_PageFlags"), mShadowProperties.pageFlagsBufferSRV);
            pass->SetProperty(NAME_ID("_PageRectBounds"), mShadowProperties.pageRectBoundsBufferSRV);
            pass->SetProperty(NAME_ID("_PhysicalPagePool"), mShadowProperties.physicalPagePoolUAV);
            pass->SetProperty(NAME_ID("_NormalBuffer"), gBufferViews[1]);
            pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            pass->SetProperty(NAME_ID("_LightDatas"), lightDataBufferView);
            pass->SetProperty(NAME_ID("_OutputDebugTexture"), debugTextureViewUAV);
            pass->SetProperty(NAME_ID("ENABLE_FOLIAGEONLY_CONTACTSHADOW"), mShadowSettings->OptimizeFoliageShadowPerformance);

            UInt3 groupSize;
            mShadowSettings->OutputDebugTextureComputeShaderR->GetThreadGroupSize("OutputDebugTexture", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(mShadowSettings->OutputDebugTextureComputeShaderR, NAME_ID("OutputDebugTexture"), math::DivideAndRoundUp(screenSize.x, groupSize.x), math::DivideAndRoundUp(screenSize.y, groupSize.y), 1);
        }
    }

    mRED->EndRegion();
}

void VirtualShadowMapArray::SetShadowProperties(ShadowProperties* shadowProperties)
{
    if (mShadowSettings->EnableVirtualShadowMap() && !mLightsRenderVSM.empty())
    {
        shadowProperties->projectionDataBufferView = mShadowProperties.projectionDataBufferSRV;
        shadowProperties->pageTableBufferView = mShadowProperties.pageTableBufferSRV;
        shadowProperties->physicalPagePoolView = mShadowProperties.physicalPagePoolUAV;
    }
    else
    {
        FrameStdVector<VirtualShadowMapProjectionData> projectionData(mMemoryPool);
        projectionData.emplace_back(VirtualShadowMapProjectionData{});
        shadowProperties->projectionDataBufferView = CreateStructuredBuffer(projectionData);

        auto [pageTableBuffer, pageTableBufferUAV, pageTableBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PageTable", 1, sizeof(UInt32));
        shadowProperties->pageTableBufferView = pageTableBufferSRV;

        auto [physicalPagePool, physicalPagePoolUAV, physicalPagePoolSRV] = CreateUAVTexture("Shadow.Virtual.PhysicalPagePool", GraphicsFormat::R32_UInt, 1, 1);
        shadowProperties->physicalPagePoolView = physicalPagePoolSRV;
    }
}

void VirtualShadowMapArray::FillDefaultShadowProperties(ShadowProperties* shadowProperties)
{
    FrameStdVector<VirtualShadowMapProjectionData> projectionData(mMemoryPool);
    projectionData.emplace_back(VirtualShadowMapProjectionData{});
    shadowProperties->projectionDataBufferView = CreateStructuredBuffer(projectionData);

    auto [pageTableBuffer, pageTableBufferUAV, pageTableBufferSRV] = CreateUAVBuffer("Shadow.Virtual.PageTable", 1, sizeof(UInt32));
    shadowProperties->pageTableBufferView = pageTableBufferSRV;

    auto [physicalPagePool, physicalPagePoolUAV, physicalPagePoolSRV] = CreateUAVTexture("Shadow.Virtual.PhysicalPagePool", GraphicsFormat::R32_UInt, 1, 1);
    shadowProperties->physicalPagePoolView = physicalPagePoolSRV;
}

bool VirtualShadowMapArray::IsCacheValid()
{
    return !mShadowSettings->DisableCache && mCache.IsValid(mRED);
}

std::tuple<REDBufferRef, REDBufferView*, REDBufferView*> VirtualShadowMapArray::CreateUAVBuffer(std::string_view name, UInt32 elementCount, UInt32 elementBytes, NGIBufferUsage additionalBufferUsage)
{
    NGIBufferUsage usage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | additionalBufferUsage;

    REDBufferRef buffer = mRED->AllocateBuffer(name, NGIBufferDesc{elementCount * elementBytes, usage});

    REDBufferView* bufferUAV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});
    REDBufferView* bufferSRV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});

    return {buffer, bufferUAV, bufferSRV};
}

std::tuple<REDTextureRef, REDTextureView*, REDTextureView*> VirtualShadowMapArray::CreateUAVTexture(std::string_view name, GraphicsFormat format, UInt32 width, UInt32 height, REDTextureRef currTexture)
{
    REDTextureRef texture = currTexture
                              ? currTexture
                              : (REDTextureRef)mRED->AllocateTexture(name, NGITextureDesc{GraphicsFormat::R32_UInt, NGITextureType::Texture2D, 1, 1, width, height, 1, 1, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess});

    REDTextureView* textureUAV = mRED->AllocateTextureView(texture, NGITextureViewDesc{NGITextureUsage::UnorderedAccess, format, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}});
    REDTextureView* textureSRV =
        mRED->AllocateTextureView(texture, NGITextureViewDesc{NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess, format, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}});

    return {texture, textureUAV, textureSRV};
}

REDBufferView* VirtualShadowMapArray::CreateSRVEmptyBuffer()
{
    REDBufferRef buffer = mRED->AllocateBuffer("EmptyBuffer", NGIBufferDesc{1 * sizeof(UInt32), NGIBufferUsage::StructuredBuffer});

    return mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0u, sizeof(UInt32), GraphicsFormat::Unknown, sizeof(UInt32)});
}

REDBufferView* VirtualShadowMapArray::CreateUAVEmptyBuffer()
{
    REDBufferRef buffer = mRED->AllocateBuffer("EmptyBuffer", NGIBufferDesc{1 * sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer});

    return mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, sizeof(UInt32), GraphicsFormat::Unknown, sizeof(UInt32)});
}

void VirtualShadowMapArray::ClearRWStructuredBuffer(REDBufferView* bufferView, UInt32 value)
{
    const SizeType bufferSize = bufferView->GetDesc().SizeInBytes;

    UInt4 clearParams(0, (UInt32)bufferSize / sizeof(UInt32), value, 0);

    auto* pass = mRED->AllocatePass("ClearUAVBuffer");
    {
        pass->SetProperty(NAME_ID("_ClearResource"), bufferView);
        pass->SetProperty(NAME_ID("_ClearParams"), clearParams);

        UInt3 groupSize;
        mShadowSettings->ClearBufferUIntComputeShaderR->GetThreadGroupSize(NAME_ID("ClearCS"), groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(mShadowSettings->ClearBufferUIntComputeShaderR, NAME_ID("ClearCS"), ((UInt32)bufferSize / sizeof(UInt32) + groupSize.x - 1) / groupSize.x, 1, 1);
    }
}

REDPass* VirtualShadowMapArray::ClearRWStructuredBuffer(REDBufferView* bufferView, UInt32 minOffset, UInt32 maxOffset, UInt32 value)
{
    UInt4 clearParams(minOffset, maxOffset, value, 0);

    auto* pass = mRED->AllocatePass("ClearUAVBuffer");
    {
        pass->SetProperty(NAME_ID("_ClearResource"), bufferView);
        pass->SetProperty(NAME_ID("_ClearParams"), clearParams);

        UInt3 groupSize;
        mShadowSettings->ClearBufferUIntComputeShaderR->GetThreadGroupSize(NAME_ID("ClearCS"), groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(mShadowSettings->ClearBufferUIntComputeShaderR, NAME_ID("ClearCS"), ((maxOffset - minOffset) + groupSize.x - 1) / groupSize.x, 1, 1);
    }

    return pass;
}

REDBufferView* VirtualShadowMapArray::CreateSRVBufferView(REDBufferRef buffer, UInt32 elementBytes)
{
    return mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0u, buffer->mDesc.Size, GraphicsFormat::Unknown, elementBytes});
}

REDBufferView* VirtualShadowMapArray::CreateUAVBufferView(REDBufferRef buffer, UInt32 elementBytes)
{
    return mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, buffer->mDesc.Size, GraphicsFormat::Unknown, elementBytes});
}

void VirtualShadowMap::UpdateCacheInfo(const Float4x4& worldToLight, double levelRadius, double viewCenterAbsoluteZ, double viewRadiusZ, SInt32 absoluteLevel)
{
    mIsCacheValid = true;

    if (mAbsoluteLevel != absoluteLevel)
    {
        mIsCacheValid = false;
    }

    if (mWorldToLight != worldToLight)
    {
        mIsCacheValid = false;
    }

    if (mIsCacheValid)
    {
        double deltaZ = abs(viewCenterAbsoluteZ - mClipmapCenterAbsoluteZ);
        if ((deltaZ + levelRadius) > 0.9 * mClipmapRadiusZ)
        {
            mIsCacheValid = false;
        }
    }

    if (!mIsCacheValid)
    {
        mAbsoluteLevel = absoluteLevel;
        mWorldToLight = worldToLight;
        mClipmapCenterAbsoluteZ = viewCenterAbsoluteZ;
        mClipmapRadiusZ = viewRadiusZ;
    }
}
}   // namespace cross