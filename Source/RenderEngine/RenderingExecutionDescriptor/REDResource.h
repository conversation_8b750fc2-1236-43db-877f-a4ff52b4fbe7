#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "RenderEngine/RenderEngineForward.h"

namespace cross {

// view has all visible object / light / reflection probe visible to this view
struct REDPass;
struct REDTexture;
struct REDTextureView;
struct REDBuffer;
struct REDBufferView;
struct REDResidentTexture;
struct REDResidentTextureView;
struct REDResidentBuffer;
struct REDResidentBufferView;
struct RenderingExecutionDescriptor;
struct REDVisualizer;

/*
 Resource state in a pass
 */
struct RENDER_ENGINE_API REDResourceState
{
    NGIResourceState NGIState;
    bool Read;
    bool Write;

    REDResourceState() = default;

    REDResourceState(const REDResourceState&) = default;

    explicit REDResourceState(NGIResourceState NGIState);

    REDResourceState(NGIResourceState NGIState, bool read, bool write);

    bool IsBufferMergeAllowed(const REDResourceState& next) const;

    bool IsTextureMergeAllowed(const REDResourceState& next) const;

    REDResourceState operator|(const REDResourceState& b) const
    {
        return {NGIState | b.NGIState, Read || b.Read, Write || b.Write};
    }

    REDResourceState& operator|=(const REDResourceState& b)
    {
        NGIState |= b.NGIState;
        Read = Read || b.Read;
        Write = Write || b.Write;
        return *this;
    }

    bool operator==(const REDResourceState& b)
    {
        return (NGIState == b.NGIState) && (Read == b.Read) && (Write == b.Write);
    }
};

/*
 resource state across multi passes
 */
struct RENDER_ENGINE_API REDResourceRangeState : public REDResourceState
{
    using REDResourceState::REDResourceState;

    REDResourceRangeState(const REDResourceState& state, REDPass* firstPass, REDPass* lastPass)
        : REDResourceState{state}
        , mFirstPass{firstPass}
        , mLastPass{lastPass}
    {}

    REDPass* mFirstPass = nullptr;
    REDPass* mLastPass = nullptr;
};

struct RENDER_ENGINE_API REDSubresourceInfo
{
    REDSubresourceInfo() = default;
    REDSubresourceInfo(const REDResourceState& state)
        : State{state}
        , MergedState{nullptr}
    {}
    REDSubresourceInfo(const REDResourceState& state, REDResourceRangeState* mergedState)
        : State{state}
        , MergedState{mergedState}
    {}

    REDResourceState State;
    REDResourceRangeState* MergedState;
};

enum class REDResourceType
{
    Normal,
    External,
    ExtendedToNext,
    ExtendedFromLast,
    Resident,
};

ENUM_CLASS_FLAGS(REDResourceType)

struct RENDER_ENGINE_API REDResource
{
    friend struct RenderingExecutionDescriptor;

    // mark the resource will be used by next frame, so the REDTextureRef or REDBufferRef will remain valid in next frame
    void ExtendLifetime()
    {
        //Assert(mType == REDResourceType::Normal || mType == REDResourceType::ExtendedFromLast);
        mType = REDResourceType::ExtendedToNext;
    }

    auto& GetName() const { return mName; }

    auto GetFrameID() const { return mFrameID; }

    REDResourceType mType = REDResourceType::Normal;
    SInt32 mReferenceCount{0};
    bool bIsTransient = true;
protected:
    REDResource(std::string name, REDResourceType type, UInt64 frameID)
        : mName{name}
        , mType{type}
        , mFrameID{frameID}
    {}

    struct Subresource
    {
        NGIResourceState BeforeFrameState = NGIResourceState::Undefined;

        REDPass* LastWriter = nullptr;

        REDResourceRangeState* MergedState = nullptr;
    };

    const std::string mName = "";

    REDPass* mFirstPass = nullptr;

    REDPass* mLastPass = nullptr;

    NGIResourceState* mAfterFrameStates = nullptr;

    const UInt64 mFrameID;
};

template<typename T>
requires std::is_base_of_v<REDResource, T>
struct REDResourceRef
{
    REDResourceRef() = default;

    REDResourceRef(const REDResourceRef&) = default;

    REDResourceRef(REDResourceRef&&) = default;

    REDResourceRef& operator=(const REDResourceRef&) = default;

    REDResourceRef& operator=(REDResourceRef&&) = default;

    T* operator ->() { return mResource; }

    T* operator ->() const { return mResource; }

    operator T*() { return mResource; }

    operator T*() const { return mResource; }

    //friend auto operator <=>(const REDResourceRef& lhs, const REDResourceRef& rhs)
    //{
    //    return lhs.mResource <=> rhs.mResource;
    //}

private:
    REDResourceRef(T* res)
        : mResource{res}
        , mFrameID { res->GetFrameID() }
    { }

    bool Matches(REDResource* res) const
    {
        return res && res->GetFrameID() == mFrameID && res == mResource && res->mType == REDResourceType::ExtendedFromLast;
    }

    T* mResource = nullptr;
    UInt64 mFrameID = 0;

    friend struct RenderingExecutionDescriptor;
    friend struct REDTextureView;
    friend struct REDBufferView;
    friend struct REDTextureSubresource;
};

struct REDTextureDesc
{
    GraphicsFormat Format;
    NGITextureType Dimension;
    UInt16 MipCount;
    UInt16 SampleCount;
    UInt32 Width;
    UInt32 Height;
    UInt16 Depth;
    // 2d face counts, 6 for cubemap, 6xn for cubemaparray where n was array size
    UInt16 ArraySize;
};

struct REDBufferDesc
{
    SizeType Size;
    bool Proxy = false;
};

struct RENDER_ENGINE_API REDTexture : public REDResource
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDPass;
    friend struct REDTextureView;
    friend struct REDVisualizer;

    REDTexture(std::string_view name, const NGITextureDesc& desc, UInt64 frameID = std::numeric_limits<UInt64>::max());

    REDTexture(std::string_view name, NGITexture* externalTexture);

    auto& GetDesc() const { return mDesc; }

    auto* GetNativeTexture() const { return mNativeTexture; }

    // initialize REDTexture manually with NGITexture
    // states store current states of subresources of NGITexture, and RED will store updated state after execution
    void Initialize(NGITexture* nativeTexture, NGIResourceState* subresStates);

    bool ModifyDesc(const NGITextureDesc& desc)
    {
        // can't change num of subresource
        if (desc.ArraySize == mDesc.ArraySize &&
            desc.MipCount == mDesc.MipCount &&
            desc.Format == mDesc.Format)
        {
            mDesc = desc;
            return true;
        }
        else
        {
            Assert(false);
            return false;
        }
    }

    NGITextureDesc mDesc{};
protected:
    NGITexture* mNativeTexture = nullptr;

private:
    std::vector<Subresource> mSubresources;

    bool mMemoryless = false;

    std::vector<REDTextureView*> mTextureViews;
};

using REDTextureRef = REDResourceRef<REDTexture>;

template<typename TFunc>
void EnumerateTextureSubresource(const NGITextureDesc& texDesc, const NGITextureSubRange& subRange, TFunc&& func)
{
    // TODO(peterwjma): multi plane format support
    UInt16 planeBegin = 0;
    UInt16 planeEnd = 0;

    if (auto[depth, stencil] = FormatHasDepthStencil(texDesc.Format); depth && stencil)
    {
        // there are two planes in source texture, view may use one or two
        if (EnumHasAllFlags(subRange.Aspect, NGITextureAspect::Depth | NGITextureAspect::Stencil))
        {
            planeBegin = 0;
            planeEnd = 2;
        }
        else if (EnumHasAllFlags(subRange.Aspect, NGITextureAspect::Depth))
        {
            planeBegin = 0;
            planeEnd = 1;
        }
        else if (EnumHasAllFlags(subRange.Aspect, NGITextureAspect::Stencil))
        {
            planeBegin = 1;
            planeEnd = 2;
        }
    }
    else
    {
        // there are only one plane in source texture, view can only use this plane
        planeBegin = 0;
        planeEnd = 1;
    }

    for (auto plane = planeBegin; plane < planeEnd; plane++)
    {
        for (auto array = subRange.FirstArraySlice; array < (subRange.FirstArraySlice + subRange.ArraySize); array++)
        {
            for (auto mip = subRange.MostDetailedMip; mip < (subRange.MostDetailedMip + subRange.MipLevels); mip++)
            {
                auto index = NGICalcSubresource(mip, array, plane, texDesc.MipCount, texDesc.ArraySize);
                func(index, mip, array, plane);
            }
        }
    }
}

struct RENDER_ENGINE_API REDTextureView
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDPass;
    friend struct PropertySet;

    REDTextureView() = default;
    REDTextureView(REDTextureRef texture, const NGITextureViewDesc& desc)
        : mTexture{ texture }
        , mDesc{ desc }
    {}

    /*
     * set resource state between frames, AKA, outside render graph
     * TODO(peterwjma): this interface was settled here for convinience, but not resonable
     * Deprecated!!! specify external state when allcoate REDTexture with external NGITexture
     */
    void SetExternalState(NGIResourceState state);

    template<typename TFunc>
    void EnumerateSubresource(TFunc&& func)
    {
        auto& texDesc = mTexture->mDesc;
        auto& subRange = mDesc.SubRange;

        EnumerateTextureSubresource(texDesc, subRange, std::forward<TFunc>(func));
    }

    // get width of the most detailed mip
    UInt32 GetWidth()
    {
        return mTexture->GetDesc().Width >> mDesc.SubRange.MostDetailedMip;
    }

    // get height of the most detailed mip
    UInt32 GetHeight()
    {
        return mTexture->GetDesc().Height >> mDesc.SubRange.MostDetailedMip;
    }

    // get size of the most detailed mip
    UInt2 GetSize()
    {
        return UInt2{GetWidth(), GetHeight()};
    }

    auto GetTexture() const { return mTexture; }

    auto GetNativeTextureView() const { return mNativeTextureView; }

    auto& GetDesc() const { return mDesc; }

    REDTextureRef mTexture;

    NGITextureViewDesc mDesc{};

protected:
    NGITextureView* mNativeTextureView = nullptr;
};

struct RENDER_ENGINE_API REDTextureSubresource
{
    REDTextureRef Texture;
    std::uint32_t Subresource;

    REDTextureSubresource() = default;

    REDTextureSubresource(REDTextureRef texture, std::uint32_t subresource)
        : Texture{texture}
        , Subresource{subresource}
    {}

    friend bool operator<(const REDTextureSubresource& a, const REDTextureSubresource& b)
    {
        return a.Texture != b.Texture ? a.Texture < b.Texture : a.Subresource < b.Subresource;
    }

    friend bool operator==(const REDTextureSubresource& a, const REDTextureSubresource& b)
    {
        return a.Texture == b.Texture && a.Subresource == b.Subresource;
    }
};

struct RENDER_ENGINE_API REDBuffer : public REDResource
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDPass;
    friend struct REDComputeExecutionPayload;
    friend struct REDVisualizer;

    REDBuffer(std::string_view name, const NGIBufferDesc& desc, UInt64 frameID = std::numeric_limits<UInt64>::max());

    REDBuffer(std::string_view name, NGIBuffer* externalBuffer);

    void SetExternalState(NGIResourceState state);

    auto& GetDesc() const { return mDesc; }

    auto* GetNativeBuffer() const { return mNativeBuffer; }

    // initialize REDBuffer manually with NGIBuffer
    // state store current state of NGIBuffer, and RED will store updated state after execution
    void Initialize(NGIBuffer* nativeBuffer, NGIResourceState* state);

    bool ModifyDesc(const NGIBufferDesc& desc)
    {
        if (desc.Usage == mDesc.Usage)
        {
            mDesc = desc;
            return true;
        }
        else
        {
            Assert(false);
            return false;
        }
    }

    void ModifySize(SizeType size)
    {
        mDesc.Size = size;
    }

    NGIBufferDesc mDesc{};

    NGIBuffer* mNativeBuffer = nullptr;

protected:
    Subresource mSubresource{};

    std::vector<REDBufferView*> mBufferViews;
};

using REDBufferRef = REDResourceRef<REDBuffer>;

struct RENDER_ENGINE_API REDBufferView
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDPass;
    friend struct PropertySet;

    REDBufferView(REDBufferRef buffer, const NGIBufferViewDesc& desc)
        : mBuffer{buffer}
        , mDesc{desc}
    {}

    bool ModifyDesc(const NGIBufferViewDesc& desc)
    {
        if (mDesc.Usage == desc.Usage)
        {
            mDesc = desc;
            return true;
        }
        else
        {
            Assert(false);
            return false;
        }
    }

    void ModifyRange(SizeType offset, SizeType size)
    {
        mDesc.BufferLocation = offset;
        mDesc.SizeInBytes = size;
    }

    auto GetBuffer() const { return mBuffer; }

    auto GetNativeBufferView() const { return mNativeBufferView; }

    auto& GetDesc() const { return mDesc; }

    REDBufferRef mBuffer;

protected:
    NGIBufferViewDesc mDesc{};

    NGIBufferView* mNativeBufferView = nullptr;
};

struct REDResidentObject
{
    friend struct RenderingExecutionDescriptor;

    struct RENDER_ENGINE_API Deleter
    {
        void operator()(REDResidentObject* object);
    };

protected:
    REDResidentObject(RenderingExecutionDescriptor* RED) : mRED{ RED } {}
    virtual ~REDResidentObject() = default;
    RenderingExecutionDescriptor* mRED;
};

template<typename T, typename = std::enable_if_t<std::is_base_of_v<REDResidentObject, T>>>
using REDUniquePtr = std::unique_ptr<T, REDResidentObject::Deleter>;

struct REDResidentBuffer : public REDBuffer, public REDResidentObject
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDResidentBufferView;

private:
    REDResidentBuffer(std::string_view name, const NGIBufferDesc& desc, RenderingExecutionDescriptor* RED);
    ~REDResidentBuffer();

    NGIResourceState mState = NGIResourceState::Undefined;
};

struct REDResidentBufferView : public REDBufferView, public REDResidentObject
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDResidentBuffer;

private:
    REDResidentBufferView(REDResidentBuffer* buffer, const NGIBufferViewDesc& desc, RenderingExecutionDescriptor* RED);
    ~REDResidentBufferView();
};

struct REDResidentTexture : public REDTexture, public REDResidentObject
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDResidentTextureView;

private:
    REDResidentTexture(std::string_view name, const NGITextureDesc& desc, RenderingExecutionDescriptor* RED);
    ~REDResidentTexture();

    std::vector<NGIResourceState> mStates;
};

struct REDResidentTextureView : public REDTextureView, public REDResidentObject
{
    friend struct RenderingExecutionDescriptor;
    friend struct REDResidentTexture;

private:
    REDResidentTextureView(REDResidentTexture* texture, const NGITextureViewDesc& desc, RenderingExecutionDescriptor* RED);
    ~REDResidentTextureView();
};

}   // namespace cross
template<>
struct std::hash<cross::REDTextureSubresource> : public std::hash<int>
{
    size_t operator()(const cross::REDTextureSubresource& val) const noexcept 
    { 
        size_t hash_value = 0;
        cross::hash_combine(hash_value, robin_hood::hash_int(static_cast<uint64_t>(val.Texture)));
        cross::hash_combine(hash_value, robin_hood::hash_int(static_cast<uint64_t>(val.Subresource)));
        return hash_value;
    }
};   // namespace cross