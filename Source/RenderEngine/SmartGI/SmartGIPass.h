#pragma once

#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "SmartGIVoxel.h"
#include <RenderEngine/PrimitiveGenerator.h>
#include "RenderEngine/SkyLightSystemR.h"
#include "SmartCommon.h"
#include "Surfel/SmartSurfelCommon.h"
#include "Surfel/SmartSurfelLighting.h"

namespace cross {
class TextureCreateHelper
{
public:
    static NGITextureViewDesc GetTexture2DViewDesc(NGITextureUsage usage, GraphicsFormat format, NGITextureAspect aspect = NGITextureAspect::Color);

    static NGITextureDesc GetTexture2DDesc(GraphicsFormat format, UInt32 width, UInt32 height, NGITextureUsage usage);

    static REDTextureView* GetHistoryTextureView(RenderingExecutionDescriptor* RED, REDTextureRef historyTex, REDTextureView* defaultTexView, NGITextureAspect aspect = NGITextureAspect::Color);

    static REDTextureRef AllocateNewTemporalTexture(RenderingExecutionDescriptor* RED, std::string_view name, NGITextureUsage usage, GraphicsFormat format, UInt32 width, UInt32 height);
};

enum class CEMeta(Reflect, Editor) FoliageTwoSidedLightingMode
{
    DISABLE_INDIRECT_LIGHTING_3S = 0,
    INDIRECT_LIGHTING_3S_USE_GI,
    INDIRECT_LIGHTING_3S_USE_SKYLIGHT,
};

enum class CEMeta(Reflect, Editor) BoolOverrideType
{
    USE_GLOBAL_CONFIG = -1,
    FALSE_OVERRIDE,
    TRUE_OVERRIDE,
};

class CEMeta(Editor, Reflect) RENDER_ENGINE_API SmartGIPostProcessSetting : public PassSetting
{
public:
    SmartGIPostProcessSetting()
    {
        enable = false;
    }

    CE_Virtual_Serialize_Deserialize;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelGI"))
    BoolOverrideType mEnableVoxelGI = BoolOverrideType::USE_GLOBAL_CONFIG;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "FoliageGIIntensity_Tricked")) float mFoliageGIIntensity_Tricked = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Lighting Intensity"))
    float mIndirectLightingIntensity = -1.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Specular Lighting Intensity"))
    float mIndirectSpecularLightingIntensity = -1.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Diffuse Lighting Intensity"))
    float mIndirectDiffuseLightingIntensity = -1.0f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableGISpecularCombine"))
    BoolOverrideType mEnableGISpecularCombine = BoolOverrideType::USE_GLOBAL_CONFIG;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Reflection use GI probe when roughness greater than this value", ValueMin = "0", ValueMax = "2"))
    float mReflectionMaxRoughnessToTrace = -1.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "fade range of goughness", ValueMin = "0.000001", ValueMax = "2"))
    float mReflectionRoughnessFadeLength = -1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Uniform Downsample Factor", ValueMin = "1", ValueMax = "32"))
    int mDownsampleFactor = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "AdaptiveProbe MinDownsampleFactor", ValueMin = "1", ValueMax = "32"))
    int mAdaptiveProbeMinDownsampleFactor = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeOctResolution"))
    BoolOverrideType mProbeOctHalfResolution = BoolOverrideType::USE_GLOBAL_CONFIG;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelRadiosity Enable or not"))
    BoolOverrideType mVoxelRadiosityEnable = BoolOverrideType::USE_GLOBAL_CONFIG;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "RadiosityDecayRate"))
    float mRadiosityDecayRate = -1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "SmartSceneFirstClipmapWorldExtent"))
    float mSmartSceneFirstClipmapWorldExtent = -1;

    virtual void Initialize() override;
};

class SmartGIPassSettings : public PassSetting
{
public:
    //SmartGIPassSettings()
    //{
    //    enable = true;
    //}


    CE_Virtual_Serialize_Deserialize

    // TODO will move voxel setting outside

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Smart Surfel Settings", ToolTips = "SmartGI"))
    SmartSurfelSettings mSmartSurfelSetting;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelGI")) bool mEnableVoxelGI = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Lighting Intensity")) float mIndirectLightingIntensity = 1.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Specular Lighting Intensity")) float mIndirectSpecularLightingIntensity = 0.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Diffuse Lighting Intensity")) float mIndirectDiffuseLightingIntensity = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Emissive Object's Indirect Lighting Intensity in SmartGI, 0 means disabled, > 1 means to boost"))
    float mEmissiveGIIntensity = 1.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Last Frame SceneColor Exposure Scale, Also Affect Emissive Color"))
    float mSceneColorWithEmissiveExposureScale = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableGISpecularCombine")) bool mEnableGISpecularCombine = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Reflection use GI probe when roughness greater than this value", ValueMin = "0", ValueMax = "2"))
    float mReflectionMaxRoughnessToTrace = 1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "fade range of goughness", ValueMin = "0.000001", ValueMax = "2"))
    float mReflectionRoughnessFadeLength = 0.5f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Uniform Downsample Factor", ValueMin = "1", ValueMax = "32")) int mDownsampleFactor = 16;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "AdaptiveProbe MinDownsampleFactor", ValueMin = "1", ValueMax = "32"))
    float mAdaptiveProbeMinDownsampleFactor = 4;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeOctResolution")) bool mProbeOctHalfResolution = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelRadiosity Enable or not")) bool mVoxelRadiosityEnable = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Radiosity Rays Per Voxel", ValueMin = "1", ValueMax = "64")) UInt32 mRadiosityRaysPerVoxel = 64;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "RadiosityDecayRate")) float mRadiosityDecayRate = 1.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "UseHiZRayTracing")) bool mUseHiZRayTracing = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "NumRayCastSteps")) UInt32 mNumRayCastSteps = 64;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxScreenTraceDistance")) float mMaxScreenTraceDistance = 5000.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxScreenTraceFraction")) float mMaxScreenTraceFraction = 0.3f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "SmartSceneFirstClipmapWorldExtent")) float mSmartSceneFirstClipmapWorldExtent = 2500;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MinVoxelTraceDistance")) float mMinVoxelTraceDistance = 100.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxVoxelTraceDistance")) float mMaxVoxelTraceDistance = 100000.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelTraceSurfaceBias")) float mVoxelTraceSurfaceBias = 0.2f;
    /*
     * NOTE: Be careful when turn on this setting, may cause severe light leaking problem, especially for geometries that occupy two voxels
     *  Influences all cone trace effects(DirectLightingInjection, ConeTraceShadow, ConeTrace)
     */
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableFirstVoxelUnHit")) bool mEnableFirstVoxelUnHit = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableIsolatedPixelFlickingSuppression"))
    bool mIsolatedPixelFlickingSuppression = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowIsolatedPixel"))
    bool mDebugShowIsolatedPixel = false;
    CEMeta(Serialize, Reflect, Editor, EdtiorPropertyInfo(PropertyType = "Auto", ToolTips = "Number of zero-depth neighbors required to classify a pixel as isolated. Range: 0-8. Lower values mean more pixels will be considered isolated."))
    UInt32 mIsolatedPixelThreshold = 2;
    
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Foliage Twosided LightingMode"))
    FoliageTwoSidedLightingMode mFoliageTwosidedLightingMode = FoliageTwoSidedLightingMode::DISABLE_INDIRECT_LIGHTING_3S;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Foliage Twosided SkyLight")) float mFoliageSkyLightLerpDistance = 10000;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LongDistanceUseSkyLight")) bool mLongDistanceUseSkyLight = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LongDistanceUseSkyLight")) float mLongDistanceUseSkyLightDistStart = 5000;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LongDistanceUseSkyLightDistLerp")) float mLongDistanceUseSkyLightDistLerp = 20000;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Diffuse Boost, but not recommended"))
    float mDiffuseBoost = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "IrradianceFormatUseSH3")) bool mIrradianceFormatUseSH3 = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "BentNormalAODirectSkyLightMethod")) bool mBentNormalAODirectSkyLightMethod = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableCompositeTraces")) bool mEnableCompositeTraces = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableProbeTemporalFilter")) bool mEnableProbeTemporalFilter = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeTemporalFilterWithHitDistance")) bool mProbeTemporalFilterWithHitDistance = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableProbeSpatialFilter")) bool mEnableProbeSpatialFilter = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "SpatialFilterNumPasses")) int mSpatialFilterNumPasses = 3;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeInterpolationWithNormal")) bool mProbeInterpolationWithNormal = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableTemporalFilter")) bool mEnableTemporalFilter = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxFramesAccumulated")) float mMaxFramesAccumulated = 14.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "NumHistoryAccumulateThres")) float mNumHistoryAccumulateThres = 0.9f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "FastUpdateScale")) float mFastUpdateScale = 10.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "HistoryDistanceThreshold")) float mHistoryDistanceThreshold = 0.05f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DisocclusionDistanceThreshold")) float mDisocclusionDistanceThreshold = 100000.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "FixFrameIndex")) int mFixFrameIndex = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "UseTaaRT")) bool mUseTaaRT = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enable PreVoxelize")) bool mEnablePreVoxelize = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Voxelize New Size")) bool mVoxelizeNewSize = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Voxelize New Size Scale")) float mVoxelizeNewSizeScale = 8.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Voxelize Mesh Lod Index")) int mVoxelizeMeshLodIndex = 100;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelsForceFullUpdate")) bool mVoxelsForceFullVoxelize = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelsForceUpdateLighting")) bool mVoxelsForceUpdateLighting = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Inject Lighting from Previous Frame")) bool mInjectLightingFromPrevFrame = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelLightCulling")) bool mEnableVoxelLightCulling = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelChunkSize")) uint32_t mVoxelChunkSize = 8;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "UseLocalLightShadowCache")) bool mUseLocalLightShadowCache = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelsKeepNotClear")) bool mVoxelsKeepNotClear = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ClearTextureUseCS")) bool mClearTextureUseCS = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelOpacityTex")) bool mEnableVoxelOpacityTex = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelOpacityCompactTex")) bool mEnableVoxelOpacityCompactTex = true;
    //CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableScreenTrace")) bool mEnableTraceHZB = true;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enable DebugShowVoxels")) bool mDebugShowVoxels = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowVoxelType")) float mDebugShowVoxelType = 2;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowClipmap")) int mDebugShowClipmap = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugTraceSkyLightingOnly")) bool mDebugTraceSkyLightingOnly = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugTraceSkyLightSH")) bool mDebugTraceSkyLightSH = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowSH3")) bool mDebugShowSH3 = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugScreenPosition")) Float2 mDebugScreenPosition{0.5f, 0.5f};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugScreenPosition2")) Float2 mDebugScreenPosition2{0.8f, 0.8f};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LockShowSH3")) bool mLockShowSH3 = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugSHOffset")) Float3 mDebugSHOffset{0, 0, 0};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugSHOffset2")) Float3 mDebugSHOffset2{0, 0, 0};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugSHScale")) Float3 mDebugSHScale{10, 10, 10};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRay")) bool mEnableDebugRay = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRayFreeze")) bool mEnableDebugRayFreeze = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRayPosition")) Float2 mDebugRayPosition{0.5f, 0.5f};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRayIndex")) int mDebugRayIndex = -1;


    RENDER_PIPELINE_RESOURCE(Material, SmartVisualizeSceneMtl, "Shader/SmartGI/SmartVisualizeSceneMtl.nda", "SmartVisualizeMaterial", "", "General Settings");
    RENDER_PIPELINE_RESOURCE(Material, SmartVisualizeSHCoefsMtl, "Shader/SmartGI/SmartVisualizeSHDebugMtl.nda", "SmartVisualizeMaterial", "", "General Settings");
    RENDER_PIPELINE_RESOURCE(Material, SmartVisualizeCubeMtl, "Shader/SmartGI/SmartVisualizeCubeDebugMtl.nda", "SmartVisualizeMaterial", "", "General Settings");
    RENDER_PIPELINE_RESOURCE(Texture, EnvBRDFTexture, "Texture/envBRDFlut.nda", "EnvBRDF Texture", "", "");

    RENDER_PIPELINE_RESOURCE(ComputeShader, UpdateVoxelizeTextureShader, "Shader/SmartGI/UpdateVoxelizeTextureShader.compute.nda", "UpdateVoxelizeTextureShader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, ClearReplacementShader, "Shader/SmartGI/ClearReplacementShader.compute.nda", "ClearReplacementShader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartSceneRadiosityShader, "Shader/SmartGI/SmartSceneRadiosity.compute.nda", "SmartSceneRadiosity", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartSceneLightingCompositeShader, "Shader/SmartGI/SmartSceneLightingComposite.compute.nda", "SmartSceneLightingComposite", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartVisualizeVoxelsShader, "Shader/SmartGI/SmartVisualizeVoxels.compute.nda", "Debug Visualize Voxels Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartVoxelizerUpdateShader, "Shader/SmartGI/SmartVoxelizerUpdate.compute.nda", "Voxel Texture Clear Shader", "", "")
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartCompactTracesShader, "Shader/SmartGI/SmartFinalGatherTracing.compute.nda", "Compact Traces Shader", "", "")
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherTraceVoxelsShader, "Shader/SmartGI/SmartFinalGatherTraceVoxels.compute.nda", "Smart Final Gather Trace Voxels Shader", "", "")

    RENDER_PIPELINE_RESOURCE(ComputeShader, HiZGeneratorShader, "Shader/SmartGI/HZB.compute.nda", "HiZ Generator Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFGTraceHZBShader, "Shader/SmartGI/SmartFinalGatherTraceHZB.compute.nda", "Smart Final Gather Trace HZB Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartImportanceSamplingShader, "Shader/SmartGI/SmartImportanceSampling.compute.nda", "Smart Importance Sampling Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartImportanceSamplingHalfResShader, "Shader/SmartGI/SmartImportanceSamplingHalfRes.compute.nda", "Smart Importance Sampling Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherFilteringShader, "Shader/SmartGI/SmartFinalGatherFiltering.compute.nda", "Smart Final Gather Filtering Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherFilteringHalfResShader, "Shader/SmartGI/SmartFinalGatherFilteringHalfRes.compute.nda", "Smart Final Gather Filtering Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherFilteringProbeShader, "Shader/SmartGI/SmartFinalGatherFilteringProbe.compute.nda", "Smart Final Gather Filtering Probe Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherIntegrateShader, "Shader/SmartGI/SmartFinalGatherIntegrate.compute.nda", "Smart Final Gather Integrate Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherTemporalShader, "Shader/SmartGI/SmartFinalGatherTemporal.compute.nda", "Smart Final Gather Temporal Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherScreenProbeComputeShader, "Shader/SmartGI/FinalGatherScreenProbe.compute.nda", "Smart Final Gather Screen Probe Compute Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartFinalGatherRayDebugShader, "Shader/SmartGI/SmartFinalGatherRayDebug.compute.nda", "Smart Smart Final Gather Ray Debug Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartVoxelizerCompactComputeShader, "Shader/SmartGI/SmartVoxelizerCompact.compute.nda", "Smart Voxelizer Compact Compute Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartVoxelizerDirectLightingComputeShader, "Shader/SmartGI/SmartSceneDirectLighting.compute.nda", "Smart Scene Direct Lighting Compute Shader", "", "");
    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartVoxelLightCullingComputeShader, "Shader/SmartGI/SmartVoxelLightCulling.compute.nda", "Smart Voxel Light Culling Compute Shader", "", "")

    RENDER_PIPELINE_RESOURCE(ComputeShader, SmartVisualizeSHComputeShader, "Shader/SmartGI/SmartVisualizeSH.compute.nda", "Compute Shader", "", "");

    virtual void Initialize() override;

    ComputeShaderR* GetSmartFinalGatherRayDebugShader() const
    {
        return SmartFinalGatherRayDebugShaderR;
    }

    ComputeShaderR* GetSmartFinalGatherFilteringShader(const GameContext& gameContext) const
    {
        return IsProbeOctHalfResolution(gameContext) ? SmartFinalGatherFilteringHalfResShaderR : SmartFinalGatherFilteringShaderR;
    }

    ComputeShaderR* GetImportanceSamplingShader(const GameContext& gameContext) const
    {
        return IsProbeOctHalfResolution(gameContext) ? SmartImportanceSamplingHalfResShaderR : SmartImportanceSamplingShaderR;
    }

    ComputeShaderR* GetSmartFinalGatherIntegrateShader() const
    {
        return SmartFinalGatherIntegrateShaderR;
    }

    int GetProbeIrradianceFormat() const
    {
        return mIrradianceFormatUseSH3 ? 0 : 1;
    }

    static const SmartGIPostProcessSetting& GetGIPostProcess(const GameContext& gameContext);

    // Enable SmartGI means that ScreenSpace is default enabled
    bool IsEnableGI(const GameContext& gameContext) const
    {
        return enable; //GetGIPostProcess(gameContext).enable ? true : enable;
    }

    bool IsEnableVoxelGI(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mEnableVoxelGI == BoolOverrideType::USE_GLOBAL_CONFIG ? mEnableVoxelGI : static_cast<bool>(GetGIPostProcess(gameContext).mEnableVoxelGI);
    }

    float GetVoxelClipmapWorldExtent(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mSmartSceneFirstClipmapWorldExtent > 0 ? GetGIPostProcess(gameContext).mSmartSceneFirstClipmapWorldExtent : mSmartSceneFirstClipmapWorldExtent;
    }

    float GetIndirectLightingIntensity(const GameContext& gameContext) const
    {
        if (IsEnableGI(gameContext))
        {
            return GetGIPostProcess(gameContext).mIndirectLightingIntensity >= 0 ? GetGIPostProcess(gameContext).mIndirectLightingIntensity : mIndirectLightingIntensity;
        }
        else
        {
            return 0.f;
        }
    }

    float GetFoliageGIIntensity_Tricked(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mFoliageGIIntensity_Tricked;
    }

    float GetIndirectSpecularLightingIntensity(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mIndirectSpecularLightingIntensity >= 0 ? GetGIPostProcess(gameContext).mIndirectSpecularLightingIntensity : mIndirectSpecularLightingIntensity;
    }

    float GetIndirectDiffuseLightingIntensity(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mIndirectDiffuseLightingIntensity >= 0 ? GetGIPostProcess(gameContext).mIndirectDiffuseLightingIntensity : mIndirectDiffuseLightingIntensity;
    }

    bool IsEnableGISpecularCombine(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mEnableGISpecularCombine == BoolOverrideType::USE_GLOBAL_CONFIG ? mEnableGISpecularCombine : static_cast<bool>(GetGIPostProcess(gameContext).mEnableGISpecularCombine);
    }

    float GetMaxRoughnessToTrace(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mReflectionMaxRoughnessToTrace >= 0 ? GetGIPostProcess(gameContext).mReflectionMaxRoughnessToTrace : mReflectionMaxRoughnessToTrace;
    }

    float GetInvRoughnessFadeLength(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mReflectionRoughnessFadeLength > 0 ? (1.0f / GetGIPostProcess(gameContext).mReflectionRoughnessFadeLength) : (1.0f / mReflectionRoughnessFadeLength);
    }

    int GetDownsampleFactor(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mDownsampleFactor > 0 ? GetGIPostProcess(gameContext).mDownsampleFactor : mDownsampleFactor;
    }

    int GetAdaptiveProbeMinDownsampleFactor(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mAdaptiveProbeMinDownsampleFactor > 0 ? GetGIPostProcess(gameContext).mAdaptiveProbeMinDownsampleFactor : static_cast<int>(mAdaptiveProbeMinDownsampleFactor);
    }

    bool IsProbeOctHalfResolution(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mProbeOctHalfResolution == BoolOverrideType::USE_GLOBAL_CONFIG ? mProbeOctHalfResolution : static_cast<bool>(GetGIPostProcess(gameContext).mProbeOctHalfResolution);
    }

    bool IsVoxelRadiosityEnable(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mVoxelRadiosityEnable == BoolOverrideType::USE_GLOBAL_CONFIG ? mVoxelRadiosityEnable : static_cast<bool>(GetGIPostProcess(gameContext).mVoxelRadiosityEnable);
    }

    float GetRadiosityDecayRate(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mRadiosityDecayRate > 0 ? GetGIPostProcess(gameContext).mRadiosityDecayRate : mRadiosityDecayRate;
    }

    uint32_t GetVoxelChunkSize() const
    {
        return std::clamp(mVoxelChunkSize, 2u, SmartGI::GetClipmapResolutionXY() / 2);
    }
};


class RENDER_ENGINE_API SmartGIPass : public PassBase<SmartGIPassSettings, SmartGIPass>
{
public:
    SmartGIPass(IRenderPipeline* renderPipeline)
        : mRenderPipeline(renderPipeline) {}

    ~SmartGIPass();

    static PassDesc GetPassDesc();

public:
    enum class StageStatus
    {
        NONE_STAGE,
        HiZ_STAGE,
        SMART_GI_STAGE,
        SMART_GI_COMPLETED_STAGE,
    };

    StageStatus mStageStatus{StageStatus::NONE_STAGE};

    ViewModeVisualizeType mVisualize = ViewModeVisualizeType::Lit;

    REDTextureView* input_depthView = nullptr;
    REDTextureView* input_targetView = nullptr;

    REDTextureView* input_sceneColorView = nullptr;
    GBufferTextures input_gBufferViews{nullptr, nullptr, nullptr, nullptr};
    REDTextureView* input_objCullingGUIDView{nullptr};
    REDTextureView* input_aoView = nullptr;
    REDTextureView* input_gtaoBentNormalView = nullptr;
    REDTextureView* input_bentNormalAO = nullptr;
    const ShadowProperties* input_shadowProperties = nullptr;

    REDTextureView* output_HiZView = nullptr; //todo(timllpan): input param, use spd-hiz result, not smartgi hiz generator
    REDTextureView* output_ClosestHiZView = nullptr;
    REDTextureView* output_diffuseIndirectView = nullptr;
    REDTextureView* output_specularIndirectView = nullptr;
    REDTextureView* output_compositeIndirectView = nullptr;

private:
    void SwapEmissiveColorRT();

public:


    bool NeedClosestHiZDepth() const
    {
        return mSetting.mUseHiZRayTracing;
    }

    REDTextureView* GetIndirectLighitingView() const
    {
        if (mSetting.mDebugShowVoxels && mSetting.mEnableVoxelGI)
        {
            return mDebugState.mDebugSceneColor;
        }
        else
        {
            return output_compositeIndirectView;
        }
    }

    std::tuple<REDTextureView*, REDTextureView*> GetIndirectLighitingViews() const
    {
        return std::make_tuple(output_diffuseIndirectView, output_specularIndirectView);
    }

    bool GetEnableVoxelGI(const GameContext& gameContext)
    {
        return mSetting.IsEnableVoxelGI(gameContext);
    }

    void SetupSkyLightingGameContext(const GameContext& gameContext, REDPass* pass);

protected:
    bool ExecuteImp(const GameContext& gameContext);

    friend PassBase<SmartGIPassSettings, SmartGIPass>;

private:
    void InitializeOctahedralSolidAngleTexture(const GameContext& gameContext);

    REDTextureView* AssembleSmartGIDepthPyramid(RenderingExecutionDescriptor* RED, REDTextureView* depthView);

    void AssembleSmartGIScreenProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView);

    cross::REDTextureView* AssembleSmartGenerateRays(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView);

    void AssembleSmartFGTraceHZB(const GameContext& gameContext, RenderingExecutionDescriptor* RED, GBufferTextures& gBufferViews, REDTextureView* depthView, REDTextureView* depthPyramid, REDTextureView* sceneColorView,
                                 REDTextureView* rayInfosForTracing);

    void AssembleSmartFilteringProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED);

    void AssembleSmartConvertToIrradiance(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* rayInfosForTracing);

    void AssembleSmartIntegrate(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView);

    void AssembleSmartScreenTemporalFilter(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView*& diffuseIndirect, REDTextureView*& specularIndirect);

    void AssembleDebugSHReadPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, int probeIndex, Float2 debugScreenPos, REDTextureView* sceneColorView);

    void UpdateReadBackBuffer(const GameContext& gameContext, RenderingExecutionDescriptor* RED);

    void AssembleDebugSHDrawPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* outputRT, REDTextureRef depthView);

    void AssembleRayDebug(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* depthView, REDTextureView* rayInfosForTracing);

private:
    FinalGatherCommonCB mSmartFGCommon;
    SmartCbViewParams mSmartViewParams;
    bool mInitialized{false};
    void InitializeSmartFGCommon(const GameContext& gameContext);
    void InitializeViewParams(const GameContext& gameContext);
    void InitializeParams(const GameContext& gameContext);

    void UpdateContextSmartFGCommon();
    void UpdateContextViewParams();

    void ExtendPreSceneColorRT(REDTextureRef sceneColorRT);

    struct FinalGatherTemporalState
    {
        REDTextureRef mPreSceneColorRT{nullptr};

        REDTextureRef mDiffuseIndirectHistoryRT{nullptr};
        REDTextureRef mRoughSpecularIndirectHistoryRT{nullptr};

        REDTextureRef mNumFramesAccumulatedRT{nullptr};
        REDTextureRef mFastUpdateModeHistoryRT{nullptr};
        REDTextureRef mDefaultR8RT{nullptr};
        REDTextureView* mDefaultR8View{nullptr};

        REDTextureRef mNormalHistoryRT{nullptr};
        REDTextureRef mDepthHistoryRT{nullptr};

        REDTextureRef mScreenProbeHitDistanceHistoryRT;
        REDTextureRef mScreenProbeRadianceHistoryRT;
        REDTextureRef mScreenProbeSceneDepthHistoryRT{nullptr};
        REDTextureRef mScreenProbeTranslatedWorldPositionHistoryRT{nullptr};
    };

    FinalGatherTemporalState mTemporalState;

private:
    void UpdateSkyLightContext(const GameContext& gameContext);
    void UpdateSmartGIRenderContext(const GameContext& gameContext);

    constexpr static int cDebugProbeNum = 2;

    struct cbDebugState
    {
        REDTextureView* mDebugSceneColor{nullptr};
        int mDebugSHCoefsSize{30 * 4 + 3 * 4};
        REDBufferView* mDebugSHCoefs[cDebugProbeNum]{nullptr, nullptr};
        std::shared_ptr<float> mReadBackSHData[cDebugProbeNum];
        std::queue<std::tuple<UInt32, std::unique_ptr<NGIStagingBuffer>, void*, SizeType>> mPendingCopyBackTasks;
    };

    cbDebugState mDebugState;
    void InitializeDebugState();

private:
    IRenderPipeline* mRenderPipeline;
    std::unique_ptr<SmartVoxelRenderer> mVoxelRenderer;
    std::unique_ptr<SmartSurfelRenderer> mSurfelRenderer;
    bool mSurfelRendererInitialized{false};

#define RAY_DEBUG_MAX_FLYING_FRAME 3
    NGIStagingBuffer* mRayDebugFeedbackBuffer[RAY_DEBUG_MAX_FLYING_FRAME]{nullptr};
    PrimitiveData primitive[RAY_DEBUG_MAX_FLYING_FRAME];
    bool mRayDebugCurrentFreezeState{false};
    float* mRayDebugFreezeDatas{nullptr};
    UInt32 mRayDebugRayDataSize{0};
};

}
