// Copyright Epic Games, Inc. All Rights Reserved.
// ActorComponent.cpp: Actor component implementation.

#include "GameplayTags/GameplayTagContainer.h"
//#include "TimerManager.h"
//#include "AbilitySystemLog.h"
#include "AttributeSet/AttributeSet.h"
#include "GameplayAbilitiesDeveloperSettings.h"
#include "GameplayPrediction.h"
#include "GameplayEffect/GameplayEffectTypes.h"
#include "GameplayAbilitySpec.h"
//#include "ObjectBase/ObjectBaseHash.h"
#include "GameFramework/GameObjects/PlayerController.h"
#include "Abilities/GameplayAbilityTypes.h"
//#include "AbilitySystemStats.h"
#include "AbilitySystemGlobals.h"
//#include "Animation/AnimMontage.h"
#//include "Animation/AnimInstance.h"
#include "Abilities/GameplayAbilityTargetTypes.h"
#include "Abilities/GameplayAbility.h"
#include "AbilitySystemComponent.h"
#//include "Abilities/GameplayAbilityTargetActor.h"
//#include "TickableAttributeSetInterface.h"
#//include "GameplayTagResponseTable.h"
//#include "ProfilingDebugging/CsvProfiler.h"

#include "TickableAttributeSetInterface.h"
#include "GameFramework/GameObjects/Character.h"
#include "GameFramework/Components/CharacterMovementComponent.h"
#include "GameFramework/GameWorld.h"

#include "Utils/ContainerUtility.h"
//#include "VisualLogger/VisualLogger.h"
//
//#define LOCTEXT_NAMESPACE "AbilitySystemComponent"
//
namespace cegf{
/** Enable to log out all render state create, destroy and updatetransform events */
#define LOG_RENDER_STATE 0
//
//tatic TAutoConsoleVariable<float> CVarReplayMontageErrorThreshold(GAS_TEXT("replay.MontageErrorThreshold"), 0.5f, GAS_TEXT("Tolerance level for when montage playback position correction occurs in replays"));
//tatic TAutoConsoleVariable<bool> CVarAbilitySystemSetActivationInfoMultipleTimes(GAS_TEXT("AbilitySystem.SetActivationInfoMultipleTimes"), false, GAS_TEXT("Set this to true if some replicated Gameplay Abilities aren't setting their owning actors correctly"));
//tatic TAutoConsoleVariable<bool> CVarGasFixClientSideMontageBlendOutTime(GAS_TEXT("AbilitySystem.Fix.ClientSideMontageBlendOutTime"), true, GAS_TEXT("Enable a fix to replicate the Montage BlendOutTime for (recently) stopped Montages"));
//tatic TAutoConsoleVariable<bool> CVarUpdateMontageSectionIdToPlay(GAS_TEXT("AbilitySystem.UpdateMontageSectionIdToPlay"), true, GAS_TEXT("During tick, update the section ID that replicated montages should use"));
//tatic TAutoConsoleVariable<bool> CVarReplicateMontageNextSectionId(GAS_TEXT("AbilitySystem.ReplicateMontageNextSectionId"), true, GAS_TEXT("Apply the replicated next section Id to montages when skipping position replication"));
//tatic TAutoConsoleVariable<bool> CVarEnsureAbilitiesEndGracefully(GAS_TEXT("AbilitySystem.EnsureAbilitiesEndGracefully"), true, GAS_TEXT("When shutting down (during ClearAllAbilities) we should check if all GameplayAbilities gracefully ended. This should be disabled if you have NonInstanced abilities that are designed for multiple concurrent executions."));

void UAbilitySystemComponent::Init()
{
    UGameplayTasksComponent::Init();

	// Look for DSO AttributeSets (note we are currently requiring all attribute sets to be subobjects of the same owner. This doesn't *have* to be the case forever.
	GameObject *Owner = GetOwner();
	InitAbilityActorInfo(Owner, Owner);	// Default init to our outer owner

	// cleanup any bad data that may have gotten into SpawnedAttributes
	for (int32 Idx = SpawnedAttributes.size()-1; Idx >= 0; --Idx)
	{
		if (SpawnedAttributes[Idx] == nullptr)
		{
            SpawnedAttributes.erase(Idx + SpawnedAttributes.begin());
		}
	}

	// TODO Object Outer 
	//std::vector<ObjectBase*> ChildObjects;
	//GetObjectsWithOuter(Owner, ChildObjects, false, RF_NoFlags, EInternalObjectFlags::Garbage);
	//
	//for (ObjectBase* Obj : ChildObjects)
	//{
	//	UAttributeSet* Set = Cast<UAttributeSet>(Obj);
	//	if (Set)  
	//	{
	//		SpawnedAttributes.AddUnique(Set);
	//	}
	//}

	SetSpawnedAttributesListDirty();
}

void UAbilitySystemComponent::Uninit(bool bShouldNotifyEC)
{
    UGameplayTasksComponent::Uninit(bShouldNotifyEC);
	
	ActiveGameplayEffects.Uninitialize();
}

void UAbilitySystemComponent::Destroyed()
{
	DestroyActiveState();

	// The MarkPendingKill on these attribute sets used to be done in UninitializeComponent,
	// but it was moved here instead since it's possible for the component to be uninitialized,
	// and later re-initialized, without being destroyed - and the attribute sets need to be preserved
	// in this case. This can happen when the owning actor's level is removed and later re-added
	// to the world, since EndPlay (and therefore UninitializeComponents) will be called on
	// the owning actor when its level is removed.
	for (auto Set : GetSpawnedAttributes())
	{
		if (Set)
		{
			//Set->MarkAsGarbage();
		}
	}

	// Call the super at the end, after we've done what we needed to do
    UGameplayTasksComponent::Destroyed();
}

void UAbilitySystemComponent::Tick(float DeltaTime)
{
	SCOPED_CPU_TIMING(GAS_ASC, "UAbilitySystemComponent::Tick");
	//SCOPE_CYCLE_COUNTER(STAT_TickAbilityTasks);
	//CSV_SCOPED_TIMING_STAT_EXCLUSIVE(AbilityTasks);

	if (IsOwnerActorAuthoritative())
	{
		AnimMontage_UpdateReplicatedData();
	}

	UGameplayTasksComponent::Tick(DeltaTime);

	for (auto AttributeSet : GetSpawnedAttributes())
	{
		ITickableAttributeSetInterface* TickableSet = dynamic_cast<ITickableAttributeSetInterface*>(AttributeSet.get());
		if (TickableSet && TickableSet->ShouldTick())
		{
			TickableSet->Tick(DeltaTime);
		}
	}
}

void UAbilitySystemComponent::InitAbilityActorInfo(GameObject* InOwnerActor, GameObject* InAvatarActor)
{
	Assert(AbilityActorInfo);
	bool WasAbilityActorNull = (WeakPtrGet(AbilityActorInfo->AvatarActor) == nullptr);
    bool AvatarChanged = (InAvatarActor != WeakPtrGet(AbilityActorInfo->AvatarActor));

	AbilityActorInfo->InitFromActor(InOwnerActor, InAvatarActor, this);

	SetOwnerActor(InOwnerActor);

	// caching the previous value of the actor so we can check against it but then setting the value to the new because it may get used
	const GameObject* PrevAvatarActor = GetAvatarActor_Direct();
	SetAvatarActor_Direct(InAvatarActor);

	// if the avatar actor was null but won't be after this, we want to run the deferred gameplaycues that may not have run in NetDeltaSerialize
	// Conversely, if the ability actor was previously null, then the effects would not run in the NetDeltaSerialize. As such we want to run them now.
	if ((WasAbilityActorNull || PrevAvatarActor == nullptr) && InAvatarActor != nullptr)
	{
		HandleDeferredGameplayCues(&ActiveGameplayEffects);
	}

	if (AvatarChanged)
    {
		ABILITYLIST_SCOPE_LOCK();
		for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
		{
			if (Spec.Ability)
			{
				if (Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerActor)
				{
					UGameplayAbility* AbilityInstance = Spec.GetPrimaryInstance();
					// If we don't have the ability instance, it was either already destroyed or will get called on creation
					if (AbilityInstance)
                    {
						AbilityInstance->OnAvatarSet(AbilityActorInfo.get(), Spec);
					}
				}
				else
                {
					Spec.Ability->OnAvatarSet(AbilityActorInfo.get(), Spec);
				}
			}
		}
	}

	//if (UGameplayTagReponseTable* TagTable = UAbilitySystemGlobals::Get().GetGameplayTagResponseTable())
	//{
	//	TagTable->RegisterResponseForEvents(this);
	//}
	
	LocalAnimMontageInfo = FGameplayAbilityLocalAnimMontage();
	if (IsOwnerActorAuthoritative())
	{
		SetRepAnimMontageInfo(FGameplayAbilityRepAnimMontage());
	}

	if (bPendingMontageRep)
	{
		OnRep_ReplicatedAnimMontage();
	}
}

bool UAbilitySystemComponent::GetShouldTick() const 
{
	const bool bHasReplicatedMontageInfoToUpdate = (IsOwnerActorAuthoritative() && GetRepAnimMontageInfo().IsStopped == false);
	
	if (bHasReplicatedMontageInfoToUpdate)
	{
		return true;
	}

	bool bResult = UGameplayTasksComponent::GetShouldTick();	
	if (bResult == false)
	{ 
		for (const auto AttributeSet : GetSpawnedAttributes())
		{
            const ITickableAttributeSetInterface* TickableAttributeSet = dynamic_cast <const ITickableAttributeSetInterface*> (AttributeSet.get());
			if (TickableAttributeSet && TickableAttributeSet->ShouldTick())
			{
				bResult = true;
				break;
			}
		}
	}
	
	return bResult;
}

void UAbilitySystemComponent::SetAvatarActor(GameObject* InAvatarActor)
{
	Assert(AbilityActorInfo);
	InitAbilityActorInfo(GetOwnerActor(), InAvatarActor);
}

void UAbilitySystemComponent::ClearActorInfo()
{
	Assert(AbilityActorInfo);
	AbilityActorInfo->ClearActorInfo();
	SetOwnerActor(nullptr);
	SetAvatarActor_Direct(nullptr);
}

void UAbilitySystemComponent::OnRep_OwningActor()
{
	Assert(AbilityActorInfo);

	GameObject* LocalOwnerActor = GetOwnerActor();
	GameObject* LocalAvatarActor = GetAvatarActor_Direct();

	if (LocalOwnerActor != WeakPtrGet(AbilityActorInfo->OwnerActor) || LocalAvatarActor != WeakPtrGet(AbilityActorInfo->AvatarActor))
	{
		if (LocalOwnerActor != nullptr)
		{
			InitAbilityActorInfo(LocalOwnerActor, LocalAvatarActor);
		}
		else
		{
			ClearActorInfo();
		}
	}
}

void UAbilitySystemComponent::RefreshAbilityActorInfo()
{
	Assert(AbilityActorInfo);
    AbilityActorInfo->InitFromActor(WeakPtrGet(AbilityActorInfo->OwnerActor), WeakPtrGet(AbilityActorInfo->AvatarActor), this);
}

FGameplayAbilitySpecHandle UAbilitySystemComponent::GiveAbility(const FGameplayAbilitySpec& Spec)
{
	if (!IsValid(Spec.Ability.get()))
	{
		LOG_ERROR("GiveAbility called with an invalid Ability Class.");

		return FGameplayAbilitySpecHandle();
	}

	if (!IsOwnerActorAuthoritative())
	{
		LOG_ERROR("GiveAbility called on ability {} on the client, not allowed!", Spec.Ability->GetName());

		return FGameplayAbilitySpecHandle();
	}

	// If locked, add to pending list. The Spec.Handle is not regenerated when we receive, so returning this is ok.
	if (AbilityScopeLockCount > 0)
	{
		LOG_INFO("{}: GiveAbility {} delayed (ScopeLocked)", (GetOwner()->GetName()), Spec.Ability->GetName());
		AbilityPendingAdds.push_back(Spec);
		return Spec.Handle;
	}
	
	ABILITYLIST_SCOPE_LOCK();
	
	FGameplayAbilitySpec& OwnedSpec = ActivatableAbilities.Items.emplace_back(Spec);

	if (OwnedSpec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerActor)
	{
		// Create the instance at creation time
		CreateNewInstanceOfAbility(OwnedSpec, Spec.Ability.get());
	}
	
	OnGiveAbility(OwnedSpec);
	MarkAbilitySpecDirty(OwnedSpec, true);

	//LOG_INFO("{}: GiveAbility {} [{}] Level: {} Source: {}", GetOwner()->GetName(), Spec.Ability->GetName(), Spec.Handle.ToString(), Spec.Level, WeakPtrGet(Spec.SourceObject)->GetName());
    //LOG_INFO("GiveAbility {} [{}] Level: {} Source: {}", Spec.Ability->GetName(), Spec.Handle.ToString(), Spec.Level, WeakPtrGet(Spec.SourceObject)->GetName());
	return OwnedSpec.Handle;
}

FGameplayAbilitySpecHandle UAbilitySystemComponent::GiveAbilityAndActivateOnce(FGameplayAbilitySpec& Spec, const FGameplayEventData* GameplayEventData)
{
    // TODO after Ability.h
    Assert(false);
	if (!IsValid(Spec.Ability.get()))
	{
		LOG_ERROR("GiveAbilityAndActivateOnce called with an invalid Ability Class.");

		return FGameplayAbilitySpecHandle();
	}

	//if (Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced || Spec.Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalOnly)
	//{
	//	LOG_ERROR("GiveAbilityAndActivateOnce called on ability {} that is non instanced or won't execute on server, not allowed!", Spec.Ability->GetName());
	//
	//	return FGameplayAbilitySpecHandle();
	//}

	if (!IsOwnerActorAuthoritative())
	{
		LOG_ERROR("GiveAbilityAndActivateOnce called on ability {} on the client, not allowed!", Spec.Ability->GetName());

		return FGameplayAbilitySpecHandle();
	}

	Spec.bActivateOnce = true;

	FGameplayAbilitySpecHandle AddedAbilityHandle = GiveAbility(Spec);

	FGameplayAbilitySpec* FoundSpec = FindAbilitySpecFromHandle(AddedAbilityHandle);

	if (FoundSpec)
	{
		FoundSpec->RemoveAfterActivation = true;

		if (!InternalTryActivateAbility(AddedAbilityHandle, FPredictionKey(), nullptr, nullptr, GameplayEventData))
		{
			// We failed to activate it, so remove it now
			ClearAbility(AddedAbilityHandle);

			return FGameplayAbilitySpecHandle();
		}
	}
	else if (GameplayEventData)
	{
		// Cache the GameplayEventData in the pending spec (if it was correctly queued)
		FGameplayAbilitySpec& PendingSpec = AbilityPendingAdds.back();
		if (PendingSpec.Handle == AddedAbilityHandle)
		{
			PendingSpec.GameplayEventData = std::make_shared<FGameplayEventData>(*GameplayEventData);
		}
	}

	return AddedAbilityHandle;
}

FGameplayAbilitySpecHandle UAbilitySystemComponent::K2_GiveAbility(TSubclassOf<UGameplayAbility> AbilityClass, int32 Level /*= 0*/, int32 InputID /*= -1*/)
{
	// build and validate the ability spec
	FGameplayAbilitySpec AbilitySpec = BuildAbilitySpecFromClass(AbilityClass, Level, InputID);

	// validate the class
	if (!IsValid(AbilitySpec.Ability.get()))
	{
		LOG_ERROR("K2_GiveAbility() called with an invalid Ability Class.");

		return FGameplayAbilitySpecHandle();
	}

	// grant the ability and return the handle. This will run validation and authority checks
	return GiveAbility(AbilitySpec);
}

FGameplayAbilitySpecHandle UAbilitySystemComponent::K2_GiveAbilityAndActivateOnce(TSubclassOf<UGameplayAbility> AbilityClass, int32 Level /* = 0*/, int32 InputID /* = -1*/)
{
	// build and validate the ability spec
	FGameplayAbilitySpec AbilitySpec = BuildAbilitySpecFromClass(AbilityClass, Level, InputID);

	// validate the class
    if (!IsValid(AbilitySpec.Ability.get()))
	{
		LOG_ERROR("K2_GiveAbilityAndActivateOnce() called with an invalid Ability Class.");

		return FGameplayAbilitySpecHandle();
	}

	return GiveAbilityAndActivateOnce(AbilitySpec);
}

void UAbilitySystemComponent::SetRemoveAbilityOnEnd(FGameplayAbilitySpecHandle AbilitySpecHandle)
{
	FGameplayAbilitySpec* FoundSpec = FindAbilitySpecFromHandle(AbilitySpecHandle);
	if (FoundSpec)
	{
		if (FoundSpec->IsActive())
		{
			FoundSpec->RemoveAfterActivation = true;
		}
		else
		{
			ClearAbility(AbilitySpecHandle);
		}
	}
}

void UAbilitySystemComponent::ClearAllAbilities()
{
	// If this is called inside an ability scope lock, postpone the workload until end of scope.
	// This was introduced for abilities that trigger their owning actor's destruction on ability
	// activation.
	if (AbilityScopeLockCount > 0)
	{
		bAbilityPendingClearAll = true;
		return;
	}

	if (!IsOwnerActorAuthoritative())
	{
		LOG_ERROR("Attempted to call ClearAllAbilities() without authority.");

		return;
	}

	// Note we aren't marking any old abilities pending kill. This shouldn't matter since they will be garbage collected.
	ABILITYLIST_SCOPE_LOCK();
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		OnRemoveAbility(Spec);
	}

	// Let's add some enhanced checking if requested
	//if (CVarEnsureAbilitiesEndGracefully.GetValueOnGameThread())
	//{
	//	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	//	{
	//		if (Spec.IsActive())
	//		{
	//			ensureAlwaysMsgf(Spec.Ability->GetInstancingPolicy() != EGameplayAbilityInstancingPolicy::NonInstanced, GAS_TEXT("{}: {} was still active (ActiveCount = {}). Since it's not instanced, it's likely that TryActivateAbility and EndAbility are not matched."), __func__, *GetNameSafe(Spec.Ability), Spec.ActiveCount);
	//			ensureAlwaysMsgf(Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced, GAS_TEXT("{}: {} was still active. Since it's an instanced ability, it's likely that there's an issue with the flow of EndAbility or RemoveAbility (such as not calling the Super function)."), __func__, *GetNameSafe(Spec.Ability));
	//		}
	//	}
	//}
    auto cachedSize = ActivatableAbilities.Items.size();
	ActivatableAbilities.Items.clear();
    ActivatableAbilities.Items.resize(cachedSize);
	//ActivatableAbilities.MarkArrayDirty();

	CheckForClearedAbilities();
	bAbilityPendingClearAll = false;
}

void UAbilitySystemComponent::ClearAllAbilitiesWithInputID(int32 InputID /*= 0*/)
{
	// find all matching abilities
	std::vector<const FGameplayAbilitySpec*> OutSpecs;
	FindAllAbilitySpecsFromInputID(InputID, OutSpecs);

	// iterate through the bound abilities
	for (const FGameplayAbilitySpec* CurrentSpec : OutSpecs)
	{
		// clear the ability
		ClearAbility(CurrentSpec->Handle);
	}
}

void UAbilitySystemComponent::ClearAbility(const FGameplayAbilitySpecHandle& Handle)
{
	if (!IsOwnerActorAuthoritative())
	{
		LOG_ERROR("Attempted to call ClearAbility() on the client. This is not allowed!");

		return;
	}

	for (int Idx = 0; Idx < AbilityPendingAdds.size(); ++Idx)
	{
		if (AbilityPendingAdds[Idx].Handle == Handle)
        {
            ArrayRemoveAtSwap(AbilityPendingAdds, Idx);
			return;
		}
	}

	for (int Idx = 0; Idx < ActivatableAbilities.Items.size(); ++Idx)
	{
		Assert(ActivatableAbilities.Items[Idx].Handle.IsValid());
		if (ActivatableAbilities.Items[Idx].Handle == Handle)
		{
			if (AbilityScopeLockCount > 0)
			{
				if (ActivatableAbilities.Items[Idx].PendingRemove == false)
				{
					ActivatableAbilities.Items[Idx].PendingRemove = true;
					AbilityPendingRemoves.push_back(Handle);
				}
			}
			else
			{
				{
					// OnRemoveAbility will possibly call EndAbility. EndAbility can "do anything" including remove this abilityspec again. So a scoped list lock is necessary here.
					ABILITYLIST_SCOPE_LOCK();
					OnRemoveAbility(ActivatableAbilities.Items[Idx]);
                    ArrayRemoveAtSwap(ActivatableAbilities.Items, Idx);
					//ActivatableAbilities.MarkArrayDirty();
				}
				CheckForClearedAbilities();
			}
			return;
		}
	}
}

void UAbilitySystemComponent::OnGiveAbility(FGameplayAbilitySpec& Spec)
{
	if (!Spec.Ability)
	{
		return;
	}

	const UGameplayAbility* SpecAbility = Spec.Ability.get();
	const bool bInstancedPerActor = SpecAbility->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerActor;
	if (bInstancedPerActor/*&& SpecAbility->GetReplicationPolicy() == EGameplayAbilityReplicationPolicy::ReplicateNo*/)
	{
		// If we don't replicate and are missing an instance, add one
		if (Spec.NonReplicatedInstances.size() == 0)
		{
			CreateNewInstanceOfAbility(Spec, SpecAbility);
		}
	}
	
	// If this Ability Spec specified that it was created from an Active Gameplay Effect, then link the handle to the Active Gameplay Effect.
	if (Spec.GameplayEffectHandle.IsValid())
	{
		UAbilitySystemComponent* SourceASC = Spec.GameplayEffectHandle.GetOwningAbilitySystemComponent();
		LOG_ERROR("OnGiveAbility Spec '{}' GameplayEffectHandle had invalid Owning Ability System Component", Spec.GetDebugString());
		if (SourceASC)
		{
			FActiveGameplayEffect* SourceActiveGE = SourceASC->ActiveGameplayEffects.GetActiveGameplayEffect(Spec.GameplayEffectHandle);
			LOG_ERROR("OnGiveAbility Spec '{}' GameplayEffectHandle was not active on Owning Ability System Component '{}'", Spec.GetDebugString(), SourceASC->GetName());
			if (SourceActiveGE)
			{
                auto itr = std::find(SourceActiveGE->GrantedAbilityHandles.begin(), SourceActiveGE->GrantedAbilityHandles.end(), Spec.Handle);
                if (itr == SourceActiveGE->GrantedAbilityHandles.end())
                {
                    SourceActiveGE->GrantedAbilityHandles.push_back(Spec.Handle);
                }
				//SourceASC->ActiveGameplayEffects.MarkItemDirty(*SourceActiveGE);
			}
		}
	}
	
	for (const FAbilityTriggerData& TriggerData : Spec.Ability->AbilityTriggers)
	{
		FGameplayTag EventTag = TriggerData.TriggerTag;
	
		auto& TriggeredAbilityMap = (TriggerData.TriggerSource == EGameplayAbilityTriggerSource::GameplayEvent) ? GameplayEventTriggeredAbilities : OwnedTagTriggeredAbilities;
	
		if (TriggeredAbilityMap.count(EventTag))
		{
            auto& Handles = TriggeredAbilityMap[EventTag];
            std::unordered_set<FGameplayAbilitySpecHandle> UniqueHandles(Handles.begin(), Handles.end());
            UniqueHandles.insert(Spec.Handle);
            Handles.clear();
            Handles.insert(Handles.end(), UniqueHandles.begin(), UniqueHandles.end()); // Fixme: is this right? Do we want to trigger the ability directly of the spec?
		}
		else
		{
			std::vector<FGameplayAbilitySpecHandle> Triggers;
			Triggers.push_back(Spec.Handle);
			TriggeredAbilityMap.emplace(EventTag, Triggers);
		}
	
		if (TriggerData.TriggerSource != EGameplayAbilityTriggerSource::GameplayEvent)
		{
			FOnGameplayEffectTagCountChanged& CountChangedEvent = RegisterGameplayTagEvent(EventTag);
			// Add a change callback if it isn't on it already
	
            Assert(false);
			//if (!CountChangedEvent.IsBoundToObject(this))
			//{
			//	MonitoredTagChangedDelegateHandle = CountChangedEvent.AddObjectBase(this, &UAbilitySystemComponent::MonitoredTagChanged);
			//}
		}
	}
	
	// If there's already a primary instance, it should be the one to receive the OnGiveAbility call
	UGameplayAbility* PrimaryInstance = bInstancedPerActor ? Spec.GetPrimaryInstance() : nullptr;
	if (PrimaryInstance)
	{
		PrimaryInstance->OnGiveAbility(AbilityActorInfo.get(), Spec);
	}
	else
	{
		Spec.Ability->OnGiveAbility(AbilityActorInfo.get(), Spec);
	}
}

void UAbilitySystemComponent::OnRemoveAbility(FGameplayAbilitySpec& Spec)
{
    Assert(AbilityScopeLockCount > 0);//, GAS_TEXT("{} called without an Ability List Lock.  It can produce side effects and should be locked to pin the Spec argument."), __func__);

	if (!Spec.Ability)
	{
		return;
	}

	LOG_INFO("{}: Removing Ability [{}] {} Level: {}", GetNameSafe(GetOwner()), Spec.Handle.ToString(), GetNameSafe(Spec.Ability), Spec.Level);
    LOG_INFO("Removing Ability [{}] {} Level: {}", Spec.Handle.ToString(), GetNameSafe(Spec.Ability), Spec.Level);

	for (const FAbilityTriggerData& TriggerData : Spec.Ability->AbilityTriggers)
	{
		FGameplayTag EventTag = TriggerData.TriggerTag;
	
		auto& TriggeredAbilityMap = (TriggerData.TriggerSource == EGameplayAbilityTriggerSource::GameplayEvent) ? GameplayEventTriggeredAbilities : OwnedTagTriggeredAbilities;
	
		if (TriggeredAbilityMap.count(EventTag))
		{
            auto& Handles = TriggeredAbilityMap[EventTag];
            Handles.erase(std::remove_if(Handles.begin(), Handles.end(), [Handle = Spec.Handle](const auto& Item)
            {
                return Item == Handle;
            }), Handles.end());

			if (TriggeredAbilityMap[EventTag].size() == 0)
			{
				TriggeredAbilityMap.erase(EventTag);
			}
		}
	}

	std::vector<UGameplayAbility*> Instances = Spec.GetAbilityInstances();
	
	for (auto Instance : Instances)
	{
		if (Instance)
		{
			if (Instance->IsActive())
			{
				// End the ability but don't replicate it, OnRemoveAbility gets replicated
				bool bReplicateEndAbility = false;
				bool bWasCancelled = false;
				Instance->EndAbility(Instance->CurrentSpecHandle, Instance->CurrentActorInfo, Instance->CurrentActivationInfo, bReplicateEndAbility, bWasCancelled);
			}
			else
			{
				// Ability isn't active, but still needs to be destroyed
				if (GetOwnerRole() == ROLE_Authority)
				{
					// Only destroy if we're the server or this isn't replicated. Can't destroy on the client or replication will fail when it replicates the end state
					RemoveReplicatedInstancedAbility(Instance);
				}
	
				if (Instance->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution)
				{
					LOG_ERROR("{} was InActive, yet still instanced during OnRemove", Instance->GetName());
					//Instance->MarkAsGarbage();
				}
			}
		}
	}

    // Notify the ability that it has been removed.  It follows the same pattern as OnGiveAbility() and is only called on the primary instance of the ability or the CDO.
    UGameplayAbility* PrimaryInstance = Spec.GetPrimaryInstance();
    if (PrimaryInstance)
    {
        PrimaryInstance->OnRemoveAbility(AbilityActorInfo.get(), Spec);

        // Make sure we remove this before marking it as garbage.
        if (GetOwnerRole() == ROLE_Authority)
        {
            RemoveReplicatedInstancedAbility(PrimaryInstance);
        }
        //PrimaryInstance->MarkAsGarbage();
    }
    else
    {
        // This is error handling some edge cases
        if (Spec.IsActive())
        {
            if (Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution)
            {
#if DO_ENSURE
                // Let's make sure we aren't recursively executing this ability.
                Instances = Spec.GetAbilityInstances();
                for (UGameplayAbility* Instance : Instances)
                {
                    ensureMsgf(Instance->bIsAbilityEnding, GAS_TEXT("All instances of {} on {} should have been ended by now. Maybe it was retriggered from OnEndAbility (bad)?"), *GetNameSafe(Spec.Ability), *GetName());
                }
#endif

                // If we're a client, fall-through anyway as this is our last chance to execute OnRemoveAbility.
                if (IsOwnerActorAuthoritative())
                {
                    // We assume we are inside an EndAbility callstack (bIsActive && bIsAbilityEnding), so remove us when we get out of it (NotifyAbilityEnded).
                    Spec.RemoveAfterActivation = true;
                    return;
                }
            }
            PRAGMA_DISABLE_DEPRECATION_WARNINGS
            else if (Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced)
            {
                // Seems like it should be cancelled, but we're just following the existing pattern (could be due to functionality from OnRep)
                constexpr bool bReplicateEndAbility = false;
                constexpr bool bWasCancelled = false;
                Spec.Ability->EndAbility(Spec.Handle, AbilityActorInfo.get(), Spec.ActivationInfo, bReplicateEndAbility, bWasCancelled);
            }
            PRAGMA_ENABLE_DEPRECATION_WARNINGS
        }

        Spec.Ability->OnRemoveAbility(AbilityActorInfo.get(), Spec);
    }

	// If this Ability Spec specified that it was created from an Active Gameplay Effect, then unlink the handle to the Active Gameplay Effect.
	// Note: It's possible (maybe even likely) that the ActiveGE is no longer considered active by this point.
	// That means we can't use FindActiveGameplayEffectHandle (which fails if ActiveGE is PendingRemove), but also many of these checks will fail
	// if the ActiveGE has completed its removal.
	if (Spec.GameplayEffectHandle.IsValid()) // This can only be true on the network authority
	{
		if (UAbilitySystemComponent* SourceASC = Spec.GameplayEffectHandle.GetOwningAbilitySystemComponent())
		{
			if (FActiveGameplayEffect* SourceActiveGE = SourceASC->ActiveGameplayEffects.GetActiveGameplayEffect(Spec.GameplayEffectHandle))
			{
                ArrayRemove(SourceActiveGE->GrantedAbilityHandles, Spec.Handle);
			}
		}
	}

	Spec.ReplicatedInstances.clear();
    Spec.NonReplicatedInstances.clear();
}

void UAbilitySystemComponent::CheckForClearedAbilities()
{
	for (auto& Triggered : GameplayEventTriggeredAbilities)
	{
		// Make sure all triggered abilities still exist, if not remove
		for (int32 i = 0; i < Triggered.second.size(); i++)
		{
			FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Triggered.second[i]);

			if (!Spec)
			{
				ArrayRemoveAt(Triggered.second, i);
				i--;
			}
		}
		
		// We leave around the empty trigger stub, it's likely to be added again
	}

	for (auto& Triggered : OwnedTagTriggeredAbilities)
	{
		bool bRemovedTrigger = false;
		// Make sure all triggered abilities still exist, if not remove
        for (int32 i = 0; i < Triggered.second.size(); i++)
		{
            FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Triggered.second[i]);

			if (!Spec)
			{
                ArrayRemoveAt(Triggered.second, i);
				i--;
				bRemovedTrigger = true;
			}
		}
		
		if (bRemovedTrigger && Triggered.second.size() == 0)
		{
			// If we removed all triggers, remove the callback
			FOnGameplayEffectTagCountChanged& CountChangedEvent = RegisterGameplayTagEvent(Triggered.first);
		
			if (CountChangedEvent.IsBound())
			{
				CountChangedEvent.Remove(MonitoredTagChangedDelegateHandle);
			}
		}

		// We leave around the empty trigger stub, it's likely to be added again
	}

	std::vector<std::shared_ptr<UGameplayAbility>>& ReplicatedAbilities = GetReplicatedInstancedAbilities_Mutable();
	for (int32 i = 0; i < ReplicatedAbilities.size(); i++)
	{
		UGameplayAbility* Ability = ReplicatedAbilities[i].get();

		if (!IsValid(Ability))
		{
			//if (IsUsingRegisteredSubObjectList())
			//{
			//	RemoveReplicatedSubObject(Ability);
			//}

			ArrayRemoveAt(ReplicatedAbilities, i);
			i--;
		}
	}

	// Clear any out of date ability spec handles on active gameplay effects
	for (FActiveGameplayEffect& ActiveGE : &ActiveGameplayEffects)
	{
PRAGMA_DISABLE_DEPRECATION_WARNINGS
		for (FGameplayAbilitySpecDef& AbilitySpec : ActiveGE.Spec.GrantedAbilitySpecs)
PRAGMA_ENABLE_DEPRECATION_WARNINGS
		{
			if (AbilitySpec.AssignedHandle.IsValid() && FindAbilitySpecFromHandle(AbilitySpec.AssignedHandle) == nullptr)
			{
				bool bIsPendingAdd = false;
				for (const FAbilityListLockActiveChange* ActiveChange : AbilityListLockActiveChanges)
				{
					for (const FGameplayAbilitySpec& PendingSpec : ActiveChange->Adds)
					{
						if (PendingSpec.Handle == AbilitySpec.AssignedHandle)
						{
							bIsPendingAdd = true;
							break;
						}
					}

					if (bIsPendingAdd)
					{
						break;
					}
				}

				for (const FGameplayAbilitySpec& PendingSpec : AbilityPendingAdds)
				{
					if (PendingSpec.Handle == AbilitySpec.AssignedHandle)
					{
						bIsPendingAdd = true;
						break;
					}
				}

				if (bIsPendingAdd)
				{
					LOG_INFO("Skipped clearing AssignedHandle {} from GE {} / {}, as it is pending being added.", AbilitySpec.AssignedHandle.ToString(), ActiveGE.GetDebugString(), ActiveGE.Handle.ToString());
					continue;
				}

				LOG_INFO("::CheckForClearedAbilities is clearing AssignedHandle {} from GE {} / {}", AbilitySpec.AssignedHandle.ToString(), ActiveGE.GetDebugString(),ActiveGE.Handle.ToString() );
				AbilitySpec.AssignedHandle = FGameplayAbilitySpecHandle();
			}
		}
	}
}

void UAbilitySystemComponent::IncrementAbilityListLock()
{
	AbilityScopeLockCount++;
}
void UAbilitySystemComponent::DecrementAbilityListLock()
{
	if (--AbilityScopeLockCount == 0)
	{
		if (bAbilityPendingClearAll)
		{
			ClearAllAbilities();

			// When there are pending adds but also a pending clear-all, prioritize clear-all since ClearAllAbilities() based on an assumption 
			// that the clear-all is likely end-of-life cleanup. There may be cases where someone intentionally calls ClearAllAbilities() and 
			// then GiveAbility() within one ability scope lock like an ability that removes all abilities and grants an ability. In the future 
			// we could support this by keeping a chronological list of pending add/remove/clear-all actions and executing them in order.
			if (AbilityPendingAdds.size() > 0)
			{
				LOG_WARN("GiveAbility and ClearAllAbilities were both called within an ability scope lock. Prioritizing clear all abilities by ignoring pending adds.");
				AbilityPendingAdds.clear();
			}

			// Pending removes are no longer relevant since all abilities have been removed
			AbilityPendingRemoves.clear();
		}
		else if (AbilityPendingAdds.size() > 0 || AbilityPendingRemoves.size() > 0)
		{
			FAbilityListLockActiveChange ActiveChange(*this, AbilityPendingAdds, AbilityPendingRemoves);

			for (FGameplayAbilitySpec& Spec : ActiveChange.Adds)
			{
				if (Spec.bActivateOnce)
				{
					GiveAbilityAndActivateOnce(Spec, Spec.GameplayEventData.get());
				}
				else
				{
					GiveAbility(Spec);
				}
			}

			for (FGameplayAbilitySpecHandle& Handle : ActiveChange.Removes)
			{
				ClearAbility(Handle);
			}
		}
	}
}

FGameplayAbilitySpec* UAbilitySystemComponent::FindAbilitySpecFromHandle(FGameplayAbilitySpecHandle Handle, EConsiderPending ConsiderPending) const
{
    SCOPED_CPU_TIMING(GAS_ASC, "UAbilitySystemComponent::FindAbilitySpecFromHandle");

	for (const FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.Handle == Handle)
		{
			if (!Spec.PendingRemove || cross::EnumHasAnyFlags(ConsiderPending, EConsiderPending::PendingRemove))
			{
				return const_cast<FGameplayAbilitySpec*>(&Spec);
			}
		}
	}

	if (cross::EnumHasAnyFlags(ConsiderPending, EConsiderPending::PendingAdd))
	{
		for (const FGameplayAbilitySpec& Spec : AbilityPendingAdds)
		{
            if (!Spec.PendingRemove || cross::EnumHasAnyFlags(ConsiderPending, EConsiderPending::PendingRemove))
			{
				return const_cast<FGameplayAbilitySpec*>(&Spec);
			}
		}
	}

	return nullptr;
}

FGameplayAbilitySpec* UAbilitySystemComponent::FindAbilitySpecFromGEHandle(FActiveGameplayEffectHandle Handle) const
{
	return nullptr;
}

std::vector<const FGameplayAbilitySpec*> UAbilitySystemComponent::FindAbilitySpecsFromGEHandle(const FScopedAbilityListLock& /*Used as a Contract*/, FActiveGameplayEffectHandle ActiveGEHandle, EConsiderPending ConsiderPending) const
{
	std::vector<const FGameplayAbilitySpec*> ReturnValue;

	if (!IsOwnerActorAuthoritative())
	{
        LOG_ERROR("FindAbilitySpecsFromGEHandle is only valid on authority as FGameplayAbilitySpec::GameplayEffectHandle is not replicated and ability granting only happens on the server");
		return ReturnValue;
	}

	auto GatherGAsByGEHandle = [ActiveGEHandle, ConsiderPending, &ReturnValue](const std::vector<FGameplayAbilitySpec>& AbilitiesToConsider)
		{
			for (const FGameplayAbilitySpec& GASpec : AbilitiesToConsider)
			{
				if (GASpec.GameplayEffectHandle == ActiveGEHandle)
				{
					if (!GASpec.PendingRemove || cross::EnumHasAnyFlags(ConsiderPending, EConsiderPending::PendingRemove))
					{
						ReturnValue.push_back(&GASpec);
					}
				}
			}
		};

	// All activatable abilities (which will include abilities that are in AbilityPendingRemoves
	GatherGAsByGEHandle(GetActivatableAbilities());

	// If requested, specifically look for abilities that are pending add
	if (cross::EnumHasAnyFlags(ConsiderPending,EConsiderPending::PendingAdd))
	{
		GatherGAsByGEHandle(AbilityPendingAdds);
	}

	return ReturnValue;
}


FGameplayAbilitySpec* UAbilitySystemComponent::FindAbilitySpecFromClass(TSubclassOf<UGameplayAbility> InAbilityClass) const
{
    SCOPED_CPU_TIMING(GAS_ASC, "FindAbilitySpecFromClass");
	for (const FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.Ability == nullptr)
		{
			continue;
		}

		if (Spec.Ability->GetMetaClass() == InAbilityClass)
		{
			return const_cast<FGameplayAbilitySpec*>(&Spec);
		}
	}

	return nullptr;
}

void UAbilitySystemComponent::MarkAbilitySpecDirty(FGameplayAbilitySpec& Spec, bool WasAddOrRemove)
{
	//if (IsOwnerActorAuthoritative())
	{
		// Don't mark dirty for specs that are server only unless it was an add/remove
		//if (!(Spec.Ability && Spec.Ability->NetExecutionPolicy == EGameplayAbilityNetExecutionPolicy::ServerOnly && !WasAddOrRemove))
		//{
		//	ActivatableAbilities.MarkItemDirty(Spec);
		//}
		AbilitySpecDirtiedCallbacks.Broadcast(Spec);
	}
	//else
	//{
	//	// Clients predicting should call MarkArrayDirty to force the internal replication map to be rebuilt.
	//	ActivatableAbilities.MarkArrayDirty();
	//}
}

FGameplayAbilitySpec* UAbilitySystemComponent::FindAbilitySpecFromInputID(int32 InputID) const
{
	if (InputID != INDEX_NONE)
	{
		for (const FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
		{
			if (Spec.InputID == InputID)
			{
				return const_cast<FGameplayAbilitySpec*>(&Spec);
			}
		}
	}
	return nullptr;
}

void UAbilitySystemComponent::FindAllAbilitySpecsFromInputID(int32 InputID, std::vector<const FGameplayAbilitySpec*>& OutAbilitySpecs) const
{
	// ignore invalid inputs
	if (InputID != INDEX_NONE)
	{
		// iterate through all abilities
		for (const FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
		{
			// add maching abilities to the list
			if (Spec.InputID == InputID)
			{
				OutAbilitySpecs.push_back(&Spec);
			}
		}
	}

	LOG_INFO("OutAbilitySpecs count: {}", OutAbilitySpecs.size());
}

FGameplayAbilitySpec UAbilitySystemComponent::BuildAbilitySpecFromClass(TSubclassOf<UGameplayAbility> AbilityClass, int32 Level /*= 0*/, int32 InputID /*= -1*/)
{
	// validate the class
	if (!(AbilityClass))
	{
		LOG_ERROR("BuildAbilitySpecFromClass called with an invalid Ability Class.");

		return FGameplayAbilitySpec();
	}

	// get the CDO. GiveAbility will validate so we don't need to
	UGameplayAbility* AbilityCDO = AbilityClass.GetDefaultObject();

	// build the ability spec
	// we need to initialize this through the constructor,
	// or the Handle won't be properly set and will cause errors further down the line
	return FGameplayAbilitySpec(AbilityClass, Level, InputID);
}

void UAbilitySystemComponent::GetAllAbilities(OUT std::vector<FGameplayAbilitySpecHandle>& OutAbilityHandles) const
{
	// ensure the output array is empty
    ArrayEmpty(OutAbilityHandles, ActivatableAbilities.Items.size());

	// iterate through all activatable abilities
	// NOTE: currently this doesn't include abilities that are mid-activation
	for (const FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		// add the spec handle to the list
		OutAbilityHandles.push_back(Spec.Handle);
	}
}

void UAbilitySystemComponent::FindAllAbilitiesWithTags(OUT std::vector<FGameplayAbilitySpecHandle>& OutAbilityHandles, FGameplayTagContainer Tags, bool bExactMatch /* = true */) const
{
	// ensure the output array is empty
    ArrayEmpty(OutAbilityHandles);

	// iterate through all Ability Specs
	for (const FGameplayAbilitySpec& CurrentSpec : ActivatableAbilities.Items)
	{
		if (!CurrentSpec.Ability)
		{
			continue;
		}

		// try to get the ability instance (if instanced per actor)
		UGameplayAbility* AbilityInstance = CurrentSpec.GetPrimaryInstance();

		// default to the CDO if we can't
		if (!AbilityInstance)
		{
			AbilityInstance = CurrentSpec.Ability.get();
		}

		// ensure the ability instance is valid
		if (IsValid(AbilityInstance))
		{
			// do we want an exact match?
			if (bExactMatch)
			{
				// check if we match all tags
				if (AbilityInstance->GetAssetTags().HasAll(Tags))
				{
					// add the matching handle
					OutAbilityHandles.push_back(CurrentSpec.Handle);
				}
			}
			else
			{
		
				// check if we match any tags
				if (AbilityInstance->GetAssetTags().HasAny(Tags))
				{
					// add the matching handle
					OutAbilityHandles.push_back(CurrentSpec.Handle);
				}
			}
		}
	}
}

void UAbilitySystemComponent::FindAllAbilitiesMatchingQuery(OUT std::vector<FGameplayAbilitySpecHandle>& OutAbilityHandles, FGameplayTagQuery Query) const
{
	// ensure the output array is empty
    ArrayEmpty(OutAbilityHandles);

	// iterate through all Ability Specs
	for (const FGameplayAbilitySpec& CurrentSpec : ActivatableAbilities.Items)
	{
		// try to get the ability instance
		UGameplayAbility* AbilityInstance = CurrentSpec.GetPrimaryInstance();

		// default to the CDO if we can't
		if (!AbilityInstance)
		{
            AbilityInstance = CurrentSpec.Ability.get();
		}

		// ensure the ability instance is valid
		if (IsValid(AbilityInstance))
        {
			if (AbilityInstance->GetAssetTags().MatchesQuery(Query))
			{
				// add the matching handle
				OutAbilityHandles.push_back(CurrentSpec.Handle);
			}
		}
	}
}

void UAbilitySystemComponent::FindAllAbilitiesWithInputID(OUT std::vector<FGameplayAbilitySpecHandle>& OutAbilityHandles, int32 InputID /*= 0*/) const
{
	// ensure the output array is empty
	ArrayEmpty(OutAbilityHandles);

	// find all ability specs matching the Input ID
	std::vector<const FGameplayAbilitySpec*> MatchingSpecs;
	FindAllAbilitySpecsFromInputID(InputID, MatchingSpecs);

	// add all matching specs to the out array
	for (const FGameplayAbilitySpec* CurrentSpec : MatchingSpecs)
	{
		OutAbilityHandles.push_back(CurrentSpec->Handle);
	}
}

FGameplayEffectContextHandle UAbilitySystemComponent::GetEffectContextFromActiveGEHandle(FActiveGameplayEffectHandle Handle)
{
	FActiveGameplayEffect* ActiveGE = ActiveGameplayEffects.GetActiveGameplayEffect(Handle);
	if (ActiveGE)
	{
		return ActiveGE->Spec.GetEffectContext();
	}

	return FGameplayEffectContextHandle();
}

UGameplayAbility* UAbilitySystemComponent::CreateNewInstanceOfAbility(FGameplayAbilitySpec& Spec, const UGameplayAbility* Ability)
{
	Assert(Ability);
	//Assert(Ability->HasAllFlags(RF_ClassDefaultObject));

	GameObject* Owner = GetOwner();
	Assert(Owner);

	std::shared_ptr<UGameplayAbility> AbilityInstance = dynamic_pointer_cast<UGameplayAbility>(NewObject(Ability->GetMetaClass()));
	Assert(AbilityInstance);

	// Add it to one of our instance lists so that it doesn't GC.
	//if (AbilityInstance->GetReplicationPolicy() != EGameplayAbilityReplicationPolicy::ReplicateNo)

	//TODO after Ability.h
	Assert(false);
	if (true)
	{
		Spec.ReplicatedInstances.push_back(AbilityInstance);
		AddReplicatedInstancedAbility(AbilityInstance.get());
	}
	else
	{
        Spec.NonReplicatedInstances.push_back(AbilityInstance);
	}
	
	return AbilityInstance.get();
}

void UAbilitySystemComponent::NotifyAbilityEnded(FGameplayAbilitySpecHandle Handle, UGameplayAbility* Ability, bool bWasCancelled)
{
	Assert(Ability);
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (Spec == nullptr)
	{
		// The ability spec may have been removed while we were ending. We can assume everything was cleaned up if the spec isnt here.
		return;
	}

	LOG_INFO("{}: Ended [{}] {}. Level: {}. WasCancelled: {}", GetNameSafe(GetOwner()), Handle.ToString(), Spec->GetPrimaryInstance() ? Spec->GetPrimaryInstance()->GetName() : Ability->GetName(), Spec->Level, bWasCancelled);
    LOG_INFO("Ended [{}] {}. Level: {}. WasCancelled: {}.", Handle.ToString(), Spec->GetPrimaryInstance() ? Spec->GetPrimaryInstance()->GetName() : Ability->GetName(), Spec->Level, bWasCancelled);

	ENetRole OwnerRole = GetOwnerRole();

	// If AnimatingAbility ended, clear the pointer
	if (WeakPtrGet(LocalAnimMontageInfo.AnimatingAbility) == Ability)
	{
		ClearAnimatingAbility(Ability);
	}

	// check to make sure we do not cause a roll over to uint8 by decrementing when it is 0
	if (Spec->ActiveCount > 0)//, GAS_TEXT("NotifyAbilityEnded called when the Spec->ActiveCount <= 0 for ability {}"), *Ability->GetName()))
	{
		Spec->ActiveCount--;
	}

	// Broadcast that the ability ended
	AbilityEndedCallbacks.Broadcast(Ability);
	OnAbilityEnded.Broadcast(FAbilityEndedData(Ability, Handle, false, bWasCancelled));
	
	// Above callbacks could have invalidated the Spec pointer, so find it again
	Spec = FindAbilitySpecFromHandle(Handle);
	if (!Spec)
	{
        LOG_ERROR("{}({}): {} lost its active handle halfway through the function.", "NotifyAbilityEnded", GetNameSafe(Ability), Handle.ToString());
		return;
	}

	/** If this is instanced per execution or flagged for cleanup, mark pending kill and remove it from our instanced lists if we are the authority */
	//if (Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution)
	//{
	//	Assert(Ability->HasAnyFlags(RF_ClassDefaultObject) == false);	// Should never be calling this on a CDO for an instanced ability!
	//
	//	if (Ability->GetReplicationPolicy() != EGameplayAbilityReplicationPolicy::ReplicateNo)
	//	{
	//		if (OwnerRole == ROLE_Authority)
	//		{
	//			Spec->ReplicatedInstances.Remove(Ability);
	//			RemoveReplicatedInstancedAbility(Ability);
	//		}
	//	}
	//	else
	//	{
	//		Spec->NonReplicatedInstances.Remove(Ability);
	//	}
	//
	//	Ability->MarkAsGarbage();
	//}

	if (OwnerRole == ROLE_Authority)
	{
		if (Spec->RemoveAfterActivation && !Spec->IsActive())
		{
			// If we should remove after activation and there are no more active instances, kill it now
			ClearAbility(Handle);
		}
		else
		{
			MarkAbilitySpecDirty(*Spec);
		}
	}
}

void UAbilitySystemComponent::ClearAbilityReplicatedDataCache(FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActivationInfo& ActivationInfo)
{
	AbilityTargetDataMap.Remove( FGameplayAbilitySpecHandleAndPredictionKey(Handle, ActivationInfo.GetActivationPredictionKey()) );
}

void UAbilitySystemComponent::CancelAbility(UGameplayAbility* Ability)
{
	ABILITYLIST_SCOPE_LOCK();
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.Ability.get() == Ability)
		{
			CancelAbilitySpec(Spec, nullptr);
		}
	}
}

void UAbilitySystemComponent::CancelAbilityHandle(const FGameplayAbilitySpecHandle& AbilityHandle)
{
	ABILITYLIST_SCOPE_LOCK();
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.Handle == AbilityHandle)
		{
			CancelAbilitySpec(Spec, nullptr);
			return;
		}
	}
}

void UAbilitySystemComponent::CancelAbilities(const FGameplayTagContainer* WithTags, const FGameplayTagContainer* WithoutTags, UGameplayAbility* Ignore)
{
	ABILITYLIST_SCOPE_LOCK();
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (!Spec.IsActive() || Spec.Ability == nullptr)
		{
			continue;
		}

		const FGameplayTagContainer& AbilityTags = Spec.Ability->GetAssetTags();
		bool WithTagPass = (!WithTags || AbilityTags.HasAny(*WithTags));
		bool WithoutTagPass = (!WithoutTags || !AbilityTags.HasAny(*WithoutTags));
		
		if (WithTagPass && WithoutTagPass)
		{
			CancelAbilitySpec(Spec, Ignore);
		}
	}
}

void UAbilitySystemComponent::CancelAbilitySpec(FGameplayAbilitySpec& Spec, UGameplayAbility* Ignore)
{
	FGameplayAbilityActorInfo* ActorInfo = AbilityActorInfo.get();

	if (Spec.Ability->GetInstancingPolicy() != EGameplayAbilityInstancingPolicy::NonInstanced)
	{
		// We need to cancel spawned instance, not the CDO
		std::vector<UGameplayAbility*> AbilitiesToCancel = Spec.GetAbilityInstances();
		for (UGameplayAbility* InstanceAbility : AbilitiesToCancel)
		{
			if (InstanceAbility && Ignore != InstanceAbility)
			{
				InstanceAbility->CancelAbility(Spec.Handle, ActorInfo, InstanceAbility->GetCurrentActivationInfoRef(), true);
			}
		}
	}
	else
	{
		// Try to cancel the non instanced, this may not necessarily work
		Spec.Ability->CancelAbility(Spec.Handle, ActorInfo, FGameplayAbilityActivationInfo(), true);
	}
	MarkAbilitySpecDirty(Spec);
}

void UAbilitySystemComponent::CancelAllAbilities(UGameplayAbility* Ignore)
{
	ABILITYLIST_SCOPE_LOCK();
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.IsActive())
		{
			CancelAbilitySpec(Spec, Ignore);
		}
	}
}

void UAbilitySystemComponent::DestroyActiveState()
{
	// If we haven't already begun being destroyed
	if (!bDestroyActiveStateInitiated)// && ((GetFlags() & RF_BeginDestroyed) == 0))
	{
		// Avoid re-entrancy (ie if during CancelAbilities() an EndAbility callback destroys the Actor owning this ability system)
		bDestroyActiveStateInitiated = true;

		// Cancel all abilities before we are destroyed.
		FGameplayAbilityActorInfo* ActorInfo = AbilityActorInfo.get();
		
		// condition needed since in edge cases canceling abilities
		// while not having valid owner/ability component can crash
        if (ActorInfo && WeakPtrGet(ActorInfo->OwnerActor) && WeakPtrGet(ActorInfo->AbilitySystemComponent))
		{
			CancelAbilities();
		}

		if (IsOwnerActorAuthoritative())
		{
			// We should now ClearAllAbilities because not all abilities CanBeCanceled().
			// This will gracefully call EndAbility and clean-up all instances of the abilities.
			ClearAllAbilities();
		}
		else
		{
			// If we're a client, ClearAllAbilities won't execute and we should clean up these instances manually.
			// CancelAbilities() will only MarkPending kill InstancedPerExecution abilities.
			// TODO: Is it correct to simply mark these as Garbage rather than EndAbility?  I suspect not, but this
			// is ingrained behavior (circa 2015). Perhaps better to allow ClearAllAbilities on client if bDestroyActiveStateInitiated (Nov 2023).
			for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
			{
				std::vector<UGameplayAbility*> AbilitiesToCancel = Spec.GetAbilityInstances();
				for (UGameplayAbility* InstanceAbility : AbilitiesToCancel)
				{
					if (InstanceAbility)
					{
						//InstanceAbility->MarkAsGarbage();
					}
				}

				ArrayEmpty(Spec.ReplicatedInstances);
                ArrayEmpty(Spec.NonReplicatedInstances);
			}
		}
	}
}

void UAbilitySystemComponent::ApplyAbilityBlockAndCancelTags(const FGameplayTagContainer& AbilityTags, UGameplayAbility* RequestingAbility, bool bEnableBlockTags, const FGameplayTagContainer& BlockTags, bool bExecuteCancelTags, const FGameplayTagContainer& CancelTags)
{
	if (bEnableBlockTags)
	{
		BlockAbilitiesWithTags(BlockTags);
	}
	else
	{
		UnBlockAbilitiesWithTags(BlockTags);
	}

	if (bExecuteCancelTags)
	{
		CancelAbilities(&CancelTags, nullptr, RequestingAbility);
	}
}

bool UAbilitySystemComponent::AreAbilityTagsBlocked(const FGameplayTagContainer& Tags) const
{
	// Expand the passed in tags to get parents, not the blocked tags
	return Tags.HasAny(BlockedAbilityTags.GetExplicitGameplayTags());
}

void UAbilitySystemComponent::BlockAbilitiesWithTags(const FGameplayTagContainer& Tags)
{
	BlockedAbilityTags.UpdateTagCount(Tags, 1);
}

void UAbilitySystemComponent::UnBlockAbilitiesWithTags(const FGameplayTagContainer& Tags)
{
	BlockedAbilityTags.UpdateTagCount(Tags, -1);
}

void UAbilitySystemComponent::BlockAbilityByInputID(int32 InputID)
{
	const std::vector<uint8>& ConstBlockedAbilityBindings = GetBlockedAbilityBindings();
	if (InputID >= 0 && InputID < ConstBlockedAbilityBindings.size())
	{
		++GetBlockedAbilityBindings_Mutable()[InputID];
	}
}

void UAbilitySystemComponent::UnBlockAbilityByInputID(int32 InputID)
{
	const std::vector<uint8>& ConstBlockedAbilityBindings = GetBlockedAbilityBindings();
	if (InputID >= 0 && InputID < ConstBlockedAbilityBindings.size() && ConstBlockedAbilityBindings[InputID] > 0)
	{
		--GetBlockedAbilityBindings_Mutable()[InputID];
	}
}

#if !UE_BUILD_SHIPPING
int32 DenyClientActivation = 0;
//static FAutoConsoleVariableRef CVarDenyClientActivation(
//GAS_TEXT("AbilitySystem.DenyClientActivations"),
//	DenyClientActivation,
//	GAS_TEXT("Make server deny the next X ability activations from clients. For testing misprediction."),
//	ECVF_Default
//	);
#endif // !UE_BUILD_SHIPPING

void UAbilitySystemComponent::OnRep_ActivateAbilities()
{
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		const UGameplayAbility* SpecAbility = Spec.Ability.get();
		if (!SpecAbility)
		{
            FTimerDelegate tempDelegate = [=]() { OnRep_ActivateAbilities(); };
			// Queue up another call to make sure this gets run again, as our abilities haven't replicated yet
            GetWorld()->GetTimerManager().SetTimer(OnRep_ActivateAbilitiesTimerHandle, std::move(tempDelegate), 0.5);
			return;
		}
	}

	CheckForClearedAbilities();

	// Make a copy in case a pending ability alters the array while iterating
	std::vector<FPendingAbilityInfo> PendingCopy = PendingServerActivatedAbilities;
	ArrayEmpty(PendingServerActivatedAbilities);

	// Try to run any pending activations that couldn't run before. If they don't work now, kill them
	for (const FPendingAbilityInfo& PendingAbilityInfo : PendingCopy)
	{
		if (PendingAbilityInfo.bPartiallyActivated)
		{
			ClientActivateAbilitySucceedWithEventData(PendingAbilityInfo.Handle, PendingAbilityInfo.PredictionKey, PendingAbilityInfo.TriggerEventData);
		}
		else
		{
			ClientTryActivateAbility(PendingAbilityInfo.Handle);
		}

		// Do some warning if we're about to drop this ability activation
		if (!PendingServerActivatedAbilities.empty())
		{
			const bool bIsSame = (PendingServerActivatedAbilities[0] == PendingAbilityInfo);
			const bool bHasNewItem = !bIsSame || (PendingServerActivatedAbilities.size() > 1);
			LOG_WARN("Failed to execute Pending Ability {} (Handle {}) because it was not replicated in time for second-chance activation (it will be ignored)", PendingAbilityInfo.PredictionKey.ToString(), PendingAbilityInfo.Handle.ToString());
			LOG_WARN("New Pending Ability added during existing execution of {} (Handle {}). New ability will be ignored.", PendingAbilityInfo.PredictionKey.ToString(), PendingAbilityInfo.Handle.ToString());

			// Empty again, so we can test again on the next item.
			ArrayEmpty(PendingServerActivatedAbilities);
		}
	}

	// This is redundant but helps signal we're leaving this function with an empty pending ability list
    ArrayEmpty(PendingServerActivatedAbilities);
}

void UAbilitySystemComponent::GetActivatableGameplayAbilitySpecsByAllMatchingTags(const FGameplayTagContainer& GameplayTagContainer, std::vector<struct FGameplayAbilitySpec* >& MatchingGameplayAbilities, bool bOnlyAbilitiesThatSatisfyTagRequirements) const
{
	if (!GameplayTagContainer.IsValid())
	{
		return;
	}

	for (const FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.Ability && Spec.Ability->GetAssetTags().HasAll(GameplayTagContainer))
		{
			// Consider abilities that are blocked by tags currently if we're supposed to (default behavior).  
			// That way, we can use the blocking to find an appropriate ability based on tags when we have more than 
			// one ability that match the GameplayTagContainer.
			if (!bOnlyAbilitiesThatSatisfyTagRequirements || Spec.Ability->DoesAbilitySatisfyTagRequirements(*this))
			{
				MatchingGameplayAbilities.push_back(const_cast<FGameplayAbilitySpec*>(&Spec));
			}
		}
	}
}

bool UAbilitySystemComponent::TryActivateAbilitiesByTag(const FGameplayTagContainer& GameplayTagContainer, bool bAllowRemoteActivation)
{
	std::vector<FGameplayAbilitySpec*> AbilitiesToActivatePtrs;
	GetActivatableGameplayAbilitySpecsByAllMatchingTags(GameplayTagContainer, AbilitiesToActivatePtrs);
	if (AbilitiesToActivatePtrs.size() < 1)
	{
		return false;
	}

	// Convert from pointers (which can be reallocated, since they point to internal data) to copies of that data
	std::vector<FGameplayAbilitySpec> AbilitiesToActivate;
	AbilitiesToActivate.reserve(AbilitiesToActivatePtrs.size());
	std::transform(AbilitiesToActivatePtrs.begin(), AbilitiesToActivatePtrs.end(), AbilitiesToActivate.begin(), [](FGameplayAbilitySpec* SpecPtr) { return *SpecPtr; });

	bool bSuccess = false;
	for (const FGameplayAbilitySpec& GameplayAbilitySpec : AbilitiesToActivate)
	{
		Assert(IsValid(GameplayAbilitySpec.Ability.get()));
		bSuccess |= TryActivateAbility(GameplayAbilitySpec.Handle, bAllowRemoteActivation);
	}

	return bSuccess;
}

bool UAbilitySystemComponent::TryActivateAbilityByClass(TSubclassOf<UGameplayAbility> InAbilityToActivate, bool bAllowRemoteActivation)
{
	bool bSuccess = false;

	const UGameplayAbility* const InAbilityCDO = InAbilityToActivate.GetDefaultObject();

	for (const FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.Ability.get() == InAbilityCDO)
		{
			bSuccess |= TryActivateAbility(Spec.Handle, bAllowRemoteActivation);
			break;
		}
	}

	return bSuccess;
}

bool UAbilitySystemComponent::TryActivateAbility(FGameplayAbilitySpecHandle AbilityToActivate, bool bAllowRemoteActivation)
{
	FGameplayTagContainer FailureTags;
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityToActivate);
	if (!Spec)
	{
		LOG_WARN("TryActivateAbility called with invalid Handle");
		return false;
	}

	// don't activate abilities that are waiting to be removed
	if (Spec->PendingRemove || Spec->RemoveAfterActivation)
	{
		return false;
	}

	UGameplayAbility* Ability = Spec->Ability.get();

	if (!Ability)
	{
		LOG_WARN("TryActivateAbility called with invalid Ability");
		return false;
	}

	const FGameplayAbilityActorInfo* ActorInfo = AbilityActorInfo.get();

	// make sure the ActorInfo and then Actor on that FGameplayAbilityActorInfo are valid, if not bail out.
	if (ActorInfo == nullptr || !WeakPtrGet(ActorInfo->OwnerActor) || !WeakPtrGet(ActorInfo->AvatarActor))
	{
		return false;
	}

		
	const ENetRole NetMode = WeakPtrGet(ActorInfo->AvatarActor)->GetLocalRole();

	// This should only come from button presses/local instigation (AI, etc).
	if (NetMode == ROLE_SimulatedProxy)
	{
		return false;
	}

	bool bIsLocal = AbilityActorInfo->IsLocallyControlled();

	// Check to see if this a local only or server only ability, if so either remotely execute or fail
	//if (!bIsLocal && (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalOnly || Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalPredicted))
	if (0)
    {
		if (bAllowRemoteActivation)
		{
			ClientTryActivateAbility(AbilityToActivate);
			return true;
		}

		LOG_INFO("Can't activate LocalOnly or LocalPredicted ability {} when not local.", Ability->GetName());
		return false;
	}

	//if (NetMode != ROLE_Authority && (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::ServerOnly || Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::ServerInitiated))
	if (0)
	{
		if (bAllowRemoteActivation)
		{
			//FScopedCanActivateAbilityLogEnabler LogEnabler;
			if (Ability->CanActivateAbility(AbilityToActivate, ActorInfo, nullptr, nullptr, &FailureTags))
			{
				// No prediction key, server will assign a server-generated key
				CallServerTryActivateAbility(AbilityToActivate, Spec->InputPressed, FPredictionKey());
				return true;
			}
			else
			{
				NotifyAbilityFailed(AbilityToActivate, Ability, FailureTags);
				return false;
			}
		}

		LOG_INFO("Can't activate ServerOnly or ServerInitiated ability {} when not the server.", Ability->GetName());
		return false;
	}

	return InternalTryActivateAbility(AbilityToActivate);
}

bool UAbilitySystemComponent::IsAbilityInputBlocked(int32 InputID) const
{
	// Check if this ability's input binding is currently blocked
	const std::vector<uint8>& ConstBlockedAbilityBindings = GetBlockedAbilityBindings();
	if (InputID >= 0 && InputID < ConstBlockedAbilityBindings.size() && ConstBlockedAbilityBindings[InputID] > 0)
	{
		return true;
	}

	return false;
}

/**
 * Attempts to activate the ability.
 *	-This function calls CanActivateAbility
 *	-This function handles instancing
 *	-This function handles networking and prediction
 *	-If all goes well, CallActivateAbility is called next.
 */
bool UAbilitySystemComponent::InternalTryActivateAbility(FGameplayAbilitySpecHandle Handle, FPredictionKey InPredictionKey, UGameplayAbility** OutInstancedAbility, FOnGameplayAbilityEnded::FDelegate* OnGameplayAbilityEndedDelegate, const FGameplayEventData* TriggerEventData)
{
	const FGameplayTag& NetworkFailTag = UAbilitySystemGlobals::Get().ActivateFailNetworkingTag;
	
	InternalTryActivateAbilityFailureTags.Reset();

	if (Handle.IsValid() == false)
	{
		LOG_WARN("InternalTryActivateAbility called with invalid Handle! ASC: {}. AvatarActor: {}", GetName(), GetNameSafe(GetAvatarActor_Direct()));
		return false;
	}

	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (!Spec)
	{
		LOG_WARN("InternalTryActivateAbility called with a valid handle but no matching ability was found. Handle: {} ASC: {}. AvatarActor: {}", Handle.ToString(), GetName(), GetNameSafe(GetAvatarActor_Direct()));
		return false;
	}

	// Lock ability list so our Spec doesn't get destroyed while activating
	ABILITYLIST_SCOPE_LOCK();

	const FGameplayAbilityActorInfo* ActorInfo = AbilityActorInfo.get();

	// make sure the ActorInfo and then Actor on that FGameplayAbilityActorInfo are valid, if not bail out.
    if (ActorInfo == nullptr || !WeakPtrGet(ActorInfo->OwnerActor) || !WeakPtrGet(ActorInfo->AvatarActor))
	{
		return false;
	}

	// This should only come from button presses/local instigation (AI, etc)
	ENetRole NetMode = ROLE_SimulatedProxy;

	// Use PC netmode if its there
	if (PlayerController* PC = WeakPtrGet(ActorInfo->PlayerController_))
	{
		NetMode = PC->GetLocalRole();
	}
	// Fallback to avataractor otherwise. Edge case: avatar "dies" and becomes torn off and ROLE_Authority. We don't want to use this case (use PC role instead).
	else if (GameObject* LocalAvatarActor = GetAvatarActor_Direct())
	{
	    NetMode = LocalAvatarActor->GetLocalRole();
	}

	if (NetMode == ROLE_SimulatedProxy)
	{
		return false;
	}

	bool bIsLocal = AbilityActorInfo->IsLocallyControlled();

	UGameplayAbility* Ability = Spec->Ability.get();

	if (!Ability)
	{
		LOG_WARN("InternalTryActivateAbility called with invalid Ability");
		return false;
	}

	// Check to see if this a local only or server only ability, if so don't execute
	//if (!bIsLocal)
 //   {
	//	if (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalOnly || (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalPredicted && !InPredictionKey.IsValidKey()))
	//	{
	//		// If we have a valid prediction key, the ability was started on the local client so it's okay
	//		UE_LOG(LogAbilitySystem, Warning, GAS_TEXT("{}: Can't activate {} ability {} when not local"), *GetNameSafe(GetOwner()), *UEnum::GetValueAsString<EGameplayAbilityNetExecutionPolicy::Type>(Ability->GetNetExecutionPolicy()), *Ability->GetName());
	//		UE_VLOG(GetOwner(), VLogAbilitySystem, Warning, GAS_TEXT("Can't activate {} ability {} when not local"), *UEnum::GetValueAsString<EGameplayAbilityNetExecutionPolicy::Type>(Ability->GetNetExecutionPolicy()), *Ability->GetName());
	//	
	//		if (NetworkFailTag.IsValid())
	//		{
	//			InternalTryActivateAbilityFailureTags.AddTag(NetworkFailTag);
	//			NotifyAbilityFailed(Handle, Ability, InternalTryActivateAbilityFailureTags);
	//		}
	//	
	//		return false;
	//	}		
	//}

	//if (NetMode != ROLE_Authority && (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::ServerOnly || Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::ServerInitiated))
	//{
	//	UE_LOG(LogAbilitySystem, Warning, GAS_TEXT("{}: Can't activate {} ability {} when not the server"), *GetNameSafe(GetOwner()), *UEnum::GetValueAsString<EGameplayAbilityNetExecutionPolicy::Type>(Ability->GetNetExecutionPolicy()), *Ability->GetName());
	//	UE_VLOG(GetOwner(), VLogAbilitySystem, Warning, GAS_TEXT("Can't activate {} ability {} when not the server"), *UEnum::GetValueAsString<EGameplayAbilityNetExecutionPolicy::Type>(Ability->GetNetExecutionPolicy()), *Ability->GetName());
	//
	//	if (NetworkFailTag.IsValid())
	//	{
	//		InternalTryActivateAbilityFailureTags.AddTag(NetworkFailTag);
	//		NotifyAbilityFailed(Handle, Ability, InternalTryActivateAbilityFailureTags);
	//	}
	//
	//	return false;
	//}

	// If it's an instanced one, the instanced ability will be set, otherwise it will be null
	UGameplayAbility* InstancedAbility = Spec->GetPrimaryInstance();
	UGameplayAbility* AbilitySource = InstancedAbility ? InstancedAbility : Ability;

	if (TriggerEventData)
	{
		if (!AbilitySource->ShouldAbilityRespondToEvent(ActorInfo, TriggerEventData))
		{
			LOG_INFO("{}: Can't activate {} because ShouldAbilityRespondToEvent was false.", GetNameSafe(GetOwner()), Ability->GetName());
			LOG_INFO("Can't activate {} because ShouldAbilityRespondToEvent was false.", Ability->GetName());

			NotifyAbilityFailed(Handle, AbilitySource, InternalTryActivateAbilityFailureTags);
			return false;
		}
	}

	{
		const FGameplayTagContainer* SourceTags = TriggerEventData ? &TriggerEventData->InstigatorTags : nullptr;
		const FGameplayTagContainer* TargetTags = TriggerEventData ? &TriggerEventData->TargetTags : nullptr;

		//FScopedCanActivateAbilityLogEnabler LogEnabler;
		if (!AbilitySource->CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, &InternalTryActivateAbilityFailureTags))
		{
			// At least let the user know that the native CanActivateAbility rejected it
			if (InternalTryActivateAbilityFailureTags.IsEmpty())
			{
				InternalTryActivateAbilityFailureTags.AddTag(GetDefault<UGameplayAbilitiesDeveloperSettings>()->ActivateFailCanActivateAbilityTag);
			}

			// CanActivateAbility with LogEnabler will have UE_LOG/UE_VLOG so don't add more failure logs here
			NotifyAbilityFailed(Handle, AbilitySource, InternalTryActivateAbilityFailureTags);
			return false;
		}
	}

	// If we're InstancedPerActor and we're already active, don't let us activate again as this breaks the graph
	if (Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerActor)
	{
		if (Spec->IsActive())
		{
			if (Ability->bRetriggerInstancedAbility && InstancedAbility)
			{
				LOG_INFO("{}: Ending {} prematurely to retrigger.", GetNameSafe(GetOwner()), Ability->GetName());
				LOG_INFO("Ending {} prematurely to retrigger.", Ability->GetName());

				constexpr bool bReplicateEndAbility = true;
				constexpr bool bWasCancelled = false;
				const FGameplayAbilityActivationInfo& ActivationInfo = InstancedAbility->GetCurrentActivationInfoRef();
				InstancedAbility->EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
			}
			else
			{
				LOG_INFO("Can't activate instanced per actor ability {} when their is already a currently active instance for this actor.", Ability->GetName());
				return false;
			}
		}
	}

	// Make sure we have a primary
	if (Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerActor && !InstancedAbility)
	{
		LOG_WARN("InternalTryActivateAbility called but instanced ability is missing! NetMode: {}. Ability: {}", (int32)NetMode, Ability->GetName());
		return false;
	}

PRAGMA_DISABLE_DEPRECATION_WARNINGS
	// We have deprecated NonInstanced and Spec.ActivationInfo but keep backwards compatibility
	const bool bNonInstanced = Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced;
	FGameplayAbilityActivationInfo NewActivationInfo;
	FGameplayAbilityActivationInfo& ActivationInfo = bNonInstanced ? Spec->ActivationInfo : NewActivationInfo;
PRAGMA_ENABLE_DEPRECATION_WARNINGS

	// Setup a fresh ActivationInfo (possibly overwriting the Spec's ActivationInfo if non-instanced)
	ActivationInfo = FGameplayAbilityActivationInfo(ActorInfo->OwnerActor.lock().get());

	// If we are the server or this is local only
	if (/*Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalOnly || */(NetMode == ROLE_Authority))
	{
		// if we're the server and don't have a valid key or this ability should be started on the server create a new activation key
		//bool bCreateNewServerKey = NetMode == ROLE_Authority &&
		//	(!InPredictionKey.IsValidKey() ||
		//	 (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::ServerInitiated ||
		//	  Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::ServerOnly));
		//if (bCreateNewServerKey)
		//{
		//	ActivationInfo.ServerSetActivationPredictionKey(FPredictionKey::CreateNewServerInitiatedKey(this));
		//}
		//else if (InPredictionKey.IsValidKey())
		//{
		//	// Otherwise if available, set the prediction key to what was passed up
		//	ActivationInfo.ServerSetActivationPredictionKey(InPredictionKey);
		//}

		// we may have changed the prediction key so we need to update the scoped key to match
		//FScopedPredictionWindow ScopedPredictionWindow(this, ActivationInfo.GetActivationPredictionKey());

		// ----------------------------------------------
		// Tell the client that you activated it (if we're not local and not server only)
		// ----------------------------------------------
		//if (!bIsLocal && Ability->GetNetExecutionPolicy() != EGameplayAbilityNetExecutionPolicy::ServerOnly)
		//{
		//	if (TriggerEventData)
		//	{
		//		ClientActivateAbilitySucceedWithEventData(Handle, ActivationInfo.GetActivationPredictionKey(), *TriggerEventData);
		//	}
		//	else
		//	{
		//		ClientActivateAbilitySucceed(Handle, ActivationInfo.GetActivationPredictionKey());
		//	}
		//	
		//	// This will get copied into the instanced abilities
		//	ActivationInfo.bCanBeEndedByOtherInstance = Ability->bServerRespectsRemoteAbilityCancellation;
		//}

		// ----------------------------------------------
		//	Call ActivateAbility (note this could end the ability too!)
		// ----------------------------------------------

		// Create instance of this ability if necessary
		if (Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution)
		{
			InstancedAbility = CreateNewInstanceOfAbility(*Spec, Ability);
			InstancedAbility->CallActivateAbility(Handle, ActorInfo, ActivationInfo, OnGameplayAbilityEndedDelegate, TriggerEventData);
		}
		else
		{
			AbilitySource->CallActivateAbility(Handle, ActorInfo, ActivationInfo, OnGameplayAbilityEndedDelegate, TriggerEventData);
		}
	}
	//else if (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalPredicted)
	//{
	//	// Flush server moves that occurred before this ability activation so that the server receives the RPCs in the correct order
	//	// Necessary to prevent abilities that trigger animation root motion or impact movement from causing network corrections
	//	if (!ActorInfo->IsNetAuthority())
	//	{
	//		ACharacter* AvatarCharacter = Cast<ACharacter>(ActorInfo->AvatarActor.Get());
	//		if (AvatarCharacter)
	//		{
	//			UCharacterMovementComponent* AvatarCharMoveComp = Cast<UCharacterMovementComponent>(AvatarCharacter->GetMovementComponent());
	//			if (AvatarCharMoveComp)
	//			{
	//				AvatarCharMoveComp->FlushServerMoves();
	//			}
	//		}
	//	}

	//	// This execution is now officially EGameplayAbilityActivationMode:Predicting and has a PredictionKey
	//	FScopedPredictionWindow ScopedPredictionWindow(this, true);

	//	ActivationInfo.SetPredicting(ScopedPredictionKey);
	//	
	//	// This must be called immediately after GeneratePredictionKey to prevent problems with recursively activating abilities
	//	if (TriggerEventData)
	//	{
	//		ServerTryActivateAbilityWithEventData(Handle, Spec->InputPressed, ScopedPredictionKey, *TriggerEventData);
	//	}
	//	else
	//	{
	//		CallServerTryActivateAbility(Handle, Spec->InputPressed, ScopedPredictionKey);
	//	}

	//	// When this prediction key is caught up, we better know if the ability was confirmed or rejected
	//	//ScopedPredictionKey.NewCaughtUpDelegate().BindObjectBase(this, &UAbilitySystemComponent::OnClientActivateAbilityCaughtUp, Handle, ScopedPredictionKey.Current);

	//	if (Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution)
	//	{
	//		// For now, only NonReplicated + InstancedPerExecution abilities can be Predictive.
	//		// We lack the code to predict spawning an instance of the execution and then merge/combine
	//		// with the server spawned version when it arrives.

	//		//if (Ability->GetReplicationPolicy() == EGameplayAbilityReplicationPolicy::ReplicateNo)
	//		{
	//			InstancedAbility = CreateNewInstanceOfAbility(*Spec, Ability);
	//			InstancedAbility->CallActivateAbility(Handle, ActorInfo, ActivationInfo, OnGameplayAbilityEndedDelegate, TriggerEventData);
	//		}
	//		//else
	//		//{
	//		//	LOG_ERROR("InternalTryActivateAbility called on ability {} that is InstancedPerExecution and Replicated. This is an invalid configuration."), *Ability->GetName() );
	//		//}
	//	}
	//	else
	//	{
	//		AbilitySource->CallActivateAbility(Handle, ActorInfo, ActivationInfo, OnGameplayAbilityEndedDelegate, TriggerEventData);
	//	}
	//}
	
	if (InstancedAbility)
	{
		if (OutInstancedAbility)
		{
			*OutInstancedAbility = InstancedAbility;
		}

		//// UGameplayAbility::PreActivate actually sets this internally (via SetCurrentInfo) which happens after replication (this is only set locally).  Let's cautiously remove this code.
		//if (CVarAbilitySystemSetActivationInfoMultipleTimes.GetValueOnGameThread())
		//{
		//	InstancedAbility->SetCurrentActivationInfo(ActivationInfo);	// Need to push this to the ability if it was instanced.
		//}
	}

	MarkAbilitySpecDirty(*Spec);

	//const UWorld* LocalWorld = GetWorld();
	//if (ensureMsgf(LocalWorld, GAS_TEXT("{}: Could not GetWorld during activation of {}"), __func__, *GetNameSafe(Ability)))
	//{
	//	AbilityLastActivatedTime = LocalWorld->GetTimeSeconds();
	//}

	//UE_LOG(LogAbilitySystem, Log, GAS_TEXT("{}: Activated [{}] {}. Level: {}. PredictionKey: {}."), *GetNameSafe(GetOwner()), *Spec->Handle.ToString(), *GetNameSafe(AbilitySource), Spec->Level, *ActivationInfo.GetActivationPredictionKey().ToString());
	//UE_VLOG(GetOwner(), VLogAbilitySystem, Log, GAS_TEXT("Activated [{}] {}. Level: {}. PredictionKey: {}."), *Spec->Handle.ToString(), *GetNameSafe(AbilitySource), Spec->Level, *ActivationInfo.GetActivationPredictionKey().ToString());
	return true;
}

void UAbilitySystemComponent::ServerTryActivateAbility(FGameplayAbilitySpecHandle Handle, bool InputPressed, FPredictionKey PredictionKey)
{
	InternalServerTryActivateAbility(Handle, InputPressed, PredictionKey, nullptr);
}

//bool UAbilitySystemComponent::ServerTryActivateAbility_Validate(FGameplayAbilitySpecHandle Handle, bool InputPressed, FPredictionKey PredictionKey)
//{
//	return true;
//}

void UAbilitySystemComponent::ServerTryActivateAbilityWithEventData(FGameplayAbilitySpecHandle Handle, bool InputPressed, FPredictionKey PredictionKey, FGameplayEventData TriggerEventData)
{
	InternalServerTryActivateAbility(Handle, InputPressed, PredictionKey, &TriggerEventData);
}

//bool UAbilitySystemComponent::ServerTryActivateAbilityWithEventData_Validate(FGameplayAbilitySpecHandle Handle, bool InputPressed, FPredictionKey PredictionKey, FGameplayEventData TriggerEventData)
//{
//	return true;
//}

void UAbilitySystemComponent::ClientTryActivateAbility(FGameplayAbilitySpecHandle Handle)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (!Spec)
	{
		// Can happen if the client gets told to activate an ability the same frame that abilities are added on the server
		FPendingAbilityInfo AbilityInfo;
		AbilityInfo.Handle = Handle;
		AbilityInfo.bPartiallyActivated = false;

		// This won't add it if we're currently being called from the pending list
        ArrayAddUnique(PendingServerActivatedAbilities, AbilityInfo);
		return;
	}

	InternalTryActivateAbility(Handle);
}

void UAbilitySystemComponent::InternalServerTryActivateAbility(FGameplayAbilitySpecHandle Handle, bool InputPressed, const FPredictionKey& PredictionKey, const FGameplayEventData* TriggerEventData)
{
#if WITH_SERVER_CODE
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	if (DenyClientActivation > 0)
	{
		DenyClientActivation--;
		ClientActivateAbilityFailed(Handle, PredictionKey.Current);
		return;
	}
#endif

	ABILITYLIST_SCOPE_LOCK();

	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (!Spec)
	{
		// Can potentially happen in race conditions where client tries to activate ability that is removed server side before it is received.
		UE_VLOG_UELOG(GetOwner(), LogAbilitySystem, Display, GAS_TEXT("{}: Rejecting ClientActivation of ability with invalid SpecHandle!"), __func__);
		ClientActivateAbilityFailed(Handle, PredictionKey.Current);
		return;
	}

	const UGameplayAbility* AbilityToActivate = Spec->Ability;

	if (!ensure(AbilityToActivate))
	{
		UE_VLOG_UELOG(GetOwner(), LogAbilitySystem, Error, GAS_TEXT("{}: Rejecting ClientActivation of unconfigured spec ability!"), __func__);
		ClientActivateAbilityFailed(Handle, PredictionKey.Current);
		return;
	}

	// Ignore a client trying to activate an ability requiring server execution
	if (AbilityToActivate->GetNetSecurityPolicy() == EGameplayAbilityNetSecurityPolicy::ServerOnlyExecution ||
		AbilityToActivate->GetNetSecurityPolicy() == EGameplayAbilityNetSecurityPolicy::ServerOnly)
	{
		UE_VLOG_UELOG(GetOwner(), LogAbilitySystem, Display, GAS_TEXT("{}: Rejecting ClientActivation of {} due to security policy violation."), __func__, *GetNameSafe(AbilityToActivate));
		ClientActivateAbilityFailed(Handle, PredictionKey.Current);
		return;
	}

	// Consume any pending target info, to clear out cancels from old executions
	ConsumeAllReplicatedData(Handle, PredictionKey);

	FScopedPredictionWindow ScopedPredictionWindow(this, PredictionKey);

	ensure(AbilityActorInfo);

	SCOPE_CYCLE_COUNTER(STAT_AbilitySystemComp_ServerTryActivate);
	SCOPE_CYCLE_UOBJECT(Ability, AbilityToActivate);

	UGameplayAbility* InstancedAbility = nullptr;
	Spec->InputPressed = true;

	// Attempt to activate the ability (server side) and tell the client if it succeeded or failed.
	if (InternalTryActivateAbility(Handle, PredictionKey, &InstancedAbility, nullptr, TriggerEventData))
	{
		// TryActivateAbility handles notifying the client of success, but let's still log it
		UE_VLOG_UELOG(GetOwner(), LogAbilitySystem, Verbose, GAS_TEXT("{}: Accepted ClientActivation of {} with PredictionKey {}."), __func__, *GetNameSafe(Spec->Ability), *PredictionKey.ToString());
	}
	else
	{
		UE_VLOG_UELOG(GetOwner(), LogAbilitySystem, Display, GAS_TEXT("{}: Rejecting ClientActivation of {} with PredictionKey {}. InternalTryActivateAbility failed: {}"),
			__func__, *GetNameSafe(Spec->Ability), *PredictionKey.ToString(), *InternalTryActivateAbilityFailureTags.ToStringSimple() );

		ClientActivateAbilityFailed(Handle, PredictionKey.Current);
		Spec->InputPressed = false;

		MarkAbilitySpecDirty(*Spec);
	}
#endif
}

void UAbilitySystemComponent::ReplicateEndOrCancelAbility(FGameplayAbilitySpecHandle Handle, FGameplayAbilityActivationInfo ActivationInfo, UGameplayAbility* Ability, bool bWasCanceled)
{
	//if (Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::LocalPredicted || Ability->GetNetExecutionPolicy() == EGameplayAbilityNetExecutionPolicy::ServerInitiated)
	if (true)
	{
		// Only replicate ending if policy is predictive
		if (GetOwnerRole() == ROLE_Authority)
		{
			if (!AbilityActorInfo->IsLocallyControlled())
			{
				// Only tell the client about the end/cancel ability if we're not the local controller
				if (bWasCanceled)
				{
					ClientCancelAbility(Handle, ActivationInfo);
				}
				else
				{
					ClientEndAbility(Handle, ActivationInfo);
				}
			}
		}
		//else if(Ability->GetNetSecurityPolicy() != EGameplayAbilityNetSecurityPolicy::ServerOnlyTermination && Ability->GetNetSecurityPolicy() != EGameplayAbilityNetSecurityPolicy::ServerOnly)
		//{
		//	// This passes up the current prediction key if we have one
		//	if (bWasCanceled)
		//	{
		//		ServerCancelAbility(Handle, ActivationInfo);
		//	}
		//	else
		//	{
		//		CallServerEndAbility(Handle, ActivationInfo, ScopedPredictionKey);
		//	}
		//}
	}
}

// This is only called when ending or canceling an ability in response to a remote instruction.
void UAbilitySystemComponent::RemoteEndOrCancelAbility(FGameplayAbilitySpecHandle AbilityToEnd, FGameplayAbilityActivationInfo ActivationInfo, bool bWasCanceled)
{
	FGameplayAbilitySpec* AbilitySpec = FindAbilitySpecFromHandle(AbilityToEnd);
	if (AbilitySpec && AbilitySpec->Ability && AbilitySpec->IsActive())
	{
		// Handle non-instanced case, which cannot perform prediction key validation
		if (AbilitySpec->Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced)
		{
			// End/Cancel the ability but don't replicate it back to whoever called us
			if (bWasCanceled)
			{
				AbilitySpec->Ability->CancelAbility(AbilityToEnd, AbilityActorInfo.get(), ActivationInfo, false);
			}
			else
			{
				AbilitySpec->Ability->EndAbility(AbilityToEnd, AbilityActorInfo.get(), ActivationInfo, false, bWasCanceled);
			}
		}
		else
		{
			std::vector<UGameplayAbility*> Instances = AbilitySpec->GetAbilityInstances();
	
			for (auto Instance : Instances)
			{
                AssertMsg(Instance == nullptr, "UAbilitySystemComponent::RemoteEndOrCancelAbility null instance for {}", GetNameSafe(AbilitySpec->Ability));
	
				// Check if the ability is the same prediction key (can both by 0) and has been confirmed. If so cancel it.
				if (Instance->GetCurrentActivationInfoRef().GetActivationPredictionKey() == ActivationInfo.GetActivationPredictionKey())
				{
					// Let the ability know that the remote instance has ended, even if we aren't about to end it here.
					Instance->SetRemoteInstanceHasEnded();
	
					if (Instance->GetCurrentActivationInfoRef().bCanBeEndedByOtherInstance)
					{
						// End/Cancel the ability but don't replicate it back to whoever called us
						if (bWasCanceled)
						{
							ForceCancelAbilityDueToReplication(Instance);
						}
						else
						{
							Instance->EndAbility(Instance->CurrentSpecHandle, Instance->CurrentActorInfo, Instance->CurrentActivationInfo, false, bWasCanceled);
						}
					}
				}
			}
		}
	}
}

void UAbilitySystemComponent::ForceCancelAbilityDueToReplication(UGameplayAbility* Instance)
{
	Assert(Instance);

	// Since this was a remote cancel, we should force it through. We do not support 'server says ability was cancelled but client disagrees that it can be'.
	Instance->SetCanBeCanceled(true);
	Instance->CancelAbility(Instance->CurrentSpecHandle, Instance->CurrentActorInfo, Instance->CurrentActivationInfo, false);
}

void UAbilitySystemComponent::ServerEndAbility(FGameplayAbilitySpecHandle AbilityToEnd, FGameplayAbilityActivationInfo ActivationInfo, FPredictionKey PredictionKey)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityToEnd);

	if (Spec && Spec->Ability 
		//&& Spec->Ability->GetNetSecurityPolicy() != EGameplayAbilityNetSecurityPolicy::ServerOnlyTermination &&
		//Spec->Ability->GetNetSecurityPolicy() != EGameplayAbilityNetSecurityPolicy::ServerOnly
		)
	{
        SCOPED_CPU_TIMING(GAS_ASC, "ServerEndAbility");

		FScopedPredictionWindow ScopedPrediction(this, PredictionKey);

		RemoteEndOrCancelAbility(AbilityToEnd, ActivationInfo, false);
	}
}

//bool UAbilitySystemComponent::ServerEndAbility_Validate(FGameplayAbilitySpecHandle AbilityToEnd, FGameplayAbilityActivationInfo ActivationInfo, FPredictionKey PredictionKey)
//{
//	return true;
//}

void UAbilitySystemComponent::ClientEndAbility(FGameplayAbilitySpecHandle AbilityToEnd, FGameplayAbilityActivationInfo ActivationInfo)
{
	RemoteEndOrCancelAbility(AbilityToEnd, ActivationInfo, false);
}

void UAbilitySystemComponent::ServerCancelAbility(FGameplayAbilitySpecHandle AbilityToCancel, FGameplayAbilityActivationInfo ActivationInfo)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityToCancel);

	if (Spec && Spec->Ability 
		//&&
		//Spec->Ability->GetNetSecurityPolicy() != EGameplayAbilityNetSecurityPolicy::ServerOnlyTermination &&
		//Spec->Ability->GetNetSecurityPolicy() != EGameplayAbilityNetSecurityPolicy::ServerOnly
		)
	{
		RemoteEndOrCancelAbility(AbilityToCancel, ActivationInfo, true);
	}
}

//bool UAbilitySystemComponent::ServerCancelAbility_Validate(FGameplayAbilitySpecHandle AbilityToCancel, FGameplayAbilityActivationInfo ActivationInfo)
//{
//	return true;
//}

void UAbilitySystemComponent::ClientCancelAbility(FGameplayAbilitySpecHandle AbilityToCancel, FGameplayAbilityActivationInfo ActivationInfo)
{
	RemoteEndOrCancelAbility(AbilityToCancel, ActivationInfo, true);
}

static_assert(sizeof(SInt16) == sizeof(FPredictionKey::KeyType), "Sizeof PredictionKey::KeyType does not match RPC parameters in AbilitySystemComponent ClientActivateAbilityFailed_Implementation");


SInt32 ClientActivateAbilityFailedPrintDebugThreshhold = -1;
//static FAutoConsoleVariableRef CVarClientActivateAbilityFailedPrintDebugThreshhold(GAS_TEXT("AbilitySystem.ClientActivateAbilityFailedPrintDebugThreshhold"), ClientActivateAbilityFailedPrintDebugThreshhold, GAS_TEXT(""), ECVF_Default );

float ClientActivateAbilityFailedPrintDebugThreshholdTime = 3.f;
//static FAutoConsoleVariableRef CVarClientActivateAbilityFailedPrintDebugThreshholdTime(GAS_TEXT("AbilitySystem.ClientActivateAbilityFailedPrintDebugThreshholdTime"), ClientActivateAbilityFailedPrintDebugThreshholdTime, GAS_TEXT(""), ECVF_Default );

void UAbilitySystemComponent::ClientActivateAbilityFailed(FGameplayAbilitySpecHandle Handle, SInt16 PredictionKey)
{
	// Tell anything else listening that this was rejected
	if (PredictionKey > 0)
	{
		FPredictionKeyDelegates::BroadcastRejectedDelegate(PredictionKey);
	}

	// Find the actual UGameplayAbility		
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (Spec == nullptr)
	{
		LOG_INFO("ClientActivateAbilityFailed_Implementation. PredictionKey: {} Ability: Could not find!", PredictionKey);
		return;
	}

	LOG_INFO("ClientActivateAbilityFailed_Implementation. PredictionKey :{} Ability: {}", PredictionKey, GetNameSafe(Spec->Ability));
	
	if (ClientActivateAbilityFailedPrintDebugThreshhold > 0)
	{
		if ((ClientActivateAbilityFailedStartTime <= 0.f) || ((GetWorld()->GetTimeSeconds() - ClientActivateAbilityFailedStartTime) > ClientActivateAbilityFailedPrintDebugThreshholdTime))
		{
			ClientActivateAbilityFailedStartTime = GetWorld()->GetTimeSeconds();
			ClientActivateAbilityFailedCountRecent = 0;
		}
		
		
		if (++ClientActivateAbilityFailedCountRecent > ClientActivateAbilityFailedPrintDebugThreshhold)
		{
			LOG_INFO("Threshold hit! Printing debug information");
			PrintDebug();
			ClientActivateAbilityFailedCountRecent = 0;
			ClientActivateAbilityFailedStartTime = 0.f;
		}
	}


PRAGMA_DISABLE_DEPRECATION_WARNINGS
	// The ability should be either confirmed or rejected by the time we get here
	if (Spec->ActivationInfo.GetActivationPredictionKey().Current == PredictionKey)
	{
		Spec->ActivationInfo.SetActivationRejected();
	}
PRAGMA_ENABLE_DEPRECATION_WARNINGS

	std::vector<UGameplayAbility*> Instances = Spec->GetAbilityInstances();
	for (UGameplayAbility* Ability : Instances)
    {
		if (Ability->CurrentActivationInfo.GetActivationPredictionKey().Current == PredictionKey)
		{
			Ability->CurrentActivationInfo.SetActivationRejected();
			Ability->K2_EndAbility();
		}
	}
}

void UAbilitySystemComponent::OnClientActivateAbilityCaughtUp(FGameplayAbilitySpecHandle Handle, FPredictionKey::KeyType PredictionKey)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (Spec && Spec->IsActive())
	{
PRAGMA_DISABLE_DEPRECATION_WARNINGS
		const FGameplayAbilityActivationInfo* ActivationInfo = (Spec->ActivationInfo.GetActivationPredictionKey().Current == PredictionKey) ? &Spec->ActivationInfo : nullptr;
PRAGMA_ENABLE_DEPRECATION_WARNINGS

		for (const UGameplayAbility* Ability : Spec->GetAbilityInstances())
        {
			if (Ability->CurrentActivationInfo.GetActivationPredictionKey().Current == PredictionKey)
			{
				ActivationInfo = &Ability->CurrentActivationInfo;
				break;
			}
		}

		// The ability should be either confirmed or rejected by the time we get here
		if (ActivationInfo && ActivationInfo->ActivationMode == EGameplayAbilityActivationMode::Predicting)
		{
			// It is possible to have this happen under bad network conditions. (Reliable Confirm/Reject RPC is lost, but separate property bunch makes it through before the reliable resend happens)
			LOG_INFO("{}. Ability {} caught up to PredictionKey {} but instance is still active and in predicting state.", __func__, GetNameSafe(Spec->Ability), PredictionKey);
		}
	}
}

void UAbilitySystemComponent::ClientActivateAbilitySucceed(FGameplayAbilitySpecHandle Handle, FPredictionKey PredictionKey)
{
	ClientActivateAbilitySucceedWithEventData(Handle, PredictionKey, FGameplayEventData());
}

void UAbilitySystemComponent::ClientActivateAbilitySucceedWithEventData(FGameplayAbilitySpecHandle Handle, FPredictionKey PredictionKey, FGameplayEventData TriggerEventData)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (!Spec)
	{
		// Can happen if the client gets told to activate an ability the same frame that abilities are added on the server
		FPendingAbilityInfo AbilityInfo;
		AbilityInfo.PredictionKey = PredictionKey;
		AbilityInfo.Handle = Handle;
		AbilityInfo.TriggerEventData = TriggerEventData;
		AbilityInfo.bPartiallyActivated = true;

		// This won't add it if we're currently being called from the pending list
		ArrayAddUnique(PendingServerActivatedAbilities, AbilityInfo);
		return;
	}

	UGameplayAbility* AbilityToActivate = Spec->Ability.get();

	Assert(AbilityToActivate);
	Assert(AbilityActorInfo);

	LOG_INFO("{}: Server Confirmed [{}] {}. PredictionKey: {}", GetNameSafe(GetOwner()), Handle.ToString(), GetNameSafe(AbilityToActivate), PredictionKey.ToString());
	LOG_INFO("Server Confirmed [{}] {}. PredictionKey: {}", Handle.ToString(), GetNameSafe(AbilityToActivate), PredictionKey.ToString());

	// Fixme: We need a better way to link up/reconcile predictive replicated abilities. It would be ideal if we could predictively spawn an
	// ability and then replace/link it with the server spawned one once the server has confirmed it.

    // TODO after Ability.h
    Assert(false);
    PRAGMA_DISABLE_DEPRECATION_WARNINGS
//	const bool bNonInstanced = AbilityToActivate->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced;
//	FGameplayAbilityActivationInfo NewActivationInfo{ AbilityActorInfo->OwnerActor.Get() };
//	FGameplayAbilityActivationInfo& ActivationInfo = bNonInstanced ? Spec->ActivationInfo : NewActivationInfo;
//PRAGMA_ENABLE_DEPRECATION_WARNINGS
//
//	// Confirm and allow the remote ending of the ability
//	ActivationInfo.SetActivationConfirmed();
//
//	const bool bLocallyPredicted = AbilityToActivate->NetExecutionPolicy == EGameplayAbilityNetExecutionPolicy::LocalPredicted;
//	if (bLocallyPredicted)
//	{
//		if (bNonInstanced)
//		{
//			// AbilityToActivate->ConfirmActivateSucceed(); // This doesn't do anything for non instanced
//		}
//		else
//		{
//			// Find the one we predictively spawned, tell them we are confirmed
//			bool found = false;
//			std::vector<UGameplayAbility*> Instances = Spec->GetAbilityInstances();
//			for (UGameplayAbility* LocalAbility : Instances)
//			{
//				if (LocalAbility != nullptr && LocalAbility->GetCurrentActivationInfo().GetActivationPredictionKey() == PredictionKey)
//				{
//					LocalAbility->ConfirmActivateSucceed();
//					found = true;
//					break;
//				}
//			}
//
//			if (!found)
//			{
//				ABILITY_LOG(Verbose, GAS_TEXT("Ability {} was confirmed by server but no longer exists on client (replication key: {})"), *AbilityToActivate->GetName(), *PredictionKey.ToString());
//			}
//		}
//	}
//	else
//	{
//
//		// We haven't already executed this ability at all, so kick it off.
//		if (PredictionKey.bIsServerInitiated)
//		{
//			// We have an active server key, set our key equal to it
//			ActivationInfo.ServerSetActivationPredictionKey(PredictionKey);
//		}
//		
//		if (AbilityToActivate->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution)
//		{
//			// Need to instantiate this in order to execute
//			UGameplayAbility* InstancedAbility = CreateNewInstanceOfAbility(*Spec, AbilityToActivate);
//			InstancedAbility->CallActivateAbility(Handle, AbilityActorInfo.Get(), ActivationInfo, nullptr, TriggerEventData.EventTag.IsValid() ?  &TriggerEventData : nullptr);
//		}
//		else if (AbilityToActivate->GetInstancingPolicy() != EGameplayAbilityInstancingPolicy::NonInstanced)
//		{
//			UGameplayAbility* InstancedAbility = Spec->GetPrimaryInstance();
//
//			if (!InstancedAbility)
//			{
//				LOG_WARN("Ability {} cannot be activated on the client because it's missing a primary instance!"), *AbilityToActivate->GetName());
//				return;
//			}
//			InstancedAbility->CallActivateAbility(Handle, AbilityActorInfo.Get(), ActivationInfo, nullptr, TriggerEventData.EventTag.IsValid() ? &TriggerEventData : nullptr);
//		}
//		else
//		{
//			AbilityToActivate->CallActivateAbility(Handle, AbilityActorInfo.Get(), ActivationInfo, nullptr, TriggerEventData.EventTag.IsValid() ? &TriggerEventData : nullptr);
//		}
//	}
}

bool UAbilitySystemComponent::HasActivatableTriggeredAbility(FGameplayTag Tag)
{
	std::vector<FGameplayAbilitySpec> Specs = GetActivatableAbilities();
	const FGameplayAbilityActorInfo* ActorInfo = AbilityActorInfo.get();
	for (const FGameplayAbilitySpec& Spec : Specs)
	{
		if (Spec.Ability == nullptr)
		{
			continue;
		}

		std::vector<FAbilityTriggerData>& Triggers = Spec.Ability->AbilityTriggers;
		for (const FAbilityTriggerData& Data : Triggers)
		{
			if (Data.TriggerTag == Tag && Spec.Ability->CanActivateAbility(Spec.Handle, ActorInfo))
			{
				return true;
			}
		}
	}
	return false;
}

bool UAbilitySystemComponent::TriggerAbilityFromGameplayEvent(FGameplayAbilitySpecHandle Handle, FGameplayAbilityActorInfo* ActorInfo, FGameplayTag EventTag, const FGameplayEventData* Payload, UAbilitySystemComponent& Component)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(Handle);
	if (!Spec)//, GAS_TEXT("Failed to find gameplay ability spec {}"), *EventTag.ToString()))
	{
		return false;
	}

	const UGameplayAbility* InstancedAbility = Spec->GetPrimaryInstance();
	const UGameplayAbility* Ability = InstancedAbility ? InstancedAbility : Spec->Ability.get();
	if (!Ability)
	{
		return false;
	}

	if (!Payload)
	{
		return false;
	}

	if (!HasNetworkAuthorityToActivateTriggeredAbility(*Spec))
	{
		// The server or client will handle activating the trigger
		return false;
	}

	// Make a temp copy of the payload, and copy the event tag into it
	FGameplayEventData TempEventData = *Payload;
	TempEventData.EventTag = EventTag;

	// Run on the non-instanced ability
	return InternalTryActivateAbility(Handle, ScopedPredictionKey, nullptr, nullptr, &TempEventData);
}

bool UAbilitySystemComponent::GetUserAbilityActivationInhibited() const
{
	return UserAbilityActivationInhibited;
}

void UAbilitySystemComponent::SetUserAbilityActivationInhibited(bool NewInhibit)
{
	if(AbilityActorInfo->IsLocallyControlled())
	{
		if (NewInhibit && UserAbilityActivationInhibited)
		{
			// This could cause problems if two sources try to inhibit ability activation, it is not clear when the ability should be uninhibited
			LOG_WARN("Call to SetUserAbilityActivationInhibited(true) when UserAbilityActivationInhibited was already true");
		}

		UserAbilityActivationInhibited = NewInhibit;
	}
}

void UAbilitySystemComponent::NotifyAbilityCommit(UGameplayAbility* Ability)
{
	AbilityCommittedCallbacks.Broadcast(Ability);
}

void UAbilitySystemComponent::NotifyAbilityActivated(FGameplayAbilitySpecHandle Handle, UGameplayAbility* Ability)
{
	AbilityActivatedCallbacks.Broadcast(Ability);
}

void UAbilitySystemComponent::NotifyAbilityFailed(const FGameplayAbilitySpecHandle Handle, UGameplayAbility* Ability, const FGameplayTagContainer& FailureReason)
{
	AbilityFailedCallbacks.Broadcast(Ability, FailureReason);
}

int32 UAbilitySystemComponent::HandleGameplayEvent(FGameplayTag EventTag, const FGameplayEventData* Payload)
{
	int32 TriggeredCount = 0;
	FGameplayTag CurrentTag = EventTag;
	ABILITYLIST_SCOPE_LOCK();
	while (CurrentTag.IsValid())
	{
		if (GameplayEventTriggeredAbilities.contains(CurrentTag))
		{
			std::vector<FGameplayAbilitySpecHandle> TriggeredAbilityHandles = GameplayEventTriggeredAbilities[CurrentTag];

			for (const FGameplayAbilitySpecHandle& AbilityHandle : TriggeredAbilityHandles)
			{
				if (TriggerAbilityFromGameplayEvent(AbilityHandle, AbilityActorInfo.get(), EventTag, Payload, *this))
				{
					TriggeredCount++;
				}
			}
		}

		CurrentTag = CurrentTag.RequestDirectParent();
	}

	if (FGameplayEventMulticastDelegate* Delegate = MapFind(GenericGameplayEventCallbacks, EventTag))
	{
		// Make a copy before broadcasting to prevent memory stomping
		FGameplayEventMulticastDelegate DelegateCopy = *Delegate;
		DelegateCopy.Broadcast(Payload);
	}

	// Make a copy in case it changes due to callbacks
	std::vector<std::pair<FGameplayTagContainer, FGameplayEventTagMulticastDelegate>> LocalGameplayEventTagContainerDelegates = GameplayEventTagContainerDelegates;
    for (std::pair<FGameplayTagContainer, FGameplayEventTagMulticastDelegate>& SearchPair : LocalGameplayEventTagContainerDelegates)
	{
        if (SearchPair.first.IsEmpty() || EventTag.MatchesAny(SearchPair.first))
		{
			SearchPair.second.Broadcast(EventTag, Payload);
		}
	}

	return TriggeredCount;
}

FDelegateHandle UAbilitySystemComponent::AddGameplayEventTagContainerDelegate(const FGameplayTagContainer& TagFilter, const FGameplayEventTagMulticastDelegate::FDelegate& Delegate)
{
	std::pair<FGameplayTagContainer, FGameplayEventTagMulticastDelegate>* FoundPair = nullptr;

	for (std::pair<FGameplayTagContainer, FGameplayEventTagMulticastDelegate>& SearchPair : GameplayEventTagContainerDelegates)
	{
		if (TagFilter == SearchPair.first)
		{
			FoundPair = &SearchPair;
			break;
		}
	}

	if (!FoundPair)
	{
		FoundPair = &(GameplayEventTagContainerDelegates.emplace_back(std::pair(TagFilter, FGameplayEventTagMulticastDelegate())));
	}

	return FoundPair->second.Add(Delegate);
}

void UAbilitySystemComponent::RemoveGameplayEventTagContainerDelegate(const FGameplayTagContainer& TagFilter, FDelegateHandle DelegateHandle)
{
	// Look for and remove delegate, remove from array if no more delegates are bound
	for (int32 Index = 0; Index < GameplayEventTagContainerDelegates.size(); Index++)
	{
		std::pair<FGameplayTagContainer, FGameplayEventTagMulticastDelegate>& SearchPair = GameplayEventTagContainerDelegates[Index];
		if (TagFilter == SearchPair.first)
		{
			SearchPair.second.Remove(DelegateHandle);
			if (!SearchPair.second.IsBound())
			{
				ArrayRemoveAt(GameplayEventTagContainerDelegates, Index);
			}
			break;
		}
	}
}

void UAbilitySystemComponent::MonitoredTagChanged(const FGameplayTag Tag, int32 NewCount)
{
	ABILITYLIST_SCOPE_LOCK();

	int32 TriggeredCount = 0;
	if (OwnedTagTriggeredAbilities.contains(Tag))
	{
		std::vector<FGameplayAbilitySpecHandle> TriggeredAbilityHandles = OwnedTagTriggeredAbilities[Tag];

		for (auto AbilityHandle : TriggeredAbilityHandles)
		{
			FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);

			if (!Spec || !HasNetworkAuthorityToActivateTriggeredAbility(*Spec))
			{
				continue;
			}

			if (Spec->Ability)
			{
				// TODO after Ability.h
                Assert(false);
				std::vector<FAbilityTriggerData> AbilityTriggers = Spec->Ability->AbilityTriggers;
				for (const FAbilityTriggerData& TriggerData : AbilityTriggers)
				{
					FGameplayTag EventTag = TriggerData.TriggerTag;
				
					if (EventTag == Tag)
					{
						if (NewCount > 0)
						{
							// Populate event data so this will use the same blueprint node to activate as gameplay triggers
							FGameplayEventData EventData;
							EventData.EventMagnitude = NewCount;
							EventData.EventTag = EventTag;
							EventData.Instigator = std::dynamic_pointer_cast<GameObject>(GetOwnerActor()->shared_from_this());
							EventData.Target = EventData.Instigator;
							// Try to activate it
							InternalTryActivateAbility(Spec->Handle, FPredictionKey(), nullptr, nullptr, &EventData);
				
							// TODO: Check client/server type
						}
						else if (NewCount == 0 && TriggerData.TriggerSource == EGameplayAbilityTriggerSource::OwnedTagPresent)
						{
							// Try to cancel, but only if the type is right
							CancelAbilitySpec(*Spec, nullptr);
						}
					}
				}
			}
		}
	}
}

bool UAbilitySystemComponent::HasNetworkAuthorityToActivateTriggeredAbility(const FGameplayAbilitySpec &Spec) const
{
	bool bIsAuthority = IsOwnerActorAuthoritative();
	bool bIsLocal = AbilityActorInfo->IsLocallyControlled();

    // TODO after Ability.h
    Assert(false);
    return bIsLocal;
	//switch (Spec.Ability->GetNetExecutionPolicy())
	//{
	//case EGameplayAbilityNetExecutionPolicy::LocalOnly:
	//case EGameplayAbilityNetExecutionPolicy::LocalPredicted:
	//	return bIsLocal;
	//case EGameplayAbilityNetExecutionPolicy::ServerOnly:
	//case EGameplayAbilityNetExecutionPolicy::ServerInitiated:
	//	return bIsAuthority;
	//}

	return false;
}

void UAbilitySystemComponent::BindToInputComponent(InputComponent* InputComponent)
{
	static const FName ConfirmBindName(GAS_TEXT("AbilityConfirm"));
	static const FName CancelBindName(GAS_TEXT("AbilityCancel"));


	//TODO after Input
	Assert(false);

	// Pressed event
	//{
    //    InputActionBinding AB(ConfirmBindName.GetCString(), cross::TriggerEvent::Triggered);
	//	AB.ActionDelegate.GetDelegateForManualSet().BindObjectBase(this, &UAbilitySystemComponent::LocalInputConfirm);
	//	InputComponent->AddActionBinding(AB);
	//}
	//
	//// 
	//{
	//	FInputActionBinding AB(CancelBindName, IE_Pressed);
	//	AB.ActionDelegate.GetDelegateForManualSet().BindObjectBase(this, &UAbilitySystemComponent::LocalInputCancel);
	//	InputComponent->AddActionBinding(AB);
	//}
}

void UAbilitySystemComponent::BindAbilityActivationToInputComponent(InputComponent* InputComponent, FGameplayAbilityInputBinds BindInfo)
{
    // TODO after Input
    Assert(false);
	//UEnum* EnumBinds = BindInfo.GetBindEnum();
	//
	//SetBlockAbilityBindingsArray(BindInfo);
	//
	//for(int32 idx=0; idx < EnumBinds->NumEnums(); ++idx)
	//{
	//	const FString FullStr = EnumBinds->GetNameStringByIndex(idx);
	//	
	//	// Pressed event
	//	{
	//		FInputActionBinding AB(FName(*FullStr), IE_Pressed);
	//		AB.ActionDelegate.GetDelegateForManualSet().BindObjectBase(this, &UAbilitySystemComponent::AbilityLocalInputPressed, idx);
	//		InputComponent->AddActionBinding(AB);
	//	}
	//
	//	// Released event
	//	{
	//		FInputActionBinding AB(FName(*FullStr), IE_Released);
	//		AB.ActionDelegate.GetDelegateForManualSet().BindObjectBase(this, &UAbilitySystemComponent::AbilityLocalInputReleased, idx);
	//		InputComponent->AddActionBinding(AB);
	//	}
	//}
	//
	//// Bind Confirm/Cancel. Note: these have to come last!
	//if (BindInfo.ConfirmTargetCommand.IsEmpty() == false)
	//{
	//	FInputActionBinding AB(FName(*BindInfo.ConfirmTargetCommand), IE_Pressed);
	//	AB.ActionDelegate.GetDelegateForManualSet().BindObjectBase(this, &UAbilitySystemComponent::LocalInputConfirm);
	//	InputComponent->AddActionBinding(AB);
	//}
	//
	//if (BindInfo.CancelTargetCommand.IsEmpty() == false)
	//{
	//	FInputActionBinding AB(FName(*BindInfo.CancelTargetCommand), IE_Pressed);
	//	AB.ActionDelegate.GetDelegateForManualSet().BindObjectBase(this, &UAbilitySystemComponent::LocalInputCancel);
	//	InputComponent->AddActionBinding(AB);
	//}
	//
	//if (BindInfo.CancelTargetInputID >= 0)
	//{
	//	GenericCancelInputID = BindInfo.CancelTargetInputID;
	//}
	//if (BindInfo.ConfirmTargetInputID >= 0)
	//{
	//	GenericConfirmInputID = BindInfo.ConfirmTargetInputID;
	//}
}

void UAbilitySystemComponent::SetBlockAbilityBindingsArray(FGameplayAbilityInputBinds BindInfo)
{
	auto* EnumBinds = BindInfo.GetBindEnum();
	GetBlockedAbilityBindings_Mutable().resize(EnumBinds->GetCount());
}

void UAbilitySystemComponent::AbilityLocalInputPressed(int32 InputID)
{
	// Consume the input if this InputID is overloaded with GenericConfirm/Cancel and the GenericConfim/Cancel callback is bound
	if (IsGenericConfirmInputBound(InputID))
	{
		LocalInputConfirm();
		return;
	}

	if (IsGenericCancelInputBound(InputID))
	{
		LocalInputCancel();
		return;
	}

	// ---------------------------------------------------------

	ABILITYLIST_SCOPE_LOCK();
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.InputID == InputID)
		{
			if (Spec.Ability)
			{
				Spec.InputPressed = true;
				if (Spec.IsActive())
                {
                    if (Spec.Ability->bReplicateInputDirectly && IsOwnerActorAuthoritative() == false)
					{
						ServerSetInputPressed(Spec.Handle);
					}

					AbilitySpecInputPressed(Spec);

PRAGMA_DISABLE_DEPRECATION_WARNINGS
                    // Fixing this up to use the instance activation, but this function should be deprecated as it cannot work with InstancedPerExecution
                    AssertMsg(Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution,
                              "{}: {} is InstancedPerExecution. This is unreliable for Input as you may only interact with the latest spawned Instance",
                              "AbilityLocalInputPressed",
                              GetNameSafe(Spec.Ability));
					std::vector<UGameplayAbility*> Instances = Spec.GetAbilityInstances();
					const FGameplayAbilityActivationInfo& ActivationInfo = Instances.empty() ? Spec.ActivationInfo : Instances.back()->GetCurrentActivationInfoRef();
PRAGMA_ENABLE_DEPRECATION_WARNINGS
					// Invoke the InputPressed event. This is not replicated here. If someone is listening, they may replicate the InputPressed event to the server.
					InvokeReplicatedEvent(EAbilityGenericReplicatedEvent::InputPressed, Spec.Handle, ActivationInfo.GetActivationPredictionKey());					
				}
				else
				{
					// Ability is not active, so try to activate it
					TryActivateAbility(Spec.Handle);
				}
			}
		}
	}
}

void UAbilitySystemComponent::AbilityLocalInputReleased(int32 InputID)
{
	ABILITYLIST_SCOPE_LOCK();
	for (FGameplayAbilitySpec& Spec : ActivatableAbilities.Items)
	{
		if (Spec.InputID == InputID)
		{
			Spec.InputPressed = false;
			if (Spec.Ability && Spec.IsActive())
            {
                if (Spec.Ability->bReplicateInputDirectly && IsOwnerActorAuthoritative() == false)
				{
					ServerSetInputReleased(Spec.Handle);
				}

				AbilitySpecInputReleased(Spec);

PRAGMA_DISABLE_DEPRECATION_WARNINGS
                // Fixing this up to use the instance activation, but this function should be deprecated as it cannot work with InstancedPerExecution
                AssertMsg(Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::InstancedPerExecution,
                          "{}: {} is InstancedPerExecution. This is unreliable for Input as you may only interact with the latest spawned Instance",
                          "AbilityLocalInputReleased",
                          GetNameSafe(Spec.Ability));
				std::vector<UGameplayAbility*> Instances = Spec.GetAbilityInstances();
				const FGameplayAbilityActivationInfo& ActivationInfo = Instances.empty() ? Spec.ActivationInfo : Instances.back()->GetCurrentActivationInfoRef();
PRAGMA_ENABLE_DEPRECATION_WARNINGS
				InvokeReplicatedEvent(EAbilityGenericReplicatedEvent::InputReleased, Spec.Handle, ActivationInfo.GetActivationPredictionKey());
			}
		}
	}
}

void UAbilitySystemComponent::PressInputID(int32 InputID)
{
	AbilityLocalInputPressed(InputID);
}

void UAbilitySystemComponent::ReleaseInputID(int32 InputID)
{
	AbilityLocalInputReleased(InputID);
}

void UAbilitySystemComponent::ServerSetInputPressed(FGameplayAbilitySpecHandle AbilityHandle)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
	if (Spec)
	{
		AbilitySpecInputPressed(*Spec);
	}

}

void UAbilitySystemComponent::ServerSetInputReleased(FGameplayAbilitySpecHandle AbilityHandle)
{
	FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
	if (Spec)
	{
		AbilitySpecInputReleased(*Spec);
	}
}
//
//bool UAbilitySystemComponent::ServerSetInputPressed_Validate(FGameplayAbilitySpecHandle AbilityHandle)
//{
//	return true;
//}
//
//bool UAbilitySystemComponent::ServerSetInputReleased_Validate(FGameplayAbilitySpecHandle AbilityHandle)
//{
//	return true;
//}

void UAbilitySystemComponent::AbilitySpecInputPressed(FGameplayAbilitySpec& Spec)
{
	Spec.InputPressed = true;
	if (Spec.IsActive())
    {
        // TODO after Ability.h
        Assert(false);

//		// The ability is active, so just pipe the input event to it
//		if (Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced)
//		{
//PRAGMA_DISABLE_DEPRECATION_WARNINGS
//			Spec.Ability->InputPressed(Spec.Handle, AbilityActorInfo.Get(), Spec.ActivationInfo);
//PRAGMA_ENABLE_DEPRECATION_WARNINGS
//		}
//		else
//		{
//			std::vector<UGameplayAbility*> Instances = Spec.GetAbilityInstances();
//			for (UGameplayAbility* Instance : Instances)
//			{
//				Instance->InputPressed(Spec.Handle, AbilityActorInfo.Get(), Instance->CurrentActivationInfo);
//			}
//		}
	}
}

void UAbilitySystemComponent::AbilitySpecInputReleased(FGameplayAbilitySpec& Spec)
{
	Spec.InputPressed = false;
	if (Spec.IsActive())
    {
        // TODO after Ability.h
        Assert(false);
		// The ability is active, so just pipe the input event to it
//		if (Spec.Ability->GetInstancingPolicy() == EGameplayAbilityInstancingPolicy::NonInstanced)
//		{
//PRAGMA_DISABLE_DEPRECATION_WARNINGS
//			Spec.Ability->InputReleased(Spec.Handle, AbilityActorInfo.Get(), Spec.ActivationInfo);
//PRAGMA_ENABLE_DEPRECATION_WARNINGS
//		}
//		else
//		{
//			std::vector<UGameplayAbility*> Instances = Spec.GetAbilityInstances();
//			for (UGameplayAbility* Instance : Instances)
//			{
//				Instance->InputReleased(Spec.Handle, AbilityActorInfo.Get(), Instance->CurrentActivationInfo);
//			}
//		}
	}
}

void UAbilitySystemComponent::LocalInputConfirm()
{
	FAbilityConfirmOrCancel Temp = GenericLocalConfirmCallbacks;
	GenericLocalConfirmCallbacks.RemoveAll();
	Temp.Broadcast();
}

void UAbilitySystemComponent::LocalInputCancel()
{	
	FAbilityConfirmOrCancel Temp = GenericLocalCancelCallbacks;
    GenericLocalCancelCallbacks.RemoveAll();
	Temp.Broadcast();
}

void UAbilitySystemComponent::InputConfirm()
{
	LocalInputConfirm();
}

void UAbilitySystemComponent::InputCancel()
{
	LocalInputCancel();
}

void UAbilitySystemComponent::TargetConfirm()
{
	// Callbacks may modify the spawned target actor array so iterate over a copy instead
    std::vector < std::shared_ptr<AGameplayAbilityTargetActor>> LocalTargetActors = SpawnedTargetActors;
	SpawnedTargetActors.clear();

	for (auto TargetActor : LocalTargetActors)
	{
		if (TargetActor)
        {
            // TODO after Ability.h
            Assert(false);
			//if (TargetActor->IsConfirmTargetingAllowed())
			//{
			//	//TODO: There might not be any cases where this bool is false
			//	if (!TargetActor->bDestroyOnConfirmation)
			//	{
			//		SpawnedTargetActors.Add(TargetActor);
			//	}
			//	TargetActor->ConfirmTargeting();
			//}
			//else
			//{
			//	SpawnedTargetActors.Add(TargetActor);
			//}
		}
	}
}

void UAbilitySystemComponent::TargetCancel()
{
	// Callbacks may modify the spawned target actor array so iterate over a copy instead
    std::vector<std::shared_ptr<AGameplayAbilityTargetActor>> LocalTargetActors = SpawnedTargetActors;
    SpawnedTargetActors.clear();
	for (auto TargetActor : LocalTargetActors)
	{
		if (TargetActor)
        {
            // TODO after Ability.h
            Assert(false);
			//TargetActor->CancelTargeting();
		}
	}
}

// --------------------------------------------------------------------------

#if ENABLE_VISUAL_LOG
void UAbilitySystemComponent::ClearDebugInstantEffects()
{
	ActiveGameplayEffects.DebugExecutedGameplayEffects.Empty();
}
#endif // ENABLE_VISUAL_LOG

// ---------------------------------------------------------------------------

float UAbilitySystemComponent::PlayMontage(UGameplayAbility* InAnimatingAbility, FGameplayAbilityActivationInfo ActivationInfo, UAnimMontage* NewAnimMontage, float InPlayRate, FName StartSectionName, float StartTimeSeconds)
{
	// TODO PlayMontage
	Assert(false);
	float Duration = -1.f;

	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance && NewAnimMontage)
	//{
//		Duration = AnimInstance->Montage_Play(NewAnimMontage, InPlayRate, EMontagePlayReturnType::MontageLength, StartTimeSeconds);
//		if (Duration > 0.f)
//		{
//			if (const UGameplayAbility* RawAnimatingAbility = LocalAnimMontageInfo.AnimatingAbility.Get())
//			{
//				if (RawAnimatingAbility != InAnimatingAbility)
//				{
//					// The ability that was previously animating will have already gotten the 'interrupted' callback.
//					// It may be a good idea to make this a global policy and 'cancel' the ability.
//					// 
//					// For now, we expect it to end itself when this happens.
//				}
//			}
	//
//			UAnimSequenceBase* Animation = NewAnimMontage->IsDynamicMontage() ? NewAnimMontage->GetFirstAnimReference() : NewAnimMontage;
	//
//			if (NewAnimMontage->HasRootMotion() && AnimInstance->GetOwningActor())
//			{
//				UE_LOG(LogRootMotion, Log, GAS_TEXT("UAbilitySystemComponent::PlayMontage {}, Role: {}")
//					, *GetNameSafe(Animation)
//					, *UEnum::GetValueAsString(GAS_TEXT("Engine.ENetRole"), AnimInstance->GetOwningActor()->GetLocalRole())
//					);
//			}
	//
//			LocalAnimMontageInfo.AnimMontage = NewAnimMontage;
//			LocalAnimMontageInfo.AnimatingAbility = InAnimatingAbility;
//			LocalAnimMontageInfo.PlayInstanceId = (LocalAnimMontageInfo.PlayInstanceId < UINT8_MAX ? LocalAnimMontageInfo.PlayInstanceId + 1 : 0);
//			
//			if (InAnimatingAbility)
//			{
//				InAnimatingAbility->SetCurrentMontage(NewAnimMontage);
//			}
//			
//			// Start at a given Section.
//			if (StartSectionName != NAME_None)
//			{
//				AnimInstance->Montage_JumpToSection(StartSectionName, NewAnimMontage);
//			}
	//
//			// Replicate for non-owners and for replay recordings
//			// The data we set from GetRepAnimMontageInfo_Mutable() is used both by the server to replicate to clients and by clients to record replays.
//			// We need to set this data for recording clients because there exists network configurations where an abilities montage data will not replicate to some clients (for example: if the client is an autonomous proxy.)
//			if (ShouldRecordMontageReplication())
//			{
//				FGameplayAbilityRepAnimMontage& MutableRepAnimMontageInfo = GetRepAnimMontageInfo_Mutable();
	//
//				// Those are static parameters, they are only set when the montage is played. They are not changed after that.
//				MutableRepAnimMontageInfo.Animation = Animation;
//				MutableRepAnimMontageInfo.PlayInstanceId = (MutableRepAnimMontageInfo.PlayInstanceId < UINT8_MAX ? MutableRepAnimMontageInfo.PlayInstanceId + 1 : 0);
	//
//				MutableRepAnimMontageInfo.SectionIdToPlay = 0;
//				if (MutableRepAnimMontageInfo.Animation && StartSectionName != NAME_None)
//				{
//					// we add one so INDEX_NONE can be used in the on rep
//					MutableRepAnimMontageInfo.SectionIdToPlay = NewAnimMontage->GetSectionIndex(StartSectionName) + 1;
//				}
	//
//				if (NewAnimMontage->IsDynamicMontage())
//				{
//					Assert(!NewAnimMontage->SlotAnimTracks.IsEmpty());
//					MutableRepAnimMontageInfo.SlotName = NewAnimMontage->SlotAnimTracks[0].SlotName;
//					MutableRepAnimMontageInfo.BlendOutTime = NewAnimMontage->GetDefaultBlendInTime();
//				}
	//
//				// Update parameters that change during Montage life time.
//				AnimMontage_UpdateReplicatedData();
//			}
	//
//			// Replicate to non-owners
//			if (IsOwnerActorAuthoritative())
//			{
//				// Force net update on our avatar actor.
//				if (AbilityActorInfo->AvatarActor != nullptr)
//				{
//					AbilityActorInfo->AvatarActor->ForceNetUpdate();
//				}
//			}
//			else
//			{
//				// If this prediction key is rejected, we need to end the preview
//				FPredictionKey PredictionKey = GetPredictionKeyForNewAction();
//				if (PredictionKey.IsValidKey())
//				{
//					PredictionKey.NewRejectedDelegate().BindObjectBase(this, &UAbilitySystemComponent::OnPredictiveMontageRejected, NewAnimMontage);
//				}
//			}
//		}
	//}

	return Duration;
}

float UAbilitySystemComponent::PlayMontageSimulated(UAnimMontage* NewAnimMontage, float InPlayRate, FName StartSectionName)
{
    // TODO PlayMontage
    Assert(false);
	float Duration = -1.f;
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance && NewAnimMontage)
	//{
	//	Duration = AnimInstance->Montage_Play(NewAnimMontage, InPlayRate);
	//	if (Duration > 0.f)
	//	{
	//		LocalAnimMontageInfo.AnimMontage = NewAnimMontage;
	//	}
	//}

	return Duration;
}

void UAbilitySystemComponent::AnimMontage_UpdateReplicatedData()
{
	Assert(ShouldRecordMontageReplication());

	AnimMontage_UpdateReplicatedData(GetRepAnimMontageInfo_Mutable());
}

void UAbilitySystemComponent::AnimMontage_UpdateReplicatedData(FGameplayAbilityRepAnimMontage& OutRepAnimMontageInfo)
{
    // TODO PlayMontage
    Assert(false);
	//const UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance && LocalAnimMontageInfo.AnimMontage)
	//{
	//	if (LocalAnimMontageInfo.AnimMontage->IsDynamicMontage())
	//	{
	//		OutRepAnimMontageInfo.Animation = LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference();
	//		OutRepAnimMontageInfo.BlendOutTime = LocalAnimMontageInfo.AnimMontage->GetDefaultBlendOutTime();
	//	}
	//	else
	//	{
	//		OutRepAnimMontageInfo.Animation = LocalAnimMontageInfo.AnimMontage;
	//		OutRepAnimMontageInfo.BlendOutTime = 0.0f;
	//	}
	//
	//	// Compressed Flags
	//	const bool bIsStopped = AnimInstance->Montage_GetIsStopped(LocalAnimMontageInfo.AnimMontage);
	//
	//	if (!bIsStopped)
	//	{
	//		OutRepAnimMontageInfo.PlayRate = AnimInstance->Montage_GetPlayRate(LocalAnimMontageInfo.AnimMontage);
	//		OutRepAnimMontageInfo.Position = AnimInstance->Montage_GetPosition(LocalAnimMontageInfo.AnimMontage);
	//		OutRepAnimMontageInfo.BlendTime = AnimInstance->Montage_GetBlendTime(LocalAnimMontageInfo.AnimMontage);
	//	}
	//
	//	if (OutRepAnimMontageInfo.IsStopped != bIsStopped)
	//	{
	//		// Set this prior to calling UpdateShouldTick, so we start ticking if we are playing a Montage
	//		OutRepAnimMontageInfo.IsStopped = bIsStopped;
	//
	//		if (bIsStopped)
	//		{
	//			// Use AnyThread because GetValueOnGameThread will fail Assert() when doing replays
	//			constexpr bool bForceGameThreadValue = true;
	//			if (CVarGasFixClientSideMontageBlendOutTime.GetValueOnAnyThread(bForceGameThreadValue))
	//			{
	//				// Replicate blend out time. This requires a manual search since Montage_GetBlendTime will fail
	//				// in GetActiveInstanceForMontage for Montages that are stopped.
	//				for (const FAnimMontageInstance* MontageInstance : AnimInstance->MontageInstances)
	//				{
	//					if (MontageInstance->Montage == LocalAnimMontageInfo.AnimMontage)
	//					{
	//						OutRepAnimMontageInfo.BlendTime = MontageInstance->GetBlendTime();
	//						break;
	//					}
	//				}
	//			}
	//		}
	//
	//		// When we start or stop an animation, update the clients right away for the Avatar Actor
	//		if (AbilityActorInfo->AvatarActor != nullptr)
	//		{
	//			AbilityActorInfo->AvatarActor->ForceNetUpdate();
	//		}
	//
	//		// When this changes, we should update whether or not we should be ticking
	//		UpdateShouldTick();
	//	}
	//
	//	// Replicate NextSectionID to keep it in sync.
	//	// We actually replicate NextSectionID+1 on a BYTE to put INDEX_NONE in there.
	//	int32 CurrentSectionID = LocalAnimMontageInfo.AnimMontage->GetSectionIndexFromPosition(OutRepAnimMontageInfo.Position);
	//	if (CurrentSectionID != INDEX_NONE)
	//	{
	//		constexpr bool bForceGameThreadValue = true;
	//		if (CVarUpdateMontageSectionIdToPlay.GetValueOnAnyThread(bForceGameThreadValue))
	//		{
	//			OutRepAnimMontageInfo.SectionIdToPlay = uint8(CurrentSectionID + 1);
	//		}
	//
	//		int32 NextSectionID = AnimInstance->Montage_GetNextSectionID(LocalAnimMontageInfo.AnimMontage, CurrentSectionID);
	//		if (NextSectionID >= (256 - 1))
	//		{
	//			ABILITY_LOG( Error, GAS_TEXT("AnimMontage_UpdateReplicatedData. NextSectionID = {}.  RepAnimMontageInfo.Position: %.2f, CurrentSectionID: {}. LocalAnimMontageInfo.AnimMontage {}"), 
	//				NextSectionID, OutRepAnimMontageInfo.Position, CurrentSectionID, *GetNameSafe(LocalAnimMontageInfo.AnimMontage) );
	//			ensure(NextSectionID < (256 - 1));
	//		}
	//		OutRepAnimMontageInfo.NextSectionID = uint8(NextSectionID + 1);
	//	}
	//	else
	//	{
	//		OutRepAnimMontageInfo.NextSectionID = 0;
	//	}
	//}
}

void UAbilitySystemComponent::AnimMontage_UpdateForcedPlayFlags(FGameplayAbilityRepAnimMontage& OutRepAnimMontageInfo)
{
	OutRepAnimMontageInfo.PlayInstanceId = LocalAnimMontageInfo.PlayInstanceId;
}

void UAbilitySystemComponent::OnPredictiveMontageRejected(UAnimMontage* PredictiveMontage)
{
    // TODO PlayMontage
    Assert(false);
	static const float MONTAGE_PREDICTION_REJECT_FADETIME = 0.25f;

	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance && PredictiveMontage)
	//{
	//	// If this montage is still playing: kill it
	//	if (AnimInstance->Montage_IsPlaying(PredictiveMontage))
	//	{
	//		AnimInstance->Montage_Stop(MONTAGE_PREDICTION_REJECT_FADETIME, PredictiveMontage);
	//	}
	//}
}

bool UAbilitySystemComponent::IsReadyForReplicatedMontage()
{
	/** Children may want to override this for additional checks (e.g, "has skin been applied") */
	return true;
}

/**	Replicated Event for AnimMontages */
void UAbilitySystemComponent::OnRep_ReplicatedAnimMontage()
{
    // TODO PlayMontage
    Assert(false);
	//UWorld* World = GetWorld();
	//
	//const FGameplayAbilityRepAnimMontage& ConstRepAnimMontageInfo = GetRepAnimMontageInfo_Mutable();
	//
	//if (ConstRepAnimMontageInfo.bSkipPlayRate)
	//{
	//	GetRepAnimMontageInfo_Mutable().PlayRate = 1.f;
	//}
	//
	//const bool bIsPlayingReplay = World && World->IsPlayingReplay();
	//
	//const float MONTAGE_REP_POS_ERR_THRESH = bIsPlayingReplay ? CVarReplayMontageErrorThreshold.GetValueOnGameThread() : 0.1f;
	//
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance == nullptr || !IsReadyForReplicatedMontage())
	//{
	//	// We can't handle this yet
	//	bPendingMontageRep = true;
	//	return;
	//}
	//bPendingMontageRep = false;
	//
	//if (!AbilityActorInfo->IsLocallyControlled())
	//{
	//	static const auto CVar = IConsoleManager::Get().FindTConsoleVariableDataInt(GAS_TEXT("net.Montage.Debug"));
	//	bool DebugMontage = (CVar && CVar->GetValueOnGameThread() == 1);
	//	if (DebugMontage)
	//	{
	//		LOG_WARN("\n\nOnRep_ReplicatedAnimMontage, {}"), *GetNameSafe(this));
	//		LOG_WARN("\tAnimMontage: {}\n\tPlayRate: {}\n\tPosition: {}\n\tBlendTime: {}\n\tNextSectionID: {}\n\tIsStopped: {}\n\tPlayInstanceId: {}"),
	//			*GetNameSafe(ConstRepAnimMontageInfo.Animation),
	//			ConstRepAnimMontageInfo.PlayRate,
	//			ConstRepAnimMontageInfo.Position,
	//			ConstRepAnimMontageInfo.BlendTime,
	//			ConstRepAnimMontageInfo.NextSectionID,
	//			ConstRepAnimMontageInfo.IsStopped,
	//			ConstRepAnimMontageInfo.PlayInstanceId);
	//		LOG_WARN("\tLocalAnimMontageInfo.AnimMontage: {}\n\tPosition: {}"),
	//			*GetNameSafe(LocalAnimMontageInfo.AnimMontage), AnimInstance->Montage_GetPosition(LocalAnimMontageInfo.AnimMontage));
	//	}
	//
	//	if(ConstRepAnimMontageInfo.Animation)
	//	{
	//		// New Montage to play
	//		UAnimSequenceBase* LocalAnimation = LocalAnimMontageInfo.AnimMontage && LocalAnimMontageInfo.AnimMontage->IsDynamicMontage() ? LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference() : LocalAnimMontageInfo.AnimMontage;
	//		if ((LocalAnimation != ConstRepAnimMontageInfo.Animation) ||
	//		    (LocalAnimMontageInfo.PlayInstanceId != ConstRepAnimMontageInfo.PlayInstanceId))
	//		{
	//			LocalAnimMontageInfo.PlayInstanceId = ConstRepAnimMontageInfo.PlayInstanceId;
	//
	//			if (UAnimMontage* MontageToPlay = Cast<UAnimMontage>(ConstRepAnimMontageInfo.Animation))
	//			{
	//				PlayMontageSimulated(MontageToPlay, ConstRepAnimMontageInfo.PlayRate);
	//			}
	//			else
	//			{
	//				PlaySlotAnimationAsDynamicMontageSimulated(
	//					ConstRepAnimMontageInfo.Animation,
	//					ConstRepAnimMontageInfo.SlotName,
	//					ConstRepAnimMontageInfo.BlendTime,
	//					ConstRepAnimMontageInfo.BlendOutTime,
	//					ConstRepAnimMontageInfo.PlayRate);
	//			}
	//		}
	//
	//		if (LocalAnimMontageInfo.AnimMontage == nullptr)
	//		{ 
	//			LOG_WARN("OnRep_ReplicatedAnimMontage: PlayMontageSimulated failed. Name: {}, Animation: {}"), *GetNameSafe(this), *GetNameSafe(ConstRepAnimMontageInfo.Animation));
	//			return;
	//		}
	//
	//		// Play Rate has changed
	//		if (AnimInstance->Montage_GetPlayRate(LocalAnimMontageInfo.AnimMontage) != ConstRepAnimMontageInfo.PlayRate)
	//		{
	//			AnimInstance->Montage_SetPlayRate(LocalAnimMontageInfo.AnimMontage, ConstRepAnimMontageInfo.PlayRate);
	//		}
	//
	//		// Compressed Flags
	//		const bool bIsStopped = AnimInstance->Montage_GetIsStopped(LocalAnimMontageInfo.AnimMontage);
	//		const bool bReplicatedIsStopped = bool(ConstRepAnimMontageInfo.IsStopped);
	//
	//		// Process stopping first, so we don't change sections and cause blending to pop.
	//		if (bReplicatedIsStopped)
	//		{
	//			if (!bIsStopped)
	//			{
	//				CurrentMontageStop(ConstRepAnimMontageInfo.BlendTime);
	//			}
	//		}
	//		else if (!ConstRepAnimMontageInfo.SkipPositionCorrection)
	//		{
	//			const int32 RepSectionID = LocalAnimMontageInfo.AnimMontage->GetSectionIndexFromPosition(ConstRepAnimMontageInfo.Position);
	//			const int32 RepNextSectionID = int32(ConstRepAnimMontageInfo.NextSectionID) - 1;
	//	
	//			// And NextSectionID for the replicated SectionID.
	//			if( RepSectionID != INDEX_NONE )
	//			{
	//				const int32 NextSectionID = AnimInstance->Montage_GetNextSectionID(LocalAnimMontageInfo.AnimMontage, RepSectionID);
	//
	//				// If NextSectionID is different than the replicated one, then set it.
	//				if( NextSectionID != RepNextSectionID )
	//				{
	//					AnimInstance->Montage_SetNextSection(LocalAnimMontageInfo.AnimMontage->GetSectionName(RepSectionID), LocalAnimMontageInfo.AnimMontage->GetSectionName(RepNextSectionID), LocalAnimMontageInfo.AnimMontage);
	//				}
	//
	//				// Make sure we haven't received that update too late and the client hasn't already jumped to another section. 
	//				const int32 CurrentSectionID = LocalAnimMontageInfo.AnimMontage->GetSectionIndexFromPosition(AnimInstance->Montage_GetPosition(LocalAnimMontageInfo.AnimMontage));
	//				if ((CurrentSectionID != RepSectionID) && (CurrentSectionID != RepNextSectionID))
	//				{
	//					// Client is in a wrong section, teleport it into the beginning of the right section
	//					const float SectionStartTime = LocalAnimMontageInfo.AnimMontage->GetAnimCompositeSection(RepSectionID).GetTime();
	//					AnimInstance->Montage_SetPosition(LocalAnimMontageInfo.AnimMontage, SectionStartTime);
	//				}
	//			}
	//
	//			// Update Position. If error is too great, jump to replicated position.
	//			const float CurrentPosition = AnimInstance->Montage_GetPosition(LocalAnimMontageInfo.AnimMontage);
	//			const int32 CurrentSectionID = LocalAnimMontageInfo.AnimMontage->GetSectionIndexFromPosition(CurrentPosition);
	//			const float DeltaPosition = ConstRepAnimMontageInfo.Position - CurrentPosition;
	//
	//			// Only check threshold if we are located in the same section. Different sections require a bit more work as we could be jumping around the timeline.
	//			// And therefore DeltaPosition is not as trivial to determine.
	//			if ((CurrentSectionID == RepSectionID) && (FMath::Abs(DeltaPosition) > MONTAGE_REP_POS_ERR_THRESH) && (ConstRepAnimMontageInfo.IsStopped == 0))
	//			{
	//				// fast forward to server position and trigger notifies
	//				if (FAnimMontageInstance* MontageInstance = AnimInstance->GetActiveInstanceForMontage(LocalAnimMontageInfo.AnimMontage))
	//				{
	//					// Skip triggering notifies if we're going backwards in time, we've already triggered them.
	//					const float DeltaTime = !FMath::IsNearlyZero(ConstRepAnimMontageInfo.PlayRate) ? (DeltaPosition / ConstRepAnimMontageInfo.PlayRate) : 0.f;
	//					if (DeltaTime >= 0.f)
	//					{
	//						MontageInstance->UpdateWeight(DeltaTime);
	//						MontageInstance->HandleEvents(CurrentPosition, ConstRepAnimMontageInfo.Position, nullptr);
	//						AnimInstance->TriggerAnimNotifies(DeltaTime);
	//					}
	//				}
	//				AnimInstance->Montage_SetPosition(LocalAnimMontageInfo.AnimMontage, ConstRepAnimMontageInfo.Position);
	//			}
	//		}
	//		// Update current and next section if not replicating position
	//		else
	//		{
	//			const float CurrentPosition = AnimInstance->Montage_GetPosition(LocalAnimMontageInfo.AnimMontage);
	//			int32 CurrentSectionID = LocalAnimMontageInfo.AnimMontage->GetSectionIndexFromPosition(CurrentPosition);
	//			const int32 RepSectionIdToPlay = (static_cast<int32>(ConstRepAnimMontageInfo.SectionIdToPlay) - 1);
	//			FName CurrentSectionName = LocalAnimMontageInfo.AnimMontage->GetSectionName(CurrentSectionID);
	//
	//			// If RepSectionIdToPlay is valid and different from the current section, then jump to it
	//			if (RepSectionIdToPlay != INDEX_NONE && RepSectionIdToPlay != CurrentSectionID )
	//			{
	//				CurrentSectionName = LocalAnimMontageInfo.AnimMontage->GetSectionName(RepSectionIdToPlay);
	//				if (CurrentSectionName != NAME_None)
	//				{
	//					AnimInstance->Montage_JumpToSection(CurrentSectionName);
	//					CurrentSectionID = RepSectionIdToPlay;
	//				}
	//				else
	//				{
	//					LOG_WARN("OnRep_ReplicatedAnimMontage: Failed to replicate current section due to invalid name. Name: {}, Section ID: {}"), 
	//					*GetNameSafe(this), 
	//					CurrentSectionID);
	//				}
	//			}
	//
	//			constexpr bool bForceGameThreadValue = true;
	//			if (CVarReplicateMontageNextSectionId.GetValueOnAnyThread(bForceGameThreadValue))
	//			{
	//				const int32 NextSectionID = AnimInstance->Montage_GetNextSectionID(LocalAnimMontageInfo.AnimMontage, CurrentSectionID);
	//				const int32 RepNextSectionID = int32(ConstRepAnimMontageInfo.NextSectionID) - 1;
	//
	//				// If NextSectionID is different than the replicated one, then set it.
	//				if (RepNextSectionID != INDEX_NONE && NextSectionID != RepNextSectionID)
	//				{
	//					const FName NextSectionName = LocalAnimMontageInfo.AnimMontage->GetSectionName(RepNextSectionID);
	//					if (CurrentSectionName != NAME_None && NextSectionName != NAME_None)
	//					{
	//						AnimInstance->Montage_SetNextSection(CurrentSectionName, NextSectionName, LocalAnimMontageInfo.AnimMontage);
	//					}
	//					else
	//					{
	//						LOG_WARN("OnRep_ReplicatedAnimMontage: Failed to replicate next section due to invalid name. Name: {}, Current Section ID: {}, Next Section ID: {}"), 
	//						*GetNameSafe(this), 
	//						CurrentSectionID, 
	//						RepNextSectionID);
	//					}
	//				}
	//			}
	//		}
	//	}
	//}
}

void UAbilitySystemComponent::CurrentMontageStop(float OverrideBlendOutTime)
{
    // TODO PlayMontage
    Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//UAnimMontage* MontageToStop = LocalAnimMontageInfo.AnimMontage;
	//bool bShouldStopMontage = AnimInstance && MontageToStop && !AnimInstance->Montage_GetIsStopped(MontageToStop);
	//
	//if (bShouldStopMontage)
	//{
	//	const float BlendOutTime = (OverrideBlendOutTime >= 0.0f ? OverrideBlendOutTime : MontageToStop->BlendOut.GetBlendTime());
	//
	//	AnimInstance->Montage_Stop(BlendOutTime, MontageToStop);
	//
	//	if (IsOwnerActorAuthoritative())
	//	{
	//		AnimMontage_UpdateReplicatedData();
	//	}
	//}
}

void UAbilitySystemComponent::StopMontageIfCurrent(const UAnimMontage& Montage, float OverrideBlendOutTime)
{
    // TODO PlayMontage
    Assert(false);
	//if (&Montage == LocalAnimMontageInfo.AnimMontage)
	//{
	//	CurrentMontageStop(OverrideBlendOutTime);
	//}
}

void UAbilitySystemComponent::ClearAnimatingAbility(UGameplayAbility* Ability)
{
    // TODO PlayMontage
    Assert(false);
	//if (LocalAnimMontageInfo.AnimatingAbility.Get() == Ability)
	//{
//		Ability->SetCurrentMontage(nullptr);
//		LocalAnimMontageInfo.AnimatingAbility = nullptr;
	//}
}

void UAbilitySystemComponent::CurrentMontageJumpToSection(FName SectionName)
{
    // TODO PlayMontage
    //Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if ((SectionName != NAME_None) && AnimInstance && LocalAnimMontageInfo.AnimMontage)
	//{
	//	AnimInstance->Montage_JumpToSection(SectionName, LocalAnimMontageInfo.AnimMontage);
	//
	//	// This data is needed for replication on the server and recording replays on clients.
	//	// We need to set GetRepAnimMontageInfo_Mutable on replay recording clients because this data is NOT replicated to all clients (for example, it is NOT replicated to autonomous proxy clients.)
	//	if (ShouldRecordMontageReplication())
	//	{
	//		FGameplayAbilityRepAnimMontage& MutableRepAnimMontageInfo = GetRepAnimMontageInfo_Mutable();
	//
	//		MutableRepAnimMontageInfo.SectionIdToPlay = 0;
	//		if (MutableRepAnimMontageInfo.Animation)
	//		{
	//			// Only change SectionIdToPlay if the anim montage's source is a montage. Dynamic montages have no sections.
	//			if (const UAnimMontage* RepAnimMontage = Cast<UAnimMontage>(MutableRepAnimMontageInfo.Animation))
	//			{
	//				// we add one so INDEX_NONE can be used in the on rep
	//				MutableRepAnimMontageInfo.SectionIdToPlay = RepAnimMontage->GetSectionIndex(SectionName) + 1;
	//			}
	//		}
	//
	//		AnimMontage_UpdateReplicatedData();
	//	}
	//	
	//	// If we are NOT the authority, then let the server handling jumping the montage.
	//	if (!IsOwnerActorAuthoritative())
	//	{	
	//		UAnimSequenceBase* Animation = LocalAnimMontageInfo.AnimMontage->IsDynamicMontage() ? LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference() : LocalAnimMontageInfo.AnimMontage;
	//		ServerCurrentMontageJumpToSectionName(Animation, SectionName);
	//	}
	//}
}

void UAbilitySystemComponent::CurrentMontageSetNextSectionName(FName FromSectionName, FName ToSectionName)
{
    // TODO PlayMontage
    Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (LocalAnimMontageInfo.AnimMontage && AnimInstance)
	//{
	//	// Set Next Section Name.
	//	AnimInstance->Montage_SetNextSection(FromSectionName, ToSectionName, LocalAnimMontageInfo.AnimMontage);
	//
	//	// Update replicated version for Simulated Proxies if we are on the server.
	//	if( IsOwnerActorAuthoritative() )
	//	{
	//		AnimMontage_UpdateReplicatedData();
	//	}
	//	else
	//	{
	//		float CurrentPosition = AnimInstance->Montage_GetPosition(LocalAnimMontageInfo.AnimMontage);
	//		UAnimSequenceBase* Animation = LocalAnimMontageInfo.AnimMontage->IsDynamicMontage() ? LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference() : LocalAnimMontageInfo.AnimMontage;
	//		ServerCurrentMontageSetNextSectionName(Animation, CurrentPosition, FromSectionName, ToSectionName);
	//	}
	//}
}

void UAbilitySystemComponent::CurrentMontageSetPlayRate(float InPlayRate)
{
    // TODO PlayMontage
    Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (LocalAnimMontageInfo.AnimMontage && AnimInstance)
	//{
	//	// Set Play Rate
	//	AnimInstance->Montage_SetPlayRate(LocalAnimMontageInfo.AnimMontage, InPlayRate);
	//
	//	// Update replicated version for Simulated Proxies if we are on the server.
	//	if (IsOwnerActorAuthoritative())
	//	{
	//		AnimMontage_UpdateReplicatedData();
	//	}
	//	else
	//	{
	//		UAnimSequenceBase* Animation = LocalAnimMontageInfo.AnimMontage->IsDynamicMontage() ? LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference() : LocalAnimMontageInfo.AnimMontage;
	//		ServerCurrentMontageSetPlayRate(LocalAnimMontageInfo.AnimMontage, InPlayRate);
	//	}
	//}
}

//bool UAbilitySystemComponent::ServerCurrentMontageSetNextSectionName_Validate(UAnimSequenceBase* ClientAnimation, float ClientPosition, FName SectionName, FName NextSectionName)
//{
//	return true;
//}

void UAbilitySystemComponent::ServerCurrentMontageSetNextSectionName(UAnimSequenceBase* ClientAnimation, float ClientPosition, FName SectionName, FName NextSectionName)
{
    // TODO PlayMontage
    Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance && LocalAnimMontageInfo.AnimMontage)
	//{
	//	UAnimSequenceBase* CurrentAnimation = LocalAnimMontageInfo.AnimMontage->IsDynamicMontage() ? LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference() : LocalAnimMontageInfo.AnimMontage;
	//	if (ClientAnimation == CurrentAnimation)
	//	{
	//		UAnimMontage* CurrentAnimMontage = LocalAnimMontageInfo.AnimMontage;
	//
	//		// Set NextSectionName
	//		AnimInstance->Montage_SetNextSection(SectionName, NextSectionName, CurrentAnimMontage);
	//
	//		// Correct position if we are in an invalid section
	//		float CurrentPosition = AnimInstance->Montage_GetPosition(CurrentAnimMontage);
	//		int32 CurrentSectionID = CurrentAnimMontage->GetSectionIndexFromPosition(CurrentPosition);
	//		FName CurrentSectionName = CurrentAnimMontage->GetSectionName(CurrentSectionID);
	//
	//		int32 ClientSectionID = CurrentAnimMontage->GetSectionIndexFromPosition(ClientPosition);
	//		FName ClientCurrentSectionName = CurrentAnimMontage->GetSectionName(ClientSectionID);
	//		if ((CurrentSectionName != ClientCurrentSectionName) || (CurrentSectionName != SectionName))
	//		{
	//			// We are in an invalid section, jump to client's position.
	//			AnimInstance->Montage_SetPosition(CurrentAnimMontage, ClientPosition);
	//		}
	//
	//		// Update replicated version for Simulated Proxies if we are on the server.
	//		if (IsOwnerActorAuthoritative())
	//		{
	//			AnimMontage_UpdateReplicatedData();
	//		}
	//	}
	//}
}

//bool UAbilitySystemComponent::ServerCurrentMontageJumpToSectionName_Validate(UAnimSequenceBase* ClientAnimation, FName SectionName)
//{
//	return true;
//}

void UAbilitySystemComponent::ServerCurrentMontageJumpToSectionName(UAnimSequenceBase* ClientAnimation, FName SectionName)
{
    // TODO PlayMontage
    Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance && LocalAnimMontageInfo.AnimMontage)
	//{
	//	UAnimSequenceBase* CurrentAnimation = LocalAnimMontageInfo.AnimMontage->IsDynamicMontage() ? LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference() : LocalAnimMontageInfo.AnimMontage;
	//	if (ClientAnimation == CurrentAnimation)
	//	{
	//		UAnimMontage* CurrentAnimMontage = LocalAnimMontageInfo.AnimMontage;
	//
	//		// Set NextSectionName
	//		AnimInstance->Montage_JumpToSection(SectionName, CurrentAnimMontage);
	//
	//		// Update replicated version for Simulated Proxies if we are on the server.
	//		if (IsOwnerActorAuthoritative())
	//		{
	//			FGameplayAbilityRepAnimMontage& MutableRepAnimMontageInfo = GetRepAnimMontageInfo_Mutable();
	//
	//			MutableRepAnimMontageInfo.SectionIdToPlay = 0;
	//			if (MutableRepAnimMontageInfo.Animation && SectionName != NAME_None)
	//			{
	//				// Only change SectionIdToPlay if the anim montage's source is a montage. Dynamic montages have no sections.
	//				if (const UAnimMontage* RepAnimMontage = Cast<UAnimMontage>(MutableRepAnimMontageInfo.Animation))
	//				{
	//					// we add one so INDEX_NONE can be used in the on rep
	//					MutableRepAnimMontageInfo.SectionIdToPlay = RepAnimMontage->GetSectionIndex(SectionName) + 1;
	//				}
	//			}
	//
	//			AnimMontage_UpdateReplicatedData();
	//		}
	//	}
	//}
}

//bool UAbilitySystemComponent::ServerCurrentMontageSetPlayRate_Validate(UAnimSequenceBase* ClientAnimation, float InPlayRate)
//{
//	return true;
//}

void UAbilitySystemComponent::ServerCurrentMontageSetPlayRate(UAnimSequenceBase* ClientAnimation, float InPlayRate)
{
    // TODO PlayMontage
    Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (AnimInstance && LocalAnimMontageInfo.AnimMontage)
	//{
	//	UAnimSequenceBase* CurrentAnimation = LocalAnimMontageInfo.AnimMontage->IsDynamicMontage() ? LocalAnimMontageInfo.AnimMontage->GetFirstAnimReference() : LocalAnimMontageInfo.AnimMontage;
	//	if (ClientAnimation == CurrentAnimation)
	//	{
	//		UAnimMontage* CurrentAnimMontage = LocalAnimMontageInfo.AnimMontage;
	//
	//		// Set PlayRate
	//		AnimInstance->Montage_SetPlayRate(CurrentAnimMontage, InPlayRate);
	//
	//		// Update replicated version for Simulated Proxies if we are on the server.
	//		if (IsOwnerActorAuthoritative())
	//		{
	//			AnimMontage_UpdateReplicatedData();
	//		}
	//	}
	//}
}

UAnimMontage* UAbilitySystemComponent::PlaySlotAnimationAsDynamicMontage(UGameplayAbility* AnimatingAbility, FGameplayAbilityActivationInfo ActivationInfo, UAnimSequenceBase* AnimAsset, FName SlotName, float BlendInTime, float BlendOutTime, float InPlayRate, float StartTimeSeconds)
{
    // TODO PlayMontage
    Assert(false);
    return nullptr;
	//UAnimMontage* DynamicMontage = UAnimMontage::CreateSlotAnimationAsDynamicMontage(AnimAsset, SlotName, BlendInTime, BlendOutTime, InPlayRate, 1, -1.0f, 0.0f);
	//PlayMontage(AnimatingAbility, ActivationInfo, DynamicMontage, InPlayRate, NAME_None, StartTimeSeconds);
	//return DynamicMontage;
}

UAnimMontage* UAbilitySystemComponent::PlaySlotAnimationAsDynamicMontageSimulated(UAnimSequenceBase* AnimAsset, FName SlotName, float BlendInTime, float BlendOutTime, float InPlayRate)
{
    // TODO PlayMontage
    Assert(false);
    return nullptr;
	//UAnimMontage* DynamicMontage = UAnimMontage::CreateSlotAnimationAsDynamicMontage(AnimAsset, SlotName, BlendInTime, BlendOutTime, InPlayRate, 1, -1.0f, 0.0f);
	//PlayMontageSimulated(DynamicMontage, InPlayRate, NAME_None);
	//return DynamicMontage;
}

UAnimMontage* UAbilitySystemComponent::GetCurrentMontage() const
{
    // TODO PlayMontage
    Assert(false);
    return nullptr;
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//if (LocalAnimMontageInfo.AnimMontage && AnimInstance && AnimInstance->Montage_IsActive(LocalAnimMontageInfo.AnimMontage))
	//{
	//	return LocalAnimMontageInfo.AnimMontage;
	//}
	//
	//return nullptr;
}

int32 UAbilitySystemComponent::GetCurrentMontageSectionID() const
{
    // TODO PlayMontage
    Assert(false);
    return 0;
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//UAnimMontage* CurrentAnimMontage = GetCurrentMontage();
	//
	//if (CurrentAnimMontage && AnimInstance)
	//{
	//	float MontagePosition = AnimInstance->Montage_GetPosition(CurrentAnimMontage);
	//	return CurrentAnimMontage->GetSectionIndexFromPosition(MontagePosition);
	//}
	//
	//return INDEX_NONE;
}

FName UAbilitySystemComponent::GetCurrentMontageSectionName() const
{
    // TODO PlayMontage
    Assert(false);
    return "";
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//UAnimMontage* CurrentAnimMontage = GetCurrentMontage();
	//
	//if (CurrentAnimMontage && AnimInstance)
	//{
	//	float MontagePosition = AnimInstance->Montage_GetPosition(CurrentAnimMontage);
	//	int32 CurrentSectionID = CurrentAnimMontage->GetSectionIndexFromPosition(MontagePosition);
	//
	//	return CurrentAnimMontage->GetSectionName(CurrentSectionID);
	//}
	//
	//return NAME_None;
}

float UAbilitySystemComponent::GetCurrentMontageSectionLength() const
{
    // TODO PlayMontage
    Assert(false);
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//UAnimMontage* CurrentAnimMontage = GetCurrentMontage();
	//if (CurrentAnimMontage && AnimInstance)
	//{
	//	int32 CurrentSectionID = GetCurrentMontageSectionID();
	//	if (CurrentSectionID != INDEX_NONE)
	//	{
	//		std::vector<FCompositeSection>& CompositeSections = CurrentAnimMontage->CompositeSections;
	//
	//		// If we have another section after us, then take delta between both start times.
	//		if (CurrentSectionID < (CompositeSections.Num() - 1))
	//		{
	//			return (CompositeSections[CurrentSectionID + 1].GetTime() - CompositeSections[CurrentSectionID].GetTime());
	//		}
	//		// Otherwise we are the last section, so take delta with Montage total time.
	//		else
	//		{
	//			return (CurrentAnimMontage->GetPlayLength() - CompositeSections[CurrentSectionID].GetTime());
	//		}
	//	}
	//
	//	// if we have no sections, just return total length of Montage.
	//	return CurrentAnimMontage->GetPlayLength();
	//}

	return 0.f;
}

float UAbilitySystemComponent::GetCurrentMontageSectionTimeLeft() const
{
    // TODO PlayMontage
    Assert(false);
    //
	//UAnimInstance* AnimInstance = AbilityActorInfo ? AbilityActorInfo->GetAnimInstance() : nullptr;
	//UAnimMontage* CurrentAnimMontage = GetCurrentMontage();
	//if (CurrentAnimMontage && AnimInstance && AnimInstance->Montage_IsActive(CurrentAnimMontage))
	//{
	//	const float CurrentPosition = AnimInstance->Montage_GetPosition(CurrentAnimMontage);
	//	return CurrentAnimMontage->GetSectionTimeLeftFromPos(CurrentPosition);
	//}

	return -1.f;
}

void UAbilitySystemComponent::SetMontageRepAnimPositionMethod(ERepAnimPositionMethod InMethod)
{
	GetRepAnimMontageInfo_Mutable().SetRepAnimPositionMethod(InMethod);
}

bool UAbilitySystemComponent::IsAnimatingAbility(UGameplayAbility* InAbility) const
{
	return (WeakPtrGet(LocalAnimMontageInfo.AnimatingAbility) == InAbility);
}

UGameplayAbility* UAbilitySystemComponent::GetAnimatingAbility()
{
    return WeakPtrGet(LocalAnimMontageInfo.AnimatingAbility);
}

void UAbilitySystemComponent::ConfirmAbilityTargetData(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, const FGameplayAbilityTargetDataHandle& TargetData, const FGameplayTag& ApplicationTag)
{
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		CachedData->TargetSetDelegate.Broadcast(TargetData, ApplicationTag);
	}
}

void UAbilitySystemComponent::CancelAbilityTargetData(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		CachedData->Reset();
		CachedData->TargetCancelledDelegate.Broadcast();
	}
}

void UAbilitySystemComponent::ConsumeAllReplicatedData(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		CachedData->Reset();
	}
}

void UAbilitySystemComponent::ConsumeClientReplicatedTargetData(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		CachedData->TargetData.Clear();
		CachedData->bTargetConfirmed = false;
		CachedData->bTargetCancelled = false;
	}
}

void UAbilitySystemComponent::ConsumeGenericReplicatedEvent(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		CachedData->GenericEvents[EventType].bTriggered = false;
	}
}

FAbilityReplicatedData UAbilitySystemComponent::GetReplicatedDataOfGenericReplicatedEvent(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	FAbilityReplicatedData ReturnData;

	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		ReturnData.bTriggered = CachedData->GenericEvents[EventType].bTriggered;
		ReturnData.VectorPayload = CachedData->GenericEvents[EventType].VectorPayload;
	}

	return ReturnData;
}

void UAbilitySystemComponent::ServerSetReplicatedEvent(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey,  FPredictionKey CurrentPredictionKey)
{
	FScopedPredictionWindow ScopedPrediction(this, CurrentPredictionKey);

	InvokeReplicatedEvent(EventType, AbilityHandle, AbilityOriginalPredictionKey, CurrentPredictionKey);
}

void UAbilitySystemComponent::ServerSetReplicatedEventWithPayload(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey,  FPredictionKey CurrentPredictionKey, cross::Double3 VectorPayload)
{
	FScopedPredictionWindow ScopedPrediction(this, CurrentPredictionKey);

	InvokeReplicatedEventWithPayload(EventType, AbilityHandle, AbilityOriginalPredictionKey, CurrentPredictionKey, VectorPayload);
}

bool UAbilitySystemComponent::InvokeReplicatedEvent(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, FPredictionKey CurrentPredictionKey)
{
    std::shared_ptr<FAbilityReplicatedDataCache> ReplicatedData = AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));

	ReplicatedData->GenericEvents[(uint8)EventType].bTriggered = true;
	ReplicatedData->PredictionKey = CurrentPredictionKey;

	if (ReplicatedData->GenericEvents[EventType].Delegate.IsBound())
	{
		ReplicatedData->GenericEvents[EventType].Delegate.Broadcast();
		return true;
	}
	else
	{
		return false;
	}
}

bool UAbilitySystemComponent::InvokeReplicatedEventWithPayload(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, FPredictionKey CurrentPredictionKey, cross::Double3 VectorPayload)
{
    std::shared_ptr<FAbilityReplicatedDataCache> ReplicatedData = AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	ReplicatedData->GenericEvents[(uint8)EventType].bTriggered = true;
	ReplicatedData->GenericEvents[(uint8)EventType].VectorPayload = VectorPayload;
	ReplicatedData->PredictionKey = CurrentPredictionKey;

	if (ReplicatedData->GenericEvents[EventType].Delegate.IsBound())
	{
		ReplicatedData->GenericEvents[EventType].Delegate.Broadcast();
		return true;
	}
	else
	{
		return false;
	}
}

//bool UAbilitySystemComponent::ServerSetReplicatedEvent_Validate(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey,  FPredictionKey CurrentPredictionKey)
//{
//	if (EventType >= EAbilityGenericReplicatedEvent::MAX)
//	{
//		return false;
//	}
//	return true;
//}

//ool UAbilitySystemComponent::ServerSetReplicatedEventWithPayload_Validate(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey,  FPredictionKey CurrentPredictionKey, cross::Double3 VectorPayload)
//
//	if (EventType >= EAbilityGenericReplicatedEvent::MAX)
//	{
//		return false;
//	}
//	return true;
//

void UAbilitySystemComponent::ClientSetReplicatedEvent(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	InvokeReplicatedEvent(EventType, AbilityHandle, AbilityOriginalPredictionKey, ScopedPredictionKey);
}

void UAbilitySystemComponent::ServerSetReplicatedTargetData(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, const FGameplayAbilityTargetDataHandle& ReplicatedTargetDataHandle, FGameplayTag ApplicationTag, FPredictionKey CurrentPredictionKey)
{
	FScopedPredictionWindow ScopedPrediction(this, CurrentPredictionKey);

	// Always adds to cache to store the new data
	std::shared_ptr<FAbilityReplicatedDataCache> ReplicatedData = AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));

	if (ReplicatedData->TargetData.Num() > 0)
	{
		FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
		if (Spec && Spec->Ability)
		{
			// Can happen under normal circumstances if ServerForceClientTargetData is hit
			LOG_INFO("Ability {} is overriding pending replicated target data.", Spec->Ability->GetName());
		}
	}

	ReplicatedData->TargetData = ReplicatedTargetDataHandle;
	ReplicatedData->ApplicationTag = ApplicationTag;
	ReplicatedData->bTargetConfirmed = true;
	ReplicatedData->bTargetCancelled = false;
	ReplicatedData->PredictionKey = CurrentPredictionKey;

	ReplicatedData->TargetSetDelegate.Broadcast(ReplicatedTargetDataHandle, ReplicatedData->ApplicationTag);
}

//bool UAbilitySystemComponent::ServerSetReplicatedTargetData_Validate(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, const FGameplayAbilityTargetDataHandle& ReplicatedTargetDataHandle, FGameplayTag ApplicationTag, FPredictionKey CurrentPredictionKey)
//{
//	// check the data coming from the client to ensure it's valid
//	for (const std::shared_ptr<FGameplayAbilityTargetData>& TgtData : ReplicatedTargetDataHandle.Data)
//	{
//		if (!ensure(TgtData.IsValid()))
//		{
//			return false;
//		}
//	}
//
//	return true;
//}

void UAbilitySystemComponent::ServerSetReplicatedTargetDataCancelled(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, FPredictionKey CurrentPredictionKey)
{
	FScopedPredictionWindow ScopedPrediction(this, CurrentPredictionKey);

	// Always adds to cache to store the new data
	std::shared_ptr<FAbilityReplicatedDataCache> ReplicatedData = AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));

	ReplicatedData->Reset();
	ReplicatedData->bTargetCancelled = true;
	ReplicatedData->PredictionKey = CurrentPredictionKey;
	ReplicatedData->TargetCancelledDelegate.Broadcast();
}

//bool UAbilitySystemComponent::ServerSetReplicatedTargetDataCancelled_Validate(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, FPredictionKey CurrentPredictionKey)
//{
//	return true;
//}

void UAbilitySystemComponent::CallAllReplicatedDelegatesIfSet(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		FScopedPredictionWindow ScopedWindow(this, CachedData->PredictionKey, false);
		if (CachedData->bTargetConfirmed)
		{
			CachedData->TargetSetDelegate.Broadcast(CachedData->TargetData, CachedData->ApplicationTag);
		}
		else if (CachedData->bTargetCancelled)
		{
			CachedData->TargetCancelledDelegate.Broadcast();
		}

		for (int32 idx=0; idx < EAbilityGenericReplicatedEvent::MAX; ++idx)
		{
			if (CachedData->GenericEvents[idx].bTriggered)
			{
				CachedData->GenericEvents[idx].Delegate.Broadcast();
			}
		}
	}
}

bool UAbilitySystemComponent::CallReplicatedTargetDataDelegatesIfSet(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	bool CalledDelegate = false;
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData)
	{
		// Use prediction key that was sent to us
		FScopedPredictionWindow ScopedWindow(this, CachedData->PredictionKey, false);

		if (CachedData->bTargetConfirmed)
		{
			CachedData->TargetSetDelegate.Broadcast(CachedData->TargetData, CachedData->ApplicationTag);
			CalledDelegate = true;
		}
		else if (CachedData->bTargetCancelled)
		{
			CachedData->TargetCancelledDelegate.Broadcast();
			CalledDelegate = true;
		}
	}

	return CalledDelegate;
}

bool UAbilitySystemComponent::CallReplicatedEventDelegateIfSet(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.Find(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData && CachedData->GenericEvents[EventType].bTriggered)
	{
		FScopedPredictionWindow ScopedWindow(this, CachedData->PredictionKey, false);

		// Already triggered, fire off delegate
		CachedData->GenericEvents[EventType].Delegate.Broadcast();
		return true;
	}
	return false;
}

bool UAbilitySystemComponent::CallOrAddReplicatedDelegate(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, FSimpleMulticastDelegate::FDelegate Delegate)
{
    std::shared_ptr<FAbilityReplicatedDataCache> CachedData = AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey));
	if (CachedData->GenericEvents[EventType].bTriggered)
	{
		FScopedPredictionWindow ScopedWindow(this, CachedData->PredictionKey, false);

		// Already triggered, fire off delegate
		Delegate();
		return true;
	}
	
	// Not triggered yet, so just add the delegate
	CachedData->GenericEvents[EventType].Delegate.Add(std::move(Delegate));
	return false;
}

FAbilityTargetDataSetDelegate& UAbilitySystemComponent::AbilityTargetDataSetDelegate(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	return AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey))->TargetSetDelegate;
}

FSimpleMulticastDelegate& UAbilitySystemComponent::AbilityTargetDataCancelledDelegate(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	return AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey))->TargetCancelledDelegate;
}

FSimpleMulticastDelegate& UAbilitySystemComponent::AbilityReplicatedEventDelegate(EAbilityGenericReplicatedEvent::Type EventType, FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey)
{
	return AbilityTargetDataMap.FindOrAdd(FGameplayAbilitySpecHandleAndPredictionKey(AbilityHandle, AbilityOriginalPredictionKey))->GenericEvents[EventType].Delegate;
}

int32 AbilitySystemLogServerRPCBatching = 0;
//static FAutoConsoleVariableRef CVarAbilitySystemLogServerRPCBatching(GAS_TEXT("AbilitySystem.ServerRPCBatching.Log"), AbilitySystemLogServerRPCBatching, GAS_TEXT(""), ECVF_Default	);

FScopedServerAbilityRPCBatcher::FScopedServerAbilityRPCBatcher(UAbilitySystemComponent* InASC, FGameplayAbilitySpecHandle InAbilityHandle) 
	: ASC(InASC), AbilityHandle(InAbilityHandle), ScopedPredictionWindow(InASC)
{
	if (ASC && AbilityHandle.IsValid() && ASC->ShouldDoServerAbilityRPCBatch())
	{
		ASC->BeginServerAbilityRPCBatch(InAbilityHandle);
	}
	else
	{
		ASC = nullptr;
	}
}

FScopedServerAbilityRPCBatcher::~FScopedServerAbilityRPCBatcher()
{
	if (ASC)
	{
		ASC->EndServerAbilityRPCBatch(AbilityHandle);
	}
}

//bool UAbilitySystemComponent::ServerAbilityRPCBatch_Validate(FServerAbilityRPCBatch BatchInfo)
//{
//	return true;
//}
//
void UAbilitySystemComponent::ServerAbilityRPCBatch(FServerAbilityRPCBatch BatchInfo)
{
	ServerAbilityRPCBatch_Internal(BatchInfo);
}

void UAbilitySystemComponent::ServerAbilityRPCBatch_Internal(FServerAbilityRPCBatch& BatchInfo)
{
	LOG_INFO("::ServerAbilityRPCBatch_Implementation {} {}", BatchInfo.AbilitySpecHandle.ToString(), BatchInfo.PredictionKey.ToString());

	ServerTryActivateAbility(BatchInfo.AbilitySpecHandle, BatchInfo.InputPressed, BatchInfo.PredictionKey);
	ServerSetReplicatedTargetData(BatchInfo.AbilitySpecHandle, BatchInfo.PredictionKey, BatchInfo.TargetData, FGameplayTag(), BatchInfo.PredictionKey);

	if (BatchInfo.Ended)
	{
		// This FakeInfo is probably bogus for the general case but should work for the limited use of batched RPCs
		FGameplayAbilityActivationInfo FakeInfo;
		FakeInfo.ServerSetActivationPredictionKey(BatchInfo.PredictionKey);
		ServerEndAbility(BatchInfo.AbilitySpecHandle, FakeInfo, BatchInfo.PredictionKey);
	}
}

void UAbilitySystemComponent::BeginServerAbilityRPCBatch(FGameplayAbilitySpecHandle AbilityHandle)
{
    LOG_INFO("::BeginServerAbilityRPCBatch {}", AbilityHandle.ToString());

	if (FServerAbilityRPCBatch* ExistingBatchData = ArrayFind(LocalServerAbilityRPCBatchData, AbilityHandle))
	{
		FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
		LOG_WARN("::BeginServerAbilityRPCBatch called when there is already a batch started for ability ({})", Spec ? GetNameSafe(Spec->Ability) : GAS_TEXT("INVALID"));
		return;
	}
	
	/** Create new FServerAbilityRPCBatch and initiailze it to this AbilityHandle */
	FServerAbilityRPCBatch& NewBatchData = LocalServerAbilityRPCBatchData.emplace_back();
	NewBatchData.AbilitySpecHandle = AbilityHandle;
}

void UAbilitySystemComponent::EndServerAbilityRPCBatch(FGameplayAbilitySpecHandle AbilityHandle)
{
    Assert(false);
	/** See if we have batch data for this ability (we should!) and call the ServerAbilityRPCBatch rpc (send what we batched up to the server) */
//	int32 idx = LocalServerAbilityRPCBatchData.IndexOfByKey(AbilityHandle);
//	if (idx != INDEX_NONE)
//	{
//		UE_CLOG(AbilitySystemLogServerRPCBatching, LogAbilitySystem, Display, GAS_TEXT("::EndServerAbilityRPCBatch. Calling ServerAbilityRPCBatch. Handle: {}. PredictionKey: {}."), *LocalServerAbilityRPCBatchData[idx].AbilitySpecHandle.ToString(), *LocalServerAbilityRPCBatchData[idx].PredictionKey.ToString());
//
//		FServerAbilityRPCBatch& ThisBatch = LocalServerAbilityRPCBatchData[idx];
//		if (ThisBatch.Started)
//		{
//			if (ThisBatch.PredictionKey.IsValidKey() == false)
//			{
//				LOG_WARN("::EndServerAbilityRPCBatch was started but has an invalid prediction key. Handle: {}. PredictionKey: {}."), *ThisBatch.AbilitySpecHandle.ToString(), *ThisBatch.PredictionKey.ToString());
//			}
//
//			ServerAbilityRPCBatch(ThisBatch);
//		}
//
//		LocalServerAbilityRPCBatchData.RemoveAt(idx, EAllowShrinking::No);
//	}
//	else
//	{
//		FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
//		LOG_WARN("::EndServerAbilityRPCBatch called on ability {} when no batch has been started."), Spec ? *GetNameSafe(Spec->Ability) : GAS_TEXT("INVALID"));
//	}
}

void UAbilitySystemComponent::CallServerTryActivateAbility(FGameplayAbilitySpecHandle AbilityHandle, bool InputPressed, FPredictionKey PredictionKey)
{
    Assert(false);
	//UE_CLOG(AbilitySystemLogServerRPCBatching, LogAbilitySystem, Display, GAS_TEXT("::CallServerTryActivateAbility {} {} {}"), *AbilityHandle.ToString(), InputPressed, *PredictionKey.ToString());
	//
	///** Queue this call up if we are in  a batch window, otherwise just push it through now */
	//if (FServerAbilityRPCBatch* ExistingBatchData = LocalServerAbilityRPCBatchData.FindByKey(AbilityHandle))
	//{
	//	if (ExistingBatchData->Started)
	//	{
	//		FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
	//		LOG_WARN("::CallServerTryActivateAbility called multiple times for ability ({}) during a single batch."), Spec ? *GetNameSafe(Spec->Ability) : GAS_TEXT("INVALID"));
	//		return;
	//	}
	//
	//	ExistingBatchData->Started = true;
	//	ExistingBatchData->InputPressed = InputPressed;
	//	ExistingBatchData->PredictionKey = PredictionKey;
	//}
	//else
	//{
	//	UE_CLOG(AbilitySystemLogServerRPCBatching, LogAbilitySystem, Display, GAS_TEXT("    NO BATCH IN SCOPE"));
	//	ServerTryActivateAbility(AbilityHandle, InputPressed, PredictionKey);
	//}
}

void UAbilitySystemComponent::CallServerSetReplicatedTargetData(FGameplayAbilitySpecHandle AbilityHandle, FPredictionKey AbilityOriginalPredictionKey, const FGameplayAbilityTargetDataHandle& ReplicatedTargetDataHandle, FGameplayTag ApplicationTag, FPredictionKey CurrentPredictionKey)
{
    Assert(false);
	//UE_CLOG(AbilitySystemLogServerRPCBatching, LogAbilitySystem, Display, GAS_TEXT("::CallServerSetReplicatedTargetData {} {} {} {} {}"), 
	//	*AbilityHandle.ToString(), *AbilityOriginalPredictionKey.ToString(), ReplicatedTargetDataHandle.IsValid(0) ? *ReplicatedTargetDataHandle.Get(0)->ToString() : GAS_TEXT("NULL"), *ApplicationTag.ToString(), *CurrentPredictionKey.ToString());
	//
	///** Queue this call up if we are in  a batch window, otherwise just push it through now */
	//if (FServerAbilityRPCBatch* ExistingBatchData = LocalServerAbilityRPCBatchData.FindByKey(AbilityHandle))
	//{
	//	if (!ExistingBatchData->Started)
	//	{
	//		// A batch window was setup but we didn't see the normal try activate -> target data -> end path. So let this unbatched rpc through.
	//		FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
	//		UE_CLOG(AbilitySystemLogServerRPCBatching, LogAbilitySystem, Display, GAS_TEXT("::CallServerSetReplicatedTargetData called for ability ({}) when CallServerTryActivateAbility has not been called"), Spec ? *GetNameSafe(Spec->Ability) : GAS_TEXT("INVALID"));
	//		ServerSetReplicatedTargetData(AbilityHandle, AbilityOriginalPredictionKey, ReplicatedTargetDataHandle, ApplicationTag, CurrentPredictionKey);
	//		return;
	//	}
	//
	//	if (ExistingBatchData->PredictionKey.IsValidKey() == false)
	//	{
	//		FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
	//		LOG_WARN("::CallServerSetReplicatedTargetData called for ability ({}) when the prediction key is not valid."), Spec ? *GetNameSafe(Spec->Ability) : GAS_TEXT("INVALID"));
	//	}
	//
	//
	//	ExistingBatchData->TargetData = ReplicatedTargetDataHandle;
	//}
	//else
	//{
	//	ServerSetReplicatedTargetData(AbilityHandle, AbilityOriginalPredictionKey, ReplicatedTargetDataHandle, ApplicationTag, CurrentPredictionKey);
	//}

}

void UAbilitySystemComponent::CallServerEndAbility(FGameplayAbilitySpecHandle AbilityHandle, FGameplayAbilityActivationInfo ActivationInfo, FPredictionKey PredictionKey)
{
    Assert(false);
	//UE_CLOG(AbilitySystemLogServerRPCBatching, LogAbilitySystem, Display, GAS_TEXT("::CallServerEndAbility {} ({} {}) {}"), *AbilityHandle.ToString(), ActivationInfo.bCanBeEndedByOtherInstance, *ActivationInfo.GetActivationPredictionKey().ToString(), *PredictionKey.ToString());
	//
	///** Queue this call up if we are in  a batch window, otherwise just push it through now */
	//if (FServerAbilityRPCBatch* ExistingBatchData = LocalServerAbilityRPCBatchData.FindByKey(AbilityHandle))
	//{
	//	if (!ExistingBatchData->Started)
	//	{
	//		// A batch window was setup but we didn't see the normal try activate -> target data -> end path. So let this unbatched rpc through.
	//		FGameplayAbilitySpec* Spec = FindAbilitySpecFromHandle(AbilityHandle);
	//		UE_CLOG(AbilitySystemLogServerRPCBatching, LogAbilitySystem, Display, GAS_TEXT("::CallServerEndAbility called for ability ({}) when CallServerTryActivateAbility has not been called"), Spec ? *GetNameSafe(Spec->Ability) : GAS_TEXT("INVALID"));
	//		ServerEndAbility(AbilityHandle, ActivationInfo, PredictionKey);
	//		return;
	//	}
	//
	//	ExistingBatchData->Ended = true;
	//}
	//else
	//{
	//	ServerEndAbility(AbilityHandle, ActivationInfo, PredictionKey);
	//}
}

#undef LOCTEXT_NAMESPACE
}