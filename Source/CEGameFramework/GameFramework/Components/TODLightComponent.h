#pragma once

#include "GameFramework/Components/Component.h"
#include "Runtime/GameWorld/TODLightSystemG.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) TODLightComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(TODLightComponent);
    CEMeta(Reflect) TODLightComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) cross::TODLightConfig GetTODLightConfig() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightConfig(const cross::TODLightConfig& inConfig);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTODLightYear() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightYear(UInt32 inYear);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightYearFloat(float inYear);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTODLightMonth() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightMonth(UInt32 inMonth);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightMonthFloat(float inMonth);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTODLightDay() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightDay(UInt32 inDay);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightDayFloat(float inDay);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTODLightHour() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightHour(UInt32 inHour);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightHourFloat(float inHour);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTODLightMinute() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightMinute(UInt32 inMinute);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightMinuteFloat(float inMinute);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTODLightSecond() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightSecond(UInt32 inSecond);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightSecondFloat(float inSecond);
    CEFunction(Reflect, Cli, ScriptCallable) void OnChangeConfig(bool refreshForce = false);
    CEFunction(Reflect, Cli, ScriptCallable) void SyncAllTODLightComponents();
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightTimeZone(SInt32 inTimeZone);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLightTimeZoneFloat(float inTimeZone);
    CEFunction(Reflect, Cli, ScriptCallable) SInt32 GetTODLightTimeZone() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTimeOfDay(cross::TOD4TimeState);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTimeOfDayOfLatLong(cross::TOD4TimeState, double latitude, double longitude);
    CEFunction(Reflect, Cli, ScriptCallable) void SetTimeOfDayForElapse(UInt32 Year, UInt32 Month, UInt32 Day, UInt32 Hour, UInt32 Minute, UInt32 Second);
    CEFunction(ScriptCallable)
    static cross::TODLightConfig GetConcreteTOD4Moment(const cross::TODLightConfig& inConfig, cross::TOD4TimeState, double latitude, double longitude);

    CEFunction(ScriptCallable)
    cross::TODLightConfig GetConcreteTOD4Moment(const cross::TODLightConfig& inConfig, cross::TOD4TimeState);

    CEFunction(Reflect, Cli, ScriptCallable)
    float ComputeSolarElevationAngleAtCameraPosition() const;
    CEFunction(Reflect, Cli, ScriptCallable)
    float ComputeSolarElevationAngleAtAnyPosition(const cross::Double3& GroundPosition) const;
    CEFunction(Reflect, Cli, ScriptCallable)
    float ComputeSolarElevationAngleAtCameraPositionAnyTime(const cross::TODLightConfig& inConfig) const;

    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;
};
} // namespace cegf
