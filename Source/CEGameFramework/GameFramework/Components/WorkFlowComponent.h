#pragma once
#include "Resource/WorkflowGraphResource.h"
#include "GameFramework/Components/Component.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include "blueprint/details/blueprint_graph_group.h"
#include "blueprint/details/blueprint_graph.h"
#include "blueprint/details/blueprint_graph_function.h"
#include "blueprint/details/blueprint_named_variable.h"
#include "blueprint/details/blueprint_event.h"
#include "blueprint/details/blueprint_context.h"

namespace cegf
{

struct WFEventNameDesc
{
    WFEventNameDesc() : EventName(""){}

    WFEventNameDesc(std::string_view inName, int inSlotIndex = 0) : EventName(inName),SlotIndex(inSlotIndex){}

    std::string_view EventName;
    int SlotIndex = 0;

    bool operator==(const WFEventNameDesc& other) const { return EventName == other.EventName && SlotIndex == other.SlotIndex; }

    struct Hash
    {
        std::size_t operator()(const WFEventNameDesc& key) const
        {
            std::size_t hash = std::hash<std::string_view>()(key.EventName);
            hash ^= std::hash<int>()(key.SlotIndex) << 1;
            return hash;
        }
    };
};

class GAMEFRAMEWORK_API CEMeta(Reflect, Cli) WorkFlowComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(WorkFlowComponent);
    static const inline std::string SK_WORKFLOWRES = "WorkflowResGUID";

    CEMeta(Reflect, Cli)
    WorkFlowComponent();

    WorkFlowComponent(GameObject * owner);

    virtual ~WorkFlowComponent();

    virtual void Init() override;

    virtual void StartGame() override;

    virtual void EndGame() override;

    virtual void Tick(float deltaTime) override;

    virtual void Serialize(SerializeNode & node, SerializeContext & context) const override;

    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;

    virtual void GetReferenceResource(cross::ResourcePtr resource) const override;

    const gbf::logic::UBlueprintGraphGroup* GetBlueprint() const;

    // init the component by WorkFlowResource Ptr, and mWorkFlowResGUID will be set 
    void InitByWorkFlowResource(cross::ResourcePtr res);

    CEMeta(Cli)
    std::string GetWorkflowRes()
    {
        return mWorkFlowResGUID;
    }
    CEMeta(Cli)
    void SetWorkflowRes(std::string resGUID);

public:

    void DeferredEvent(const WFEventNameDesc& inEventName)
    {
        m_events_to_fire[inEventName] = {};
    }

    template<typename... Args>
    void DeferredEvent(const WFEventNameDesc& inEventName, Args&&... args)
    {
        gbf::logic::BlueprintEventParamList param_list = {gbf::machine::VValue{args}...};

        m_events_to_fire[inEventName] = std::forward<gbf::logic::BlueprintEventParamList>(param_list);
    }

    void ImmediateEvent(const WFEventNameDesc& inEventName);

    template<typename... Args>
    void ImmediateEvent(const WFEventNameDesc& inEventName, Args&&... args)
    {
        gbf::logic::BlueprintEventParamList param_list = {gbf::machine::VValue{args}...};
        for (auto& ctx : mAllContext)
        {
            ctx->OnImmediateEvent(inEventName.EventName, std::forward<gbf::logic::BlueprintEventParamList>(param_list), inEventName.SlotIndex);
        }
    }

    void ReceiveDelegate(RttiBase * node, gbf::logic::BlueprintEventParamList param_list, size_t out_slot_index = 0);
    void ReceiveDelegate(RttiBase * node, gbf::machine::VValue _param, size_t out_slot_index = 0);

private:
    // called by InitByWorkFlowEditor and InitByWorkFlowResource
    void InitGraphContext();

    void InitBPGlobalVariables(gbf::logic::UBlueprintContextPtr inContext);

    std::string mWorkFlowResGUID;

    // import object for running the blueprint
    cross::WorkflowGraphResourcePtr mWorkflowGraphResourcePtr;
    std::vector<gbf::logic::UBlueprintContextPtr> mAllContext;

    std::unordered_map<WFEventNameDesc, gbf::logic::BlueprintEventParamList, WFEventNameDesc::Hash> m_events_to_fire;
};

}
