#pragma once
#include "GameFramework/Components/BasicComponent.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "GameFramework/Components/PhysicsComponent.h"
namespace cegf
{

class GAMEFRAMEWORK_API CEMeta(Cli, <PERSON>flect, Puerts) SphereComponent : public PhysicsComponent
{
public:
    StaticMetaClassName(SphereComponent);
    CEMeta(Cli, Reflect)
    SphereComponent();

    virtual void Init() override;

    CEMeta(Cli, Puerts) float GetSphereRadius() const;
    CEMeta(Cli, Puerts) void SetSphereRadius(float inRadius);

protected:
    cross::PhysicsGeometrySphere mSphere;
};

}
