#pragma once
#include <vector>
#include "CrossBase/Math/CrossMath.h"
#include "GameFramework/Components/PrimitiveComponent.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"

namespace cegf
{

template<class T>
[[nodiscard]] T ComputeCurveTangent(float prevTime, const T& prevPoint, float curTime, const T& curPoint, float nextTime, const T& nextPoint, float tension)
{
    T tangent = (1.0f - tension) * ((curPoint - prevPoint) + (nextPoint - curPoint));
    float prevToNextTimeDiff = std::max(1e-4f, nextTime - prevTime);

    return tangent / prevToNextTimeDiff;
}

enum GAMEFRAMEWORK_API CEMeta(Puerts) InterpCurveMode
{
    Linear = 0,
    Cubic = 1,
};

template<class T>
class InterpCurvePoint
{
public:
    float mInVal{};
    T mOutVal{};
    T mArriveTangent{};
    T mLeaveTangent{};

    InterpCurveMode mInterpMode{};

    bool IsCubicKey() const { return mInterpMode == InterpCurveMode::Cubic; }
};

template<class T, class U>
[[nodiscard]] T CubicInterp(T const& p0, T const& t0, T const& p1, T const& t1, U const& a)
{
    auto a2 = a * a;
    auto a3 = a2 * a;

    return T((((2 * a3) - (3 * a2) + 1) * p0) + ((a3 - (2 * a2) + a) * t0) + ((a3 - a2) * t1) + (((-2 * a3) + (3 * a2)) * p1));
}

template<class T, class U>
[[nodiscard]] T CubicInterpDerivative(T const& p0, T const& t0, T const& p1, T const& t1, U const& alpha)
{
    T a = 6.0f * p0 + 3.0f * t0 + 3.0f * t1 - 6.0f * p1;
    T b = -6.f * p0 - 4.f * t0 - 2.f * t1 + 6.f * p1;
    T c = t0;

    U alpha2 = alpha * alpha;

    return T(a * alpha2 + b * alpha + c);
}

template<class T>
class InterpCurve
{
public:
    void AutoSetTangents(float tension, bool stationaryEndpoints);
    T Eval(float const inVal, T const& defaultRes = {}) const;
    T EvalDerivative(float const inVal, T const& defaultRes = {}) const;
    T EvalSecondDerivative(float const inVal, T const& defaultRes = {}) const;

    std::vector<InterpCurvePoint<T>> mPoints{};
};

template<class T>
void InterpCurve<T>::AutoSetTangents(float tension, bool stationaryEndpoints)
{
    int numPoints = static_cast<int>(mPoints.size());
    int lastPoint = numPoints - 1;

    for (int pointId = 0; pointId < numPoints; pointId++)
    {
        // TODO: loop
        int prevId = (pointId == 0) ? 0 : pointId - 1;
        int nextId = (pointId == lastPoint) ? lastPoint : pointId + 1;

        auto& thisPoint = mPoints[pointId];
        auto const& prevPoint = mPoints[prevId];
        auto const& nextPoint = mPoints[nextId];

        switch (thisPoint.mInterpMode)
        {
        case InterpCurveMode::Cubic:
        {
            // TODO: loop
            if (stationaryEndpoints && (pointId == 0 || pointId == lastPoint))
            {
                thisPoint.mArriveTangent = T{};
                thisPoint.mLeaveTangent = T{};
            }
            else if (prevPoint.IsCubicKey())
            {
                float prevTime = prevPoint.mInVal;
                float nextTime = nextPoint.mInVal;

                auto tangent = ComputeCurveTangent(prevTime, prevPoint.mOutVal, thisPoint.mInVal, thisPoint.mOutVal, nextTime, nextPoint.mOutVal, tension);

                thisPoint.mArriveTangent = tangent;
                thisPoint.mLeaveTangent = tangent;
            }

            break;
        }

        case InterpCurveMode::Linear:
        {
            thisPoint.mLeaveTangent = nextPoint.mOutVal - thisPoint.mOutVal;

            thisPoint.mArriveTangent = prevPoint.IsCubicKey() ? thisPoint.mLeaveTangent : thisPoint.mOutVal - prevPoint.mOutVal;

            break;
        }
        }
    }
}

template<class T>
T InterpCurve<T>::Eval(float const inVal, T const& defaultRes) const
{
    int numPoints = static_cast<int>(mPoints.size());
    int lastPoint = numPoints - 1;

    if (numPoints == 0)
    {
        return defaultRes;
    }

    int index = static_cast<int>(std::upper_bound(mPoints.begin(), mPoints.end(), inVal,
        [](float const& inVal, InterpCurvePoint<T> const& pointInfo)
        {
            return inVal < pointInfo.mInVal;
        }) - mPoints.begin()) - 1;

    // std::cerr << "Prev Point Index: " << index << ". ";

    if (index == -1)
    {
        return mPoints[0].mOutVal;
    }

    // TODO： loop
    if (index == lastPoint)
    {
        return mPoints[lastPoint].mOutVal;
    }

    int nextIndex = index + 1;
    auto& prevPoint = mPoints[index];
    auto& nextPoint = mPoints[nextIndex];

    float diff = nextPoint.mInVal - prevPoint.mInVal;

    if (diff > 0.0f)
    {
        float alpha = (inVal - prevPoint.mInVal) / diff;

        if (prevPoint.mInterpMode == InterpCurveMode::Linear)
        {
            return cross::MathUtils::LerpStable(prevPoint.mOutVal, nextPoint.mOutVal, alpha);
        }
        else
        {
            return CubicInterp(prevPoint.mOutVal, prevPoint.mLeaveTangent * diff, nextPoint.mOutVal, nextPoint.mArriveTangent * diff, alpha);
        }
    }
    else
    {
        return mPoints[index].mOutVal;
    }
}

template<class T>
T InterpCurve<T>::EvalDerivative(float const inVal, T const& defaultRes) const
{
    int numPoints = static_cast<int>(mPoints.size());
    int lastPoint = numPoints - 1;

    if (numPoints == 0)
    {
        return defaultRes;
    }

    int index = static_cast<int>(std::upper_bound(mPoints.begin(), mPoints.end(), inVal,
        [](float const& inVal, InterpCurvePoint<T> const& pointInfo)
        {
            return inVal < pointInfo.mInVal;
        }) - mPoints.begin()) - 1;

    if (index == -1)
    {
        return mPoints[0].mLeaveTangent;
    }

    // TODO： loop
    if (index == lastPoint)
    {
        return mPoints[lastPoint].mArriveTangent;
    }

    int nextIndex = index + 1;
    auto& prevPoint = mPoints[index];
    auto& nextPoint = mPoints[nextIndex];

    float diff = nextPoint.mInVal - prevPoint.mInVal;

    if (diff > 0.0f)
    {
        if (prevPoint.mInterpMode == InterpCurveMode::Linear)
        {
            return (nextPoint.mOutVal - prevPoint.mOutVal) / diff;
        }
        else
        {
            float one_over_diff = 1.0f / diff;
            const float alpha = (inVal - prevPoint.mInVal) * one_over_diff;
            return CubicInterpDerivative(prevPoint.mOutVal, prevPoint.mLeaveTangent * diff, nextPoint.mOutVal, nextPoint.mArriveTangent * diff, alpha) * one_over_diff;
        }
    }
    else
    {
        return T{};
    }
}

using InterpCurveFloat = InterpCurve<float>;
using InterpCurveVec3  = InterpCurve<cross::Float3>;
using InterpCurveQuat  = InterpCurve<cross::Quaternion>;

class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, Puerts) SplineComponent : public PrimitiveComponent
{
public:
    StaticMetaClassName(SplineComponent);
    CEMeta(Reflect) SplineComponent() = default;

    virtual void Init() override;

    virtual void Uninit(bool bShouldNotifyECS) override;

    virtual void Tick(float deltaTime) override;

    virtual void StartGame() override;

    virtual void EndGame() override;

    virtual void BeginDestroy() override;

    virtual void Serialize(SerializeNode& node, SerializeContext& context) const override;

    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;

    float GetSplineLength() const;

    void UpdateSpline();

    virtual void Draw() const override;

    cross::Float3 GetLocationAtDistanceAlongSpline(float distance) const;

    cross::Quaternion GetQuaternionAtDistanceAlongSpline(float distance) const;

    cross::Float3 GetLocationAtSplineInputKey(float inKey) const;

    cross::Quaternion GetQuaternionAtSplineInputKey(float inKey) const;
    
    InterpCurveVec3 const& GetSplinePointsPosition() const;

    void SetSplinePointsPosition(InterpCurveVec3 const& positions);

    CEMeta(Cli)
    int GetPointNum() const;

    CEMeta(Cli)
    cross::Float3 const& GetSplinePointsPositionAt(int index) const;

    CEMeta(Cli)
    InterpCurveMode GetSplinePointsInterpModeAt(int index) const;

    CEFunction(Cli, ScriptCallable)
    void SetPointNum(int size);

    // Must call SetPointNum() first then set point one by one. 
    CEFunction(Cli, ScriptCallable)
    void SetSplinePointAt(int index, cross::Float3 const& position = {}, InterpCurveMode mode = InterpCurveMode::Cubic);

    CEMeta(Cli)
    bool GetEndpointsIsStationary() const;

    CEMeta(Cli)
    void SetEndpointsIsStationary(bool isStationaryEndpoints);

    // InterpCurveQuat& GetSplinePointsRotation() { return mRotation; }
    // InterpCurveQuat const& GetSplinePointsRotation() const { return mRotation; }
    // InterpCurveVec3& GetSplinePointsScale() { return mScale; }
    // InterpCurveVec3 const& GetSplinePointsScale() const { return mScale; }

private:
    InterpCurveVec3 mPosition{};
    InterpCurveQuat mRotation{};
    // InterpCurveVec3 mScale{};
    InterpCurveFloat mReparamTable{};

    int mReparamStepsPerSegment{10};
    bool mStationaryEndpoints{false};

    float mDuration{1.0f};
    cross::Float3 mDefaultUpVector{0.0f, 1.0f, 0.0f};

    cross::PrimitiveRenderSystemG* mPrimitiveRenderSystem{nullptr};

    float GetSegmentLength(int const index, float const param, cross::Float3 const& scale3D) const;
};

}
