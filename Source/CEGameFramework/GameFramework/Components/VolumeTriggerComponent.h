#pragma once

#include "GameFramework/Components/Component.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) VolumeTriggerComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(VolumeTriggerComponent);
    CEMeta(Reflect) VolumeTriggerComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) bool GetGenerateOverlap();
    CEFunction(Reflect, Cli, ScriptCallable) void SetGenerateOverlap(const bool& value);
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;
};
} // namespace cegf
