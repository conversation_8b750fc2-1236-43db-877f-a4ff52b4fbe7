#pragma once

#include "GameFramework/Components/Component.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) SkyLightComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(SkyLightComponent);
    CEMeta(Reflect) SkyLightComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) void SetLightMapIntensityDebug(float val);
    CEFunction(Reflect, Cli, ScriptCallable) float GetLightMapIntensityDebug() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetSkyLightIntensity(float val);
    CEFunction(Reflect, Cli, ScriptCallable) float GetSkyLightIntensity() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnable(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetEnable() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetRealTimeCapture(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetRealTimeCapture() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetRealTimeCaptureSliceCount(SInt32 val);
    CEFunction(Reflect, Cli, ScriptCallable) SInt32 GetRealTimeCaptureSliceCount() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetIsLowerHemisphereColor(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetIsLowerHemisphereColor() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTODLowerHemisphereColor(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool GetTODLowerHemisphereColor() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetLowerHemisphereColor(cross::Float4 val);
    CEFunction(Reflect, Cli, ScriptCallable) cross::Float4 GetLowerHemisphereColor() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetLightColor(cross::Float3 inValue);
    CEFunction(Reflect, Cli, ScriptCallable) void SetDiffuseProbe(cross::Float4 param, int index);
    CEFunction(Reflect, Cli, ScriptCallable) void SetSpecularProbe(std::string path);
    CEFunction(Reflect, Cli, ScriptCallable) void ResetSystemState();
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;
};
} // namespace cegf
