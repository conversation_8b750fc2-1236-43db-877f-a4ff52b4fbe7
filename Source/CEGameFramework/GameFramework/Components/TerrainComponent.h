#pragma once

#include "Resource/TerrainResource.h"
#include "GameFramework/Components/Component.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) TerrainComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(TerrainComponent);
    CEMeta(Reflect) TerrainComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) bool GetTerrainEnable() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetTerrainEnable(bool enable);
    CEFunction(Reflect, Cli, ScriptCallable) cross::TerrainSurfaceType GetSurfaceType() const;
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetGridSizeX() const;
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetGridSizeY() const;
    CEFunction(Reflect, C<PERSON>, ScriptCallable) UInt32 GetBlockSize() const;
    CEFunction(Reflect, C<PERSON>, ScriptCallable) UInt32 GetTileSize() const;
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTextureSize() const;
    CEFunction(Reflect, Cli, ScriptCallable) float GetWGS84SemiMajor() const;
    CEFunction(Reflect, Cli, ScriptCallable) cross::Float3 GetWorldScale() const;
    CEFunction(Reflect, Cli, ScriptCallable) cross::Float3 GetWorldTranslation() const;
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetGridDimX() const;
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetGridDimY() const;
    CEFunction(Reflect, Cli, ScriptCallable) std::string GetTerrainPath();
    CEFunction(Reflect, Cli, ScriptCallable) void SetTerrainPath(const std::string& terrainPath);
    CEFunction(Reflect, Cli, ScriptCallable) cross::TerrainInfo GetTerrainInfo() const;
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetNumBlendLayers() const;
    CEFunction(ScriptCallable) UInt64 CreateCustomTerrainOverride(cross::Float2 latRange, cross::Float2 lonRange);
    CEFunction(ScriptCallable) void RemoveCustomTerrainOverride(UInt64 handle);
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;
};
} // namespace cegf
