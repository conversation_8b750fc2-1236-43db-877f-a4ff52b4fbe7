#pragma once
#include "AudioEnums.h"
#include "GameFramework/Components/Component.h"
#include "GameFramework/GameFrameworkGlobals.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Refle<PERSON>, <PERSON><PERSON><PERSON>, Cli) AudioComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(AudioComponent);
    CEMeta(Reflect)
    AudioComponent();
    virtual ~AudioComponent();
    virtual void Serialize(SerializeNode& node, SerializeContext& context) const override;
    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;

    cross::AudioEngine::ObjectID GetObjectID() const;

    virtual void StartGame() override;
    virtual void EndGame() override;

    // script only
    CEFunction(ScriptCallable) bool AddListener(AudioComponent * listener) const;
    CEFunction(ScriptCallable) bool RemoveListener(AudioComponent * listener) const;
    CEFunction(ScriptCallable) bool SetVolume(AudioComponent * listener, float const volume) const;
    CEFunction(ScriptCallable) bool Move(cross::Float3 const& position, cross::Float3 const& forward, cross::Float3 const& up) const;
    CEFunction(ScriptCallable) bool Move(cross::Double3 const& position, cross::Double3 const& forward, cross::Double3 const& up) const;
    CEFunction(ScriptCallable) cross::AudioEngine::EventPtr Post(std::string const & event_name
        , cross::AudioEngine::EndCallback const & end_callback = nullptr
        , cross::AudioEngine::MarkerCallback const & marker_callback = nullptr) const;
    CEFunction(ScriptCallable) cross::AudioEngine::EventPtr Post(cross::AudioEngine::EventID const & event_id
        , cross::AudioEngine::EndCallback const & end_callback = nullptr
        , cross::AudioEngine::MarkerCallback const & marker_callback = nullptr) const;
    CEFunction(ScriptCallable) bool Stop(cross::AudioEngine::EventPtr const event_ptr, int const duration_ms, AudioCurveInterpolation const curve) const;
    CEFunction(ScriptCallable) bool StopAll() const;
    CEFunction(ScriptCallable) bool Set(std::string const & param_name, float const value, int const duration_ms = 0, AudioCurveInterpolation const curve = AudioCurveInterpolation::Linear) const;
    CEFunction(ScriptCallable) bool Set(cross::AudioEngine::ParamID const & param_id, float const value, int const duration_ms = 0, AudioCurveInterpolation const curve = AudioCurveInterpolation::Linear) const;
    CEFunction(ScriptCallable) bool Set(cross::AudioEngine::EventPtr const event_ptr, std::string const & param_name, float const value, int const duration_ms = 0, AudioCurveInterpolation const curve = AudioCurveInterpolation::Linear) const;
    CEFunction(ScriptCallable) bool Set(cross::AudioEngine::EventPtr const event_ptr, cross::AudioEngine::ParamID const & param_id, float const value, int const duration_ms = 0, AudioCurveInterpolation const curve = AudioCurveInterpolation::Linear) const;
    CEFunction(ScriptCallable) bool Reset(std::string const & param_name, int const duration_ms = 0, AudioCurveInterpolation const curve = AudioCurveInterpolation::Linear) const;
    CEFunction(ScriptCallable) bool Reset(cross::AudioEngine::ParamID const & param_id, int const duration_ms = 0, AudioCurveInterpolation const curve = AudioCurveInterpolation::Linear) const;
    CEFunction(ScriptCallable) bool Switch(std::string const & switch_name, std::string const & switch_value) const;
    CEFunction(ScriptCallable) bool Switch(cross::AudioEngine::SwitchID const & switch_id, cross::AudioEngine::SwitchValueID const & switch_value) const;

public: // script and editor
    CEFunction(Cli, ScriptCallable) bool GetIsSpatialListener() const;
    CEFunction(Cli, ScriptCallable) void SetIsSpatialListener(bool value);
    CEFunction(Cli, ScriptCallable) std::string GetBank() const;
    CEFunction(Cli, ScriptCallable) void SetBank(std::string const & value);

private:
    // variables
    bool mIsSpatialListener = false;
    std::string mBank = "";
    int mSpatialListenerRefCount = 0;

    // friend class
    friend class AudioComponent;
};

using AudioComponentPtr = std::shared_ptr<AudioComponent>;
}
