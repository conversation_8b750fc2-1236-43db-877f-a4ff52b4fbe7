#pragma once
#include "GameFramework/Components/Component.h"
#include "Math/CrossMath.h"

namespace cegf {

enum class CEMeta(Cli) CmCameraType
{
    TRACK,
    DOLLY,
    FREELOOK,
    ANIMATED
};

enum class CEMeta(Cli) CmCameraPositionMode
{
    LOCAL,      // keep transform the same with binding node
    WORLD,      // only keep position the same
    NO_ROLL,    // keep transform the same except roll with binding node
    WORLD_UP,   // always world up, but follow binding mode, only yaw is valid
};

enum class CEMeta(Cli) CmCameraDollyMode
{
    NONE,        // manually set position on the track
    ANIMATION,   // animate camera on the track
    AUTOTRACK    // automatic find best place on the track
};

enum class CEMeta(Cli) CmCameraDollyUnit
{
    NORMALIZED,   // index for each node
    RESOLUTION,   // track resolution
    WORLD,        // world distance
};

struct CmCamera
{
    CE_Serialize_Deserialize;

    virtual ~CmCamera() = default;

    CEProperty(Serialize)
    CmCameraType type;

    // property
    CEProperty(Serialize)
    float focal_length = 50.0f;

    // position
    CEProperty(Serialize)
    CmCameraPositionMode position_mode;
    CEProperty(Serialize)
    cross::Double3 position_offset;
    CEProperty(Serialize)
    cross::Double3 position_damping;
    CEProperty(Serialize)
    cross::Double3 rotation_damping;

    CEProperty(Serialize)
    cross::Double3 rig_height = cross::Double3(40.0, 20.0f, 0.0f);     // top, middle, bottom
    CEProperty(Serialize)
    cross::Double3 rig_radius = cross::Double3(20.0f, 40.0f, 20.0f);   // top, middle, bottom
    CEProperty(Serialize)
    cross::Double2 rig_control = cross::Double2(0.0f, 0.5f);   // normalized range [0, 1] from bottom to top

    CEProperty(Serialize)
    CmCameraDollyMode dolly_mode = CmCameraDollyMode::NONE;
    CEProperty(Serialize)
    CmCameraDollyUnit dolly_unit = CmCameraDollyUnit::NORMALIZED;
    CEProperty(Serialize)
    uint32_t dolly_resolution = 10;
    CEProperty(Serialize)
    float dolly_distance = 0.0f;
    CEProperty(Serialize)
    float dolly_speed = 0.0f;
    CEProperty(Serialize)
    float dolly_search_offset = 0.0f;
    CEProperty(Serialize)
    uint32_t dolly_search_radius = 2.0f;
    CEProperty(Serialize)
    bool dolly_loop = false;
    CEProperty(Serialize)
    bool dolly_paused = false;   // only for animation

    // aiming
    CEProperty(Serialize)
    cross::Double3 aim_offset;
    CEProperty(Serialize)
    cross::Double2 aim_damping = cross::Double2(0.0f, 0.0f);       // XY
    CEProperty(Serialize)
    cross::Double2 center_position = cross::Double2(0.5f, 0.5f);   // XY
    CEProperty(Serialize)
    cross::Double2 dead_zone = cross::Double2(0.0f, 0.0f);         // WH
    CEProperty(Serialize)
    cross::Double2 soft_zone = cross::Double2(0.8f, 0.8f);   // WH
    CEProperty(Serialize)
    cross::Double2 zone_bias = cross::Double2(0.0f, 0.0f);   // XY

    // animation
    //AssetsHandle<CameraAnim> animation;
    //float anim_speed = 1.0f;
    //bool anim_loop = false;
    //bool anim_paused = false;

    // control
    CEProperty(Serialize)
    bool always_update = true;   // always update even if not activated
    CEProperty(Serialize)
    bool confiner_keep_aim = false;
};

class GAMEFRAMEWORK_API CEMeta(Reflect, Cli) CmCameraComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(CmCameraComponent);
    CEMeta(Reflect)
    CmCameraComponent();

    virtual ~CmCameraComponent();

    virtual void Tick(float deltaTime);

    CEMeta(Cli)
    CmCameraType GetCmCameraType() const
    {
        return mCmCamera->type;
    }

    void Serialize(SerializeNode& node, SerializeContext& context) const override;

    bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;

    auto SetProgress(float progress)->void;
    auto GetProgress()->float;

private:
    auto PrepareCmCamera(float deltaTime) -> bool;
    auto PreparePosition(float deltaTime) -> bool;
    auto PrepareAim() -> bool;
    auto ConfinePosition(cross::Transform_D transform) -> cross::Transform_D;

    auto PrepareDollyPosition(float deltaTime) -> bool;
    auto PrepareAnimation(float deltaTime) -> bool;
    auto PrepareNormalPosition() -> bool;

    auto RecomputeDollyTrack() -> void;
    auto GetBoneMatrix(GameObject* gameObject, const std::string& bone_name) -> cross::Double4x4;
    auto GetAutoDolly() -> std::pair<int, float>;
    auto GetDollyInfo(int unit, float dist) -> std::pair<int, float>;
    std::unique_ptr<CmCamera> mCmCamera;
    friend class CinemachineComponent;
    friend struct CmCameraEditorBase;
};

using CmCameraComponentPtr = std::shared_ptr<CmCameraComponent>;

}   // namespace cegf
