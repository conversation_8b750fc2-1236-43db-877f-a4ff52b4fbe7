#pragma once
#include "GameFramework/Components/Component.h"
#include "Math/CrossMath.h"

namespace cegf {

enum class CEMeta(Cli) CmBrainCameraApplyType
{
    Single,   // Apply single CmCamera
    Blend,
    Mix
};

enum class CEMeta(Cli) CmBrainBlendType
{
    Cut,
    Linear,
    EaseInOut,
    EaseIn,
    EaseOut,
    HardIn,
    HardOut
};

struct CEMeta(Cli) CmBrain
{
    CE_Serialize_Deserialize;
    virtual ~CmBrain() = default;
    CEProperty(Serialize)
    uint32_t priority;
    CEProperty(Serialize)
    CmBrainCameraApplyType type;
    CEProperty(Serialize)
    CmBrainBlendType blend_type;
    CEProperty(Serialize)
    float blend_time;
};

class GAMEFRAMEWORK_API CEMeta(Reflect, Cli) CinemachineComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(CinemachineComponent);
    CEMeta(Reflect)
    CinemachineComponent();

    virtual ~CinemachineComponent();

    virtual void Tick(float deltaTime);

    CEMeta(Cli)
    CmBrainCameraApplyType GetCameraApplyType() const
    {
        return mCmBrain->type;
    }

    void Serialize(SerializeNode& node, SerializeContext& context) const override;
    bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;

private:
    auto VerifyEntities(float elapsed_time) -> void;
    auto RemoveCmcameraNotChild() -> void;
    auto PrepareCmCameras(float elapsed_time) -> void;
    auto PrepareCmBrains() -> void;

    auto ApplySingle() -> void;
    auto ApplyBlend() -> void;
    auto ApplyMix() -> void;

    std::unique_ptr<CmBrain> mCmBrain;
    friend struct CmBrainEditorBase;
};

using CinemachineComponentPtr = std::shared_ptr<CinemachineComponent>;

}   // namespace cegf
