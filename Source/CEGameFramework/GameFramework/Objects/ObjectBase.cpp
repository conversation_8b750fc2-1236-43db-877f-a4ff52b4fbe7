#include "GameFramework/Objects/ObjectBase.h"
#include "TypeScriptEngine/TypeScriptModule.h"
namespace cegf
{

std::atomic<UInt32> ObjectBase::ObjectNameIndex = 0;

std::string ObjectBase::MakeUniqueObjectName(const gbf::reflection::MetaClass* metaClass)
{
    //meta class name include namespace, remove this part
    std::string className = metaClass->name();
    auto const pos = className.find_last_of(static_cast<const char>('::'));
    const auto subStr = className.substr(pos + 1);
    return std::format("{}_{}", subStr, ++ObjectNameIndex);
}

std::string ObjectBase::MakeUniqueObjectName(const std::string& className)
{
    return std::format("{}_{}", className, ++ObjectNameIndex);
}

auto ObjectBase::MixinTypescript(const std::string& typescriptClass) -> bool
{
    if (mTSObject)
    {
        return false;
    }
    auto typeScriptModule = cross::TypeScriptModule::Instance();
    mTSObject = typeScriptModule->ConvertToTypeScriptObject(PUERTS_NAMESPACE::DynamicTypeId<ObjectBase>::get(this), this);
    return typeScriptModule->MixinTypescript(mTSObject, typescriptClass);
}

}
