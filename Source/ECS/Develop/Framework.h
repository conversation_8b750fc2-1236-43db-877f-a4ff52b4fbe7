#pragma once
#include <initializer_list>
#include "CECommon/Common/EngineGlobal.h"
#include "Template/TypeDemangle.hpp"
#include "Template/ArrayList.hpp"
#include "ECS/ecsforward.h"
#include "ECS/Develop/Framework/Types.h"
#include "ECS/Develop/Framework/PrototypeInterface.h"
#include "ECS/Develop/Framework/EntityInterface.h"
#include "ECS/Develop/Framework/QueryInterface.h"
#include "ECS/Develop/Framework/EventInterface.h"
#include "ECS/Develop/Framework/DataTable.h"
#include "ECS/Develop/ECSFrameworkConst.h"
#include "Threading/Task.h"
#include "Threading/TaskSystem.h"

namespace cross::ecs {
template<typename T, typename = void>
struct CompHasGetDesc : std::false_type
{};
template<typename T>
struct CompHasGetDesc<T, std::void_t<decltype(std::declval<T>().GetDesc())>> : std::true_type
{};

// The only purpose of ComponentDescProxy is create an instance of ComponentDesc
struct ComponentDescProxy
{
    ComponentDescProxy(bool isGameComponent, bool isRenderComponent, 
        const char* name, std::type_index ID, UInt32 componentSize, UInt16 aligment, UInt16 componentTypeFlag, SInt32 maskBit, LifeCycleFunction function, bool isBuildInComponent = false)
        : Desc(isGameComponent, isRenderComponent, name, ID, componentSize, aligment, componentTypeFlag, maskBit, function, isBuildInComponent)
    {}

    ComponentDesc Desc;
};

class Framework final
{
    friend class EntityPool;
    using GameStorePtr = std::unique_ptr<GameStore>;
    using RenderStorePtr = std::unique_ptr<RenderStore>;
    using GameStoreContainer = std::vector<GameStorePtr>;
    using RenderStoreContainer = std::vector<RenderStorePtr>;

protected:
    Framework() = default;

public:
    ~Framework() = default;
    Framework(Framework const&) = delete;
    Framework& operator=(Framework const&) = delete;
    Framework(Framework&&) = delete;

public:
    ECS_API void Init();

    ECS_API const ComponentDescMap& GetComponentTypesRegistry() const noexcept
    {
        return mCompDescMap;
    }
    ECS_API PrototypeRegistry& GetPrototypeRegistry() noexcept
    {
        return mPrototypes;
    }

private:
    template<typename Component, typename Parent = void>
    ComponentDesc* GetOrCreateComponentDesc(bool isGameComp, bool isRenderComp, bool isBuildInComponent = false);

public:
    template<typename GameComponent, typename Parent = void>
    ecs::ComponentDesc* CreateOrGetGameComponentDesc(ComponentDesc::ComponentConfig config, ecs::ComponentDesc::ComponentSerializeFunc serializeFunc = ecs::ComponentDesc::ComponentSerializeFunc(),
                                                     ecs::ComponentDesc::ComponentDeserializeFunc deserializeFunc = ecs::ComponentDesc::ComponentDeserializeFunc(),
                                                     ecs::ComponentDesc::ComponnetPostDeserializeFunc postDeserializeFunc = ecs::ComponentDesc::ComponnetPostDeserializeFunc(),
                                                     ecs::ComponentDesc::ComponnetUpdateDeserializeDeFunc updateDeserializeFunc = ecs::ComponentDesc::ComponnetUpdateDeserializeDeFunc(),
                                                     ecs::ComponentDesc::ComponnetGetResourceFunc getResourceFunc = ecs::ComponentDesc::ComponnetGetResourceFunc())
    {
        auto sDesc = EngineGlobal::GetECSFramework().GetOrCreateComponentDesc<GameComponent, Parent>(true, false);
        sDesc->Init(config, serializeFunc, deserializeFunc, postDeserializeFunc, updateDeserializeFunc, getResourceFunc);
        return sDesc;
    }

        template<typename GameComponent, typename Parent = void>
    ecs::ComponentDesc* CreateOrGetGameComponentDesc(ComponentDesc::ComponentConfig config, ecs::ComponentDesc::ComponentSerializeFuncSimple serializeFunc = ecs::ComponentDesc::ComponentSerializeFuncSimple(),
                                                     ecs::ComponentDesc::ComponentDeserializeFunc deserializeFunc = ecs::ComponentDesc::ComponentDeserializeFunc(),
                                                     ecs::ComponentDesc::ComponnetPostDeserializeFunc postDeserializeFunc = ecs::ComponentDesc::ComponnetPostDeserializeFunc(),
                                                     ecs::ComponentDesc::ComponnetUpdateDeserializeDeFunc updateDeserializeFunc = ecs::ComponentDesc::ComponnetUpdateDeserializeDeFunc(),
                                                     ecs::ComponentDesc::ComponnetGetResourceFunc getResourceFunc = ecs::ComponentDesc::ComponnetGetResourceFunc())
    {
            auto sDesc = EngineGlobal::GetECSFramework().GetOrCreateComponentDesc<GameComponent, Parent>(true, false);
        sDesc->Init(
            config,
            [serializeFunc](ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ecs::EntityID entityId) -> SerializeNode { return serializeFunc(serializeWorld, componentPtr); },
            deserializeFunc,
            postDeserializeFunc,
            updateDeserializeFunc,
            getResourceFunc);
        return sDesc;
    }

    template<typename RenderComponent>
    ecs::ComponentDesc* CreateOrGetRenderComponentDesc(bool isEditorOnlyComp)
    {
        auto sDesc = EngineGlobal::GetECSFramework().GetOrCreateComponentDesc<RenderComponent>(false, true);
        sDesc->Init({ isEditorOnlyComp, false, false });
        return sDesc;
    }

    ECS_API ecs::ComponentDesc* GetGameComponentDescByIndex(UInt32 maskBitIndex);

    ECS_API ecs::ComponentDesc* GetRenderComponentDescByIndex(UInt32 maskBitIndex);

    ECS_API ecs::ComponentDesc* GetIDComponentDesc();

    ECS_API ecs::ComponentDesc* GetMetaComponentDesc();

private:
    ComponentDescMap mCompDescMap;
    CEHashMap<std::type_index, ComponentDesc*> mCompDescTypeMap;
    PrototypeRegistry mPrototypes;
    ComponentDescProxy* mIDComponentDesc{nullptr};
    std::vector<ComponentDescProxy> mGameComponentDescs;
    std::vector<ComponentDescProxy> mRenderComponentDescs;
    UInt32 mGameComponentCount{1};
    UInt32 mRenderComponentCount{1};

    friend class ::cross::CrossEngine;
};

inline const ecs::ComponentDesc* GetComponentDescByHash(StringHash32 metahash)
{
    auto& framework = EngineGlobal::GetECSFramework();
    auto itr = framework.GetComponentTypesRegistry().find(metahash);
    if (itr != framework.GetComponentTypesRegistry().end())
    {
        return itr->second;
    }
    return nullptr;
}

template<typename... Components, typename = TEnableIfAreAllDifferentComponents<AtLeastOne, Components...>>
inline PrototypePtr GetOrCreatePrototypeG()
{
    return EngineGlobal::GetECSFramework().GetPrototypeRegistry().GetOrCreatePrototype<EntityIDComponent, EntityMetaComponentG, Components...>(true);
}

inline PrototypePtr GetOrCreatePrototypeG(ecs::ComponentBitMask& mask)
{
    return EngineGlobal::GetECSFramework().GetPrototypeRegistry().GetOrCreatePrototypeImp(mask, true);
}

template<typename... Components, typename = TEnableIfAreAllDifferentComponents<AtLeastOne, Components...>>
inline PrototypePtr GetOrCreatePrototypeR()
{
    return EngineGlobal::GetECSFramework().GetPrototypeRegistry().GetOrCreatePrototype<EntityIDComponent, Components...>(false);
}

inline PrototypePtr GetOrCreateProtypeR(ecs::ComponentBitMask& mask)
{
    return EngineGlobal::GetECSFramework().GetPrototypeRegistry().GetOrCreatePrototypeImp(mask, false);
}

inline PrototypePtr MergePrototype(std::initializer_list<const PrototypePtr> prototypes)
{
    return EngineGlobal::GetECSFramework().GetPrototypeRegistry().MergePrototype(prototypes);
}

inline PrototypePtr CalculatePrototypeSub(PrototypePtr const& srcType, PrototypePtr const& subType)
{
    return EngineGlobal::GetECSFramework().GetPrototypeRegistry().CalculatePrototypeSub(srcType, subType);
}

template<typename Container, typename = std::enable_if_t<TIsSequentialContainerOfV<Container, ComponentDesc*>>>
inline PrototypePtr GetOrCreatePrototype(Container const& container)
{
    return EngineGlobal::GetECSFramework().GetPrototypeRegistry().GetOrCreatePrototype(container.data(), container.size());
}

// This function only perform bit mask check without verifying game/render component
template<typename T>
bool HasComponentMask(const ComponentBitMask& mask)
{
    return mask[T::GetDesc()->GetMaskBitIndex()];
}

ECS_API UInt8* GetComponentPtrFromChunk(Chunk* chunk, ComponentIndex const componentIndex, EntityOffset const chunkIndex);

ECS_API UInt8* GetComponentPtrFromChunk(Chunk const* chunk, ComponentIndex const componentIndex, EntityOffset const chunkIndex);

template<typename Component>
inline auto AddComponentData(ComponentIndex const compIdx, Chunk* chunk, Component&& component) -> std::enable_if_t<TIsDataComponentV<TRemoveRCVT<Component>>>
{
    using ComponentType = TRemoveRCVT<Component>;
    EntityOffset ettOffset;
    if (chunk->mPolicy == ChunkStoragePolicy::FreeList)
    {
        auto holeIndex = (chunk->mEntityMask)[static_cast<int>(RuntimeMaskType::ProtoTypeHolelist)].Findfirstbit(true);
        ettOffset = holeIndex > 0 ? holeIndex : chunk->ElementCount;
    }
    else
    {
        ettOffset = chunk->ElementCount;
    }
    auto componentDataPtr = GetComponentPtrFromChunk(chunk, compIdx, ettOffset);
    if (componentDataPtr)
    {
        new (componentDataPtr) ComponentType{std::forward<Component>(component)};
    }
}

/// Return component ptr
template<typename Component>
inline TGetComponentTypeT<Component> AddComponentDataAndReturn(EntityID entity, ComponentIndex const compIdx, Chunk* chunk)
{
    using ComponentType = TRemoveRCVT<Component>;

    EntityOffset ettOffset;
    if (chunk->mPolicy == ChunkStoragePolicy::FreeList)
    {
        auto holeIndex = (chunk->mEntityMask)[static_cast<int>(RuntimeMaskType::ProtoTypeHolelist)].Findfirstbit(true);
        ettOffset = holeIndex > 0 ? holeIndex : chunk->ElementCount;
    }
    else
    {
        ettOffset = chunk->ElementCount;
    }
    auto componentDataPtr = GetComponentPtrFromChunk(chunk, compIdx, ettOffset);
    DEBUG_ASSERT(componentDataPtr);
    new (componentDataPtr) ComponentType{};
    auto pointer = TGetComponentTypeT<Component>::From(reinterpret_cast<Component*>(componentDataPtr), GetIDComponent(chunk, ettOffset));
    return pointer;
}

template<typename Component>
inline typename TGetConstComponentType<Component>::RawPointerType GetComponentDataRawImpl(Chunk const* chunk, EntityOffset index, UInt32& compIdx)
{
     using PointerType = typename TGetConstComponentType<Component>::RawPointerType;
    // get type info
    // cache prototype pointer
    auto prototype = chunk->Type;
        // check for each component and return
    using Sanitized = std::remove_const_t<std::remove_reference_t<Component>>;
    auto typeindex = std::type_index(typeid(Sanitized));
    ComponentIndex loop{0};
    if constexpr (TIsProxyComponentV<Component>)
    {
        for (; loop < prototype->ComponentCount; ++loop)
        {
            auto& dataInfo = prototype->Components[loop];
            if (dataInfo.Desc->TypeId == typeindex)
            {
                compIdx = loop.mVal;
                break;
            }
            else
            {
                if (std::any_of(dataInfo.Desc->InheritanceInformation.AllChidren.begin(), dataInfo.Desc->InheritanceInformation.AllChidren.end(), [typeindex = typeindex](std::type_index type)
                {
                    return type == typeindex; 
                }))
                {
                    compIdx = loop.mVal;
                    break;
                }
            }
        }

    }
   
    else
    {
        for (; loop < prototype->ComponentCount; ++loop)
        {
            auto& dataInfo = prototype->Components[loop];
            if (dataInfo.Desc->TypeId == typeindex)
            {
                compIdx = loop.mVal;
                break;
            }
        }
    }
    // return null if not found
    if (loop == prototype->ComponentCount)
    {
        return nullptr;
    }

    auto* dataPtr = GetComponentPtrFromChunk(chunk, loop, index);
    return reinterpret_cast<typename TGetConstComponentType<Component>::RawPointerType>(dataPtr);
}
template<typename Component>
inline auto GetComponentData(Chunk* chunk, EntityOffset index) -> TGetComponentTypeT<Component>
{
    std::uint32_t compIdx = 0;
    auto pointer = GetComponentDataRawImpl<Component>(const_cast<Chunk const*>(chunk), index, compIdx);
    auto guard = GetDataRaceGuardPtr(chunk, index, compIdx);
    auto* idComponent = GetIDComponent(chunk, index);

    if constexpr (std::is_const_v<Component>)
    {
        return TGetComponentTypeT<Component>::From(pointer, guard, idComponent);
    }
    else
    {
        auto nonconstptr = const_cast<typename TGetComponentType<Component>::RawPointerType>(pointer);
        return TGetComponentTypeT<Component>::From(nonconstptr, guard, idComponent);
    }
}

template<typename Component>
inline auto GetComponentIndex(Prototype const* prototype) -> std::enable_if_t<TIsDataComponentV<Component>, ComponentIndex>
{
    auto* compDesc = Component::GetDesc();

    // check for each component and return
    ComponentIndex loop{1};
    for (; loop < prototype->ComponentCount; ++loop)
    {
        auto& dataInfo = prototype->Components[loop];
        if (dataInfo.Desc->Name == compDesc->Name)
        {
            return loop;
        }
    }

    return ComponentIndex::InvalidHandle();
}

size_t GetComponentIndex(Prototype const* prototype, ComponentDesc const* type);

// entity
inline EntityHandle GetEntityHandle(EntityID entity) noexcept
{
    return entity.GetHandle();
}

// type info
// the size of the type
// alignment attribute
// name of the type
// first 32-bits part of the hash value
// constructor, destructor & swap function
// the number of field
// allocate a runtime type information object
TypeInfo* AllocateTypeInfo(UInt32 size, UInt32 alignment, std::string name, UInt32 nameHash, LifeCycleFunction functions, UInt32 fieldCount);
// free a runtime type information object
void FreeTypeInfo(TypeInfo* info);

// chunk interface
Chunk* AllocateChunk();

void FreeChunk(Chunk* chunk);
// clear all the data in the chunk, NOT to delete the chunk
void ClearChunk(Chunk* chunk);

template<typename T>
constexpr LifeCycleFunction GetLifeCycleFunctions() noexcept
{
    LifeCycleFunction functions{0};

    if constexpr (ComponentIsTag<T>::value == false)
    {
        // calculate constructor
        if constexpr (std::negation_v<std::is_trivially_constructible<T>>)
        {
            functions.Ctor = [](void* address) {
                Assert(address);
                new (address) T{};
            };
        }

        functions.Move = [](void* dest, void* src) {
            Assert(dest);
            Assert(src);
            *reinterpret_cast<T*>(dest) = std::move(*reinterpret_cast<T*>(src));
        };

        if constexpr (std::negation_v<std::is_trivially_destructible<T>>)
        {
            functions.Dtor = [](void* address) {
                Assert(address);
                reinterpret_cast<T*>(address)->~T();
            };
        }
        return functions;
    }
    else
    {
        return functions;
    }
}

constexpr LifeCycleFunction GetEmptyLifeCycleFunctions() noexcept
{
    LifeCycleFunction lcfunctions = {nullptr, nullptr, nullptr, nullptr};
    return lcfunctions;
};
}   // namespace cross::ecs

#include "ECS/Develop/Framework/Framework.inl"
