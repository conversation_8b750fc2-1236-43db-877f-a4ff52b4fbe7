#ifndef SHADER_PASS_FORWARD
#define SHADER_PASS_FORWARD

#include "../Lighting/TransparentShadingUtils.hlsl"
#include "Material/Lit/LitUEVariables.hlsl"

VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}

#ifdef CUSTOM_PS_MAIN
// user defined custom pxiel shader 
#else

#include "../Features/Fog/FogCommon.hlsl"

#include "../Features/SkyCloud/SkyCloudFog.hlsl"


float GetNdcDepth(float linearDepth){
    float4 view = float4(0.f, 0.f, linearDepth, 1.f);
    float4 ndc = mul(ce_Projection, view);
    ndc /= max(ndc.w, 1e-7);
    return ndc.z;
}

TransparentPSOutput PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace) 
{
	PSInput input = VSOutputToPSInput(vsoutput);
	PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);
    posInput.positionSS = input.positionNDC.xy;
    posInput.deviceDepth = input.positionNDC.z;

	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	SurfaceData surfaceData;
	BuiltinData builtinData;
#if defined(INSTANCING) && INSTANCING == 1 || defined(CE_INSTANCING)
	GetSurfaceAndBuiltinData(V, input, isFrontFace, input.instanceID, surfaceData, builtinData);
#else
	GetSurfaceAndBuiltinData(V, input, isFrontFace, 0, surfaceData, builtinData);
#endif
 
	BSDFData bsdfData = ConvertSurfaceDataToBSDFData(surfaceData);

	LightLoopOutput lightLoopOutput = (LightLoopOutput)0;
	if (MATERIAL_TYPE != MaterialType_Unlit)
	{
		lightLoopOutput = LightLoop(V, posInput, bsdfData, builtinData, input);
	}
	
	float3 directLighting = lightLoopOutput.diffuseLighting + lightLoopOutput.specularLighting + surfaceData.emissiveColor;
	float3 indirectLighting = builtinData.indirectLighting;
	float4 outColor = float4(directLighting + indirectLighting, surfaceData.opacity);
 
	// Blend with fog : ----------------  
	float3 pinAbsWS = posInput.positionWS.xyz;
    float3 cameraAbsWS = ce_CameraPos.xyz;

	float linearDepth = LinearEyeDepth(posInput.deviceDepth, ce_Projection);

    float4 FogColor = float4(0.0, 0.0 ,0.0, 1.0);


    if (useSFog && !builtinData.ForceDisableExponentialFog)
	{
#if ENABLE_MULTI_LAYER_FOG
        FogColor = GetScreenFogResult(pinAbsWS, cameraAbsWS, 0, UseWGS84, linearDepth);
#else
		float3 positionView = mul(ce_View, posInput.positionWS).xyz;
        FogColor = GetScreenFogResult(useVFog, pinAbsWS, cameraAbsWS, UseWGS84, positionView);
		if (useVFog)
		{
			float4 vFog = GetVolumetricFogResult(posInput.uv, linearDepth);
			FogColor = float4(FogColor.xyz * vFog.w + vFog.xyz, FogColor.w * vFog.w);
		}
#endif

        //outColor.xyz = outColor.xyz * sFog.a + sFog.rgb;
		//outColor.a = 1.f - sFog.a * (1.f - outColor.a);
	}

    FogColor = GetCloudFog(linearDepth, input.screenUV, FogColor, builtinData.ForceDisableCloudFog);

#if defined(MATERIAL_WORKS_WITH_DUAL_SOURCE_COLOR_BLENDING)
	// Add fog luminance according to surfacecoverage and reduce surface luminance according to fog coverage.
	half3 AdjustedDualBlendAdd = DualBlendSurfaceCoverage * VertexFog.rgb + VertexFog.a * DualBlendSurfaceLuminancePostCoverage;
	// Fade the surface color transmittance out to 1 according to the surface coverage, and take into account the fog coverage to the surface.
	half3 AdjustedDualBlendMul = lerp(1.0f, VertexFog.a * DualBlendSurfaceTransmittancePreCoverage, DualBlendSurfaceCoverage);

	#if DUAL_SOURCE_COLOR_BLENDING_ENABLED
		OutColor = half4(AdjustedDualBlendAdd,0.0);
		OutColor1 = half4(AdjustedDualBlendMul,1.0);
	#else
		// In the fallback case, use standard alpha blending(grey scale transmittance)
		half AdjustedAlpha = saturate(1 - dot(AdjustedDualBlendMul, half3(1.0f, 1.0f, 1.0f) / 3.0f));
		outColor = half4(AdjustedDualBlendAdd, AdjustedAlpha);
	#endif
#elif defined(MATERIAL_SHADINGMODEL_SINGLELAYERWATER)
	outColor = half4(outColor.rgb * FogColor.a + FogColor.rgb * outColor.a, outColor.a);
#else
	if (MATERIAL_BLEND_MODE == MATERIAL_BLEND_MODE_ALPHA_COMPOSITE)
	{
		outColor = half4(outColor.rgb * FogColor.a + FogColor.rgb * outColor.a, outColor.a);
	}
	else if (MATERIAL_BLEND_MODE == MATERIAL_BLEND_MODE_TRANSLUCENT)
	{
		outColor = half4(outColor.rgb * FogColor.a + FogColor.rgb, outColor.a);
	}
	else if (MATERIAL_BLEND_MODE == MATERIAL_BLEND_MODE_ADDITIVE)
	{
		outColor.rgb *= FogColor.a * outColor.a;
		outColor.a = 0;
	}
	else if (MATERIAL_BLEND_MODE == MATERIAL_BLEND_MODE_MODULATE)
	{
		half3 FoggedColor = lerp(half3(1, 1, 1), outColor.rgb, FogColor.aaa * FogColor.aaa);
		outColor = half4(FoggedColor, outColor.a);
	}
	else
	{
		// TRANSLUCENT Blend Mode
		// still seems some problem
		outColor.xyz = outColor.xyz * FogColor.a + FogColor.rgb;
		outColor.a = min(outColor.a, FogColor.a);
	}
#endif

	return StoreTransparentPSOutput(outColor, surfaceData.temporalReactive);
}
#endif

#endif