// #pragma enable debug_symbol
#pragma vertex VSMain
#pragma pixel PSMain

// #pragma keyword USE_VEGETATION_ANIM
// #pragma keyword QTANGENT
#pragma keyword CE_INSTANCING
#pragma keyword CE_USE_DOUBLE_TRANSFORM
#pragma keyword IS_PARTICLE_PASS
#pragma keyword IS_MESH_PARTICLE
#pragma keyword IS_GLOBAL_PARTICLE
// #pragma keyword TERRAIN_RENDERING
// #pragma keyword TERRAIN_USE_INSTANCING
// #pragma keyword TEXTURE_ARRAY_ENABLE

#if !IS_PARTICLE_PASS
	#define VERTEX_TYPE VertexType_Vegetation
	#include "../Material/Lit/SurfaceShaderIncludes.hlsl"
#else

#if IS_MESH_PARTICLE
	#define ENABLE_MESH_PARTICLE
#else
	#define ENABLE_SPRITE_PARTICLE
#endif

#if IS_GLOBAL_PARTICLE
	#define USED_WITH_GLOBAL_SPACE_PARTICLE
#else
	#define USED_WITH_LOCAL_SPACE_PARTICLE
#endif

#define CUSTOM_VS_OUTPUT 
#define CUSTOM_PS_INPUT
#define SET_CUSTOM_PS_INPUT
#include "../ShaderLibrary/MaterialEditor/VertexLayout.hlsl"
#include "../Material/Lit/Common/SceneData_MaterialEditor.hlsl"

float3 GetWorldPositionOffset(PrimitiveSceneData primitiveData, ObjectSceneData objectData, float3 positionWS, in VSOutput input, in VSInput vsInput)
{
    return positionWS;
}

void GenerateCustomInterpolators(PrimitiveSceneData primitiveData, ObjectSceneData objectData, float3 positionWS, inout VSOutput input, in VSInput vsInput)
{
}

#include "../ShaderLibrary/MaterialEditor/Vertex.hlsl"

#endif	// IS_PARTICLE_PASS

VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}

float4 PSMain(VSOutput input) : SV_TARGET
{
	return float4(1, 1, 1, 1);
}