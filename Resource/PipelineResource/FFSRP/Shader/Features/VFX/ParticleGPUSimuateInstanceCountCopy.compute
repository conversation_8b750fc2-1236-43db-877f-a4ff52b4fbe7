// #pragma enable debug_symbol
#pragma compute InstanceCountCopy
#include "Features/GPUScene/CullingSceneData.hlsl"
#define THREADS_PER_GROUP 64

StructuredBuffer<int> _PayloadDataOffsets;
StructuredBuffer<int> _PayloadDataOffsetsN;

StructuredBuffer<int> _GlobalInstanceCounterBufferOffsets;
StructuredBuffer<int> _ParticleGlobalInstanceCounterBuffer;

StructuredBuffer<int> _GlobalInstanceCounterBufferOffsetsN;
StructuredBuffer<int> _ParticleGlobalInstanceCounterBufferN;

RWStructuredBuffer<ObjectPayloadData2> _ObjectPayloadDatas;
cbuffer cbPass
{
    uint _GPUEmitterCount;
    uint _NGPUEmitterCount;
}

[numthreads(THREADS_PER_GROUP, 1, 1)]
void InstanceCountCopy(
    uint3 groupId          : SV_GroupID,
    uint3 groupThreadId    : SV_GroupThreadID,
    uint3 dispatchThreadId : SV_DispatchThreadID,
    uint  groupIndex       : SV_GroupIndex)
{
    uint globalThreadId = groupId.x * THREADS_PER_GROUP + groupIndex;
    if (globalThreadId < _GPUEmitterCount)
    {
        int payloadOffset = _PayloadDataOffsets[globalThreadId];
        int globalInstanceCounterBufferOffset = _GlobalInstanceCounterBufferOffsets[globalThreadId];
        _ObjectPayloadDatas[payloadOffset].objectCount = _ParticleGlobalInstanceCounterBuffer[globalInstanceCounterBufferOffset];
    }

    if (globalThreadId < _NGPUEmitterCount)
    {
        int payloadOffset = _PayloadDataOffsetsN[globalThreadId];
        int globalInstanceCounterBufferOffsetN = _GlobalInstanceCounterBufferOffsetsN[globalThreadId];
        _ObjectPayloadDatas[payloadOffset].objectCount = _ParticleGlobalInstanceCounterBufferN[globalInstanceCounterBufferOffsetN];
    }
}